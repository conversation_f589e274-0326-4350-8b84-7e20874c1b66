'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';

export default function AccountSettingsPage() {
  const { user, updateUserSettings } = useAuth();
  const [successMessage, setSuccessMessage] = useState('');
  const [error, setError] = useState('');

  const handleUpdateOnboarding = async () => {
    try {
      await updateUserSettings({
        showOnboarder: !user?.profile?.userSettings?.showOnboarder,
      });
      setSuccessMessage('Onboarding settings updated');
      setError('');
    } catch (err: any) {
      console.error('Failed to update onboarding settings:', err);
      setError(err.response?.data?.message || 'Failed to update settings. Please try again.');
      setSuccessMessage('');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Account Settings</h1>
          <Link 
            href="/settings"
            className="text-sm text-indigo-600 hover:text-indigo-800"
          >
            Back to Settings
          </Link>
        </div>

        {error && (
          <div className="bg-red-50 p-4 rounded-md mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {successMessage && (
          <div className="bg-green-50 p-4 rounded-md mb-6">
            <p className="text-green-700">{successMessage}</p>
          </div>
        )}

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Account Information</h2>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Email</p>
              <p className="mt-1">{user?.email}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Role</p>
              <p className="mt-1 capitalize">{user?.roles?.join(', ')}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Preferences</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-base font-medium text-gray-900">Onboarding Guide</h3>
                <p className="text-sm text-gray-500">
                  Show the onboarding guide to help you get started
                </p>
              </div>
              <button
                onClick={handleUpdateOnboarding}
                className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                  user?.profile?.userSettings?.showOnboarder ? 'bg-indigo-600' : 'bg-gray-200'
                }`}
              >
                <span className="sr-only">Toggle onboarding</span>
                <span
                  className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                    user?.profile?.userSettings?.showOnboarder ? 'translate-x-5' : 'translate-x-0'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
