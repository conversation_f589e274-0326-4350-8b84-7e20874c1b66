'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import Link from 'next/link';

interface PostType {
  _id: string;
  name: string;
  description?: string;
  recordTips?: string;
  companyId: string;
  archived: boolean;
}

export default function PostTypesSettingsPage() {
  const { user } = useAuth();
  const [postTypes, setPostTypes] = useState<PostType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    if (user?.profile?.companyId) {
      fetchPostTypes(user.profile.companyId);
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchPostTypes = async (companyId: string) => {
    try {
      setLoading(true);
      // Include archived post types in the admin view
      const { data } = await api.get(`${endpoints.postTypes.base}?companyId=${companyId}&includeArchived=true`);
      setPostTypes(data);
    } catch (err: any) {
      console.error('Failed to fetch post types:', err);
      setError('Failed to load post types. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleArchiveToggle = async (postType: PostType) => {
    try {
      const updatedPostType = { ...postType, archived: !postType.archived };
      await api.patch(endpoints.postTypes.byId(postType._id), {
        archived: updatedPostType.archived
      });
      
      setPostTypes(postTypes.map(pt => 
        pt._id === postType._id ? updatedPostType : pt
      ));
      
      setSuccessMessage(`Post type ${updatedPostType.archived ? 'archived' : 'unarchived'} successfully`);
      
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (err: any) {
      console.error('Failed to update post type:', err);
      setError(err.response?.data?.message || 'Failed to update post type. Please try again.');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-5xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Post Types</h1>
          <div className="flex space-x-4">
            <Link 
              href="/settings"
              className="text-sm text-indigo-600 hover:text-indigo-800"
            >
              Back to Settings
            </Link>
            {user?.roles?.includes('admin') && (
              <Link
                href="/settings/post-types/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Create Post Type
              </Link>
            )}
          </div>
        </div>

        {error && (
          <div className="bg-red-50 p-4 rounded-md mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {successMessage && (
          <div className="bg-green-50 p-4 rounded-md mb-6">
            <p className="text-green-700">{successMessage}</p>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {postTypes.length > 0 ? (
                postTypes.map((postType) => (
                  <li key={postType._id} className={postType.archived ? 'bg-gray-50' : ''}>
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-900">{postType.name}</span>
                            {postType.archived && (
                              <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                Archived
                              </span>
                            )}
                          </div>
                          {postType.description && (
                            <div className="text-sm text-gray-500" dangerouslySetInnerHTML={{ __html: postType.description }} />
                          )}
                        </div>
                        {user?.roles?.includes('admin') && (
                          <div className="flex items-center space-x-4">
                            <Link
                              href={`/settings/post-types/${postType._id}`}
                              className="text-sm text-indigo-600 hover:text-indigo-900"
                            >
                              Edit
                            </Link>
                            <button
                              onClick={() => handleArchiveToggle(postType)}
                              className={`text-sm ${postType.archived ? 'text-green-600 hover:text-green-900' : 'text-gray-600 hover:text-gray-900'}`}
                            >
                              {postType.archived ? 'Unarchive' : 'Archive'}
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </li>
                ))
              ) : (
                <li className="px-4 py-5 sm:px-6">
                  <div className="text-center text-gray-500">
                    No post types found
                  </div>
                </li>
              )}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
