import { Document, Schema as MongooseSchema } from 'mongoose';
export type ProfileDocument = Profile & Document;
export declare class Profile {
    firstName: string;
    lastName: string;
    role: string;
    companyId: MongooseSchema.Types.ObjectId;
    avatar: string;
    transformedAvatar: {
        small: string;
        medium: string;
        large: string;
    };
    hashtags: string[];
    userSettings: {
        showOnboarder: boolean;
        onboarderStep: number;
        [key: string]: any;
    };
    userId: MongooseSchema.Types.ObjectId;
}
export declare const ProfileSchema: MongooseSchema<Profile, import("mongoose").Model<Profile, any, any, any, Document<unknown, any, Profile> & Profile & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Profile, Document<unknown, {}, import("mongoose").FlatRecord<Profile>> & import("mongoose").FlatRecord<Profile> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
