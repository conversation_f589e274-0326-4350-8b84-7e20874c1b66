import { AiService } from './ai.service';
import { CreateSummaryDto } from './dto/create-summary.dto';
import { CreateInsightDto } from './dto/create-insight.dto';
import { CreateAiPromptDto } from './dto/create-ai-prompt.dto';
import { CreateAiChatDto } from './dto/create-ai-chat.dto';
import { CreateAppSettingsDto } from './dto/create-app-settings.dto';
import { TranscribeAudioDto } from './dto/transcribe-audio.dto';
export declare class AiController {
    private readonly aiService;
    constructor(aiService: AiService);
    createSummary(createSummaryDto: CreateSummaryDto): Promise<import("./schemas/summary.schema").SummaryDocument>;
    findAllSummaries(userId?: string, companyId?: string, tag?: string): Promise<import("./schemas/summary.schema").SummaryDocument[]>;
    findSummaryById(id: string): Promise<import("./schemas/summary.schema").SummaryDocument>;
    updateSummary(id: string, updateSummaryDto: any): Promise<import("./schemas/summary.schema").SummaryDocument>;
    removeSummary(id: string): Promise<import("./schemas/summary.schema").SummaryDocument>;
    generateSummary(createSummaryDto: CreateSummaryDto): Promise<import("./schemas/summary.schema").SummaryDocument>;
    createInsight(createInsightDto: CreateInsightDto): Promise<import("./schemas/insight.schema").InsightDocument>;
    findAllInsights(userId?: string, companyId?: string, tag?: string): Promise<import("./schemas/insight.schema").InsightDocument[]>;
    findInsightById(id: string): Promise<import("./schemas/insight.schema").InsightDocument>;
    updateInsight(id: string, updateInsightDto: any): Promise<import("./schemas/insight.schema").InsightDocument>;
    removeInsight(id: string): Promise<import("./schemas/insight.schema").InsightDocument>;
    analyzeInsight(createInsightDto: CreateInsightDto): Promise<import("./schemas/insight.schema").InsightDocument>;
    createAiPrompt(createAiPromptDto: CreateAiPromptDto): Promise<import("./schemas/ai-prompt.schema").AiPromptDocument>;
    findAllAiPrompts(companyId?: string, isSystem?: boolean): Promise<import("./schemas/ai-prompt.schema").AiPromptDocument[]>;
    findAiPromptById(id: string): Promise<import("./schemas/ai-prompt.schema").AiPromptDocument>;
    updateAiPrompt(id: string, updateAiPromptDto: any): Promise<import("./schemas/ai-prompt.schema").AiPromptDocument>;
    removeAiPrompt(id: string): Promise<import("./schemas/ai-prompt.schema").AiPromptDocument>;
    createAiChat(createAiChatDto: CreateAiChatDto): Promise<import("./schemas/ai-chat.schema").AiChatDocument>;
    findAllAiChats(userId: string): Promise<import("./schemas/ai-chat.schema").AiChatDocument[]>;
    findAiChatById(id: string): Promise<import("./schemas/ai-chat.schema").AiChatDocument>;
    updateAiChat(id: string, updateAiChatDto: any): Promise<import("./schemas/ai-chat.schema").AiChatDocument>;
    removeAiChat(id: string): Promise<import("./schemas/ai-chat.schema").AiChatDocument>;
    sendChatMessage(id: string, message: string, userId: string): Promise<import("./schemas/ai-chat.schema").AiChatDocument>;
    createAppSettings(createAppSettingsDto: CreateAppSettingsDto): Promise<import("./schemas/app-settings.schema").AppSettingsDocument>;
    findAllAppSettings(type?: string, companyId?: string): Promise<import("./schemas/app-settings.schema").AppSettingsDocument[]>;
    findAppSettingsById(id: string): Promise<import("./schemas/app-settings.schema").AppSettingsDocument>;
    updateAppSettings(id: string, updateAppSettingsDto: any): Promise<import("./schemas/app-settings.schema").AppSettingsDocument>;
    removeAppSettings(id: string): Promise<import("./schemas/app-settings.schema").AppSettingsDocument>;
    transcribeAudio(transcribeAudioDto: TranscribeAudioDto): Promise<{
        text: string;
    }>;
}
