'use client';

import React from 'react';

interface PostItemProps {
  post: {
    _id: string;
    body: string;
    enhancedBody?: string;
    userId: string;
    createdAt: string;
    hashtags: string[];
    user?: {
      firstName?: string;
      lastName?: string;
      avatar?: string;
    };
    teamId?: string;
    team?: {
      name: string;
    };
    postTypeId?: string;
    postType?: {
      name: string;
    };
    audioUrl?: string;
  };
  onTagClick?: (tag: string) => void;
}

export default function PostItem({ post, onTagClick }: PostItemProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center mb-4">
        <div className="h-10 w-10 rounded-full bg-gray-200 flex-shrink-0 flex items-center justify-center">
          {post.user?.avatar ? (
            <img
              src={post.user.avatar}
              alt={`${post.user.firstName} ${post.user.lastName}`}
              className="h-10 w-10 rounded-full object-cover"
            />
          ) : (
            <span className="text-gray-500 text-sm font-medium">
              {post.user?.firstName?.[0] || ''}
              {post.user?.lastName?.[0] || ''}
            </span>
          )}
        </div>
        <div className="ml-3">
          <p className="font-medium">
            {post.user?.firstName} {post.user?.lastName}
          </p>
          <div className="flex items-center space-x-2">
            <p className="text-sm text-gray-500">
              {new Date(post.createdAt).toLocaleDateString()}
            </p>
            {post.team && (
              <span className="text-sm text-gray-500">
                • Team: {post.team.name}
              </span>
            )}
            {post.postType && (
              <span className="text-sm text-gray-500">
                • Type: {post.postType.name}
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="prose max-w-none">
        {post.enhancedBody ? (
          <div dangerouslySetInnerHTML={{ __html: post.enhancedBody }} />
        ) : (
          <p>{post.body}</p>
        )}
      </div>

      {post.audioUrl && (
        <div className="mt-4">
          <audio controls src={post.audioUrl} className="w-full" />
        </div>
      )}

      {post.hashtags && post.hashtags.length > 0 && (
        <div className="mt-4 flex flex-wrap gap-2">
          {post.hashtags.map((tag) => (
            <button
              key={tag}
              onClick={() => onTagClick && onTagClick(tag)}
              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 hover:bg-indigo-200"
            >
              #{tag}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
