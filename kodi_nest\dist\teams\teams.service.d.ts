import { Model } from 'mongoose';
import { TeamDocument } from './schemas/team.schema';
import { MembershipDocument } from './schemas/membership.schema';
import { CreateTeamDto } from './dto/create-team.dto';
import { UpdateTeamDto } from './dto/update-team.dto';
import { CreateMembershipDto } from './dto/create-membership.dto';
export declare class TeamsService {
    private teamModel;
    private membershipModel;
    constructor(teamModel: Model<TeamDocument>, membershipModel: Model<MembershipDocument>);
    create(createTeamDto: CreateTeamDto): Promise<TeamDocument>;
    findAll(): Promise<TeamDocument[]>;
    findByCompany(companyId: string): Promise<TeamDocument[]>;
    findById(id: string): Promise<TeamDocument>;
    update(id: string, updateTeamDto: UpdateTeamDto): Promise<TeamDocument>;
    remove(id: string): Promise<TeamDocument>;
    getTeamMembers(teamId: string): Promise<MembershipDocument[]>;
    getUserTeams(userId: string, companyId?: string): Promise<TeamDocument[]>;
    addMember(createMembershipDto: CreateMembershipDto): Promise<MembershipDocument>;
    removeMember(teamId: string, memberId: string): Promise<void>;
    isMember(teamId: string, userId: string): Promise<boolean>;
}
