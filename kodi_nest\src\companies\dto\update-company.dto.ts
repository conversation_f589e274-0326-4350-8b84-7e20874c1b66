import { IsOptional, IsString, IsNumber, IsObject, IsArray } from 'class-validator';
import { PartialType } from '@nestjs/mapped-types';
import { CreateCompanyDto } from './create-company.dto';
import { CompanyAccountType } from '../schemas/company.schema';

export class UpdateCompanyDto extends PartialType(CreateCompanyDto) {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsNumber()
  accountType?: CompanyAccountType;

  @IsOptional()
  @IsString()
  logo?: string;

  @IsOptional()
  @IsObject()
  transformedLogo?: {
    small: string;
    medium: string;
    large: string;
  };

  @IsOptional()
  @IsArray()
  hashtags?: string[];

  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;
}
