import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import PostItem from './PostItem';

describe('PostItem', () => {
  const mockPost = {
    _id: 'post-1',
    body: 'This is a test post with #hashtag',
    userId: 'user-1',
    createdAt: '2023-01-01T12:00:00Z',
    hashtags: ['hashtag'],
    user: {
      firstName: 'John',
      lastName: 'Doe',
    },
  };

  it('renders the post with basic information', () => {
    render(<PostItem post={mockPost} />);

    // Check for user name
    expect(screen.getByText('<PERSON>')).toBeInTheDocument();

    // Check for post body
    expect(screen.getByText('This is a test post with #hashtag')).toBeInTheDocument();

    // Check for date
    expect(screen.getByText('1/1/2023')).toBeInTheDocument();

    // Check for hashtag
    expect(screen.getByRole('button', { name: '#hashtag' })).toBeInTheDocument();
  });

  it('renders user initials when avatar is not available', () => {
    render(<PostItem post={mockPost} />);

    // Check for user initials
    expect(screen.getByText('JD')).toBeInTheDocument();
  });

  it('renders avatar when available', () => {
    const postWithAvatar = {
      ...mockPost,
      user: {
        ...mockPost.user,
        avatar: 'https://example.com/avatar.jpg',
      },
    };

    render(<PostItem post={postWithAvatar} />);

    // Check for avatar image
    const avatar = screen.getByAltText('John Doe');
    expect(avatar).toBeInTheDocument();
    expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg');
  });

  it('renders team information when available', () => {
    const postWithTeam = {
      ...mockPost,
      teamId: 'team-1',
      team: {
        name: 'Engineering',
      },
    };

    render(<PostItem post={postWithTeam} />);

    // Check for team name
    expect(screen.getByText('• Team: Engineering')).toBeInTheDocument();
  });

  it('renders post type information when available', () => {
    const postWithType = {
      ...mockPost,
      postTypeId: 'type-1',
      postType: {
        name: 'Strategic feedback',
      },
    };

    render(<PostItem post={postWithType} />);

    // Check for post type name
    expect(screen.getByText('• Type: Strategic feedback')).toBeInTheDocument();
  });

  it('renders enhanced body when available', () => {
    const postWithEnhancedBody = {
      ...mockPost,
      enhancedBody: '<p>This is <strong>enhanced</strong> content</p>',
    };

    render(<PostItem post={postWithEnhancedBody} />);

    // Check for enhanced content
    const enhancedContent = screen.getByText('This is enhanced content');
    expect(enhancedContent).toBeInTheDocument();

    // Check that the strong tag is rendered
    const strongElement = enhancedContent.querySelector('strong');
    expect(strongElement).toBeInTheDocument();
  });

  it('renders audio player when audio URL is available', () => {
    const postWithAudio = {
      ...mockPost,
      audioUrl: 'https://example.com/audio.webm',
    };

    render(<PostItem post={postWithAudio} />);

    // Check for audio element
    const audioElement = screen.getByRole('audio');
    expect(audioElement).toBeInTheDocument();
    expect(audioElement).toHaveAttribute('src', 'https://example.com/audio.webm');
  });

  it('handles missing user information gracefully', () => {
    const postWithoutUser = {
      ...mockPost,
      user: undefined,
    };

    render(<PostItem post={postWithoutUser} />);

    // Check that it doesn't crash and renders the post body
    expect(screen.getByText('This is a test post with #hashtag')).toBeInTheDocument();
  });

  it('calls onTagClick when a hashtag is clicked', async () => {
    const mockOnTagClick = jest.fn();
    const user = userEvent.setup();

    render(<PostItem post={mockPost} onTagClick={mockOnTagClick} />);

    // Click on the hashtag
    await user.click(screen.getByRole('button', { name: '#hashtag' }));

    // Check that onTagClick was called with the correct tag
    expect(mockOnTagClick).toHaveBeenCalledWith('hashtag');
  });
});
