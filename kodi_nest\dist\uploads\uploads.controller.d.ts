import { UploadsService } from './uploads.service';
export declare class UploadsController {
    private readonly uploadsService;
    constructor(uploadsService: UploadsService);
    generatePresignedUrl(fileType: string, isPrivate?: boolean): Promise<{
        uploadUrl: string;
        key: string;
        url: string;
    }>;
    generateAvatarUploadUrl(fileType: string): Promise<{
        uploadUrl: string;
        key: string;
        url: string;
    }>;
    generateLogoUploadUrl(fileType: string): Promise<{
        uploadUrl: string;
        key: string;
        url: string;
    }>;
    generateAudioUploadUrl(fileType: string): Promise<{
        uploadUrl: string;
        key: string;
        url: string;
    }>;
    uploadAudio(file: Express.Multer.File): {
        url: string;
        filename: string;
        originalname: string;
        mimetype: string;
        size: number;
    };
    generateDocumentUploadUrl(fileType: string): Promise<{
        uploadUrl: string;
        key: string;
        url: string;
    }>;
}
