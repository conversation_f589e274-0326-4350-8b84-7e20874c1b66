/**
 * Web implementation of expo-av
 * This provides a browser-based implementation for web platforms
 */

import { EventEmitter } from 'events';

// Define the interfaces to match the native AV API
export enum AVPlaybackStatus {
  Idle = 'IDLE',
  Loading = 'LOADING',
  Buffering = 'BUFFERING',
  Playing = 'PLAYING',
  Paused = 'PAUSED',
  Stopped = 'STOPPED',
  Error = 'ERROR',
}

export interface AVPlaybackStatusSuccess {
  isLoaded: boolean;
  uri?: string;
  progressUpdateIntervalMillis?: number;
  durationMillis?: number;
  positionMillis?: number;
  playableDurationMillis?: number;
  seekMillisToleranceBefore?: number;
  seekMillisToleranceAfter?: number;
  shouldPlay?: boolean;
  isPlaying?: boolean;
  isBuffering?: boolean;
  rate?: number;
  shouldCorrectPitch?: boolean;
  volume?: number;
  isMuted?: boolean;
  isLooping?: boolean;
  didJustFinish?: boolean;
}

export interface AVPlaybackStatusError {
  isLoaded: false;
  error: string;
}

export type AVPlaybackStatusToSet = {
  shouldPlay?: boolean;
  positionMillis?: number;
  rate?: number;
  shouldCorrectPitch?: boolean;
  volume?: number;
  isMuted?: boolean;
  isLooping?: boolean;
  progressUpdateIntervalMillis?: number;
};

// Audio implementation for web
class Audio extends EventEmitter {
  private audio: HTMLAudioElement | null = null;
  private status: AVPlaybackStatusSuccess = {
    isLoaded: false,
    shouldPlay: false,
    isPlaying: false,
    isBuffering: false,
    rate: 1.0,
    shouldCorrectPitch: false,
    volume: 1.0,
    isMuted: false,
    isLooping: false,
    didJustFinish: false,
    positionMillis: 0,
    durationMillis: 0,
    progressUpdateIntervalMillis: 500,
  };
  private progressUpdateInterval: NodeJS.Timeout | null = null;

  constructor() {
    super();
    this.audio = new Audio();
    this.setupAudioListeners();
  }

  private setupAudioListeners() {
    if (!this.audio) return;

    this.audio.addEventListener('loadedmetadata', () => {
      if (!this.audio) return;
      this.status.isLoaded = true;
      this.status.durationMillis = this.audio.duration * 1000;
      this.emit('loadedmetadata', this.getStatusAsync());
    });

    this.audio.addEventListener('play', () => {
      this.status.isPlaying = true;
      this.emit('play', this.getStatusAsync());
    });

    this.audio.addEventListener('pause', () => {
      this.status.isPlaying = false;
      this.emit('pause', this.getStatusAsync());
    });

    this.audio.addEventListener('ended', () => {
      this.status.didJustFinish = true;
      this.status.isPlaying = false;
      this.status.positionMillis = this.status.durationMillis || 0;
      this.emit('ended', this.getStatusAsync());
    });

    this.audio.addEventListener('error', (e) => {
      this.emit('error', {
        isLoaded: false,
        error: `Audio error: ${e}`,
      });
    });

    this.audio.addEventListener('waiting', () => {
      this.status.isBuffering = true;
      this.emit('waiting', this.getStatusAsync());
    });

    this.audio.addEventListener('canplay', () => {
      this.status.isBuffering = false;
      this.emit('canplay', this.getStatusAsync());
    });

    this.audio.addEventListener('timeupdate', () => {
      if (!this.audio) return;
      this.status.positionMillis = this.audio.currentTime * 1000;
    });
  }

  async loadAsync(source: { uri: string }, initialStatus: AVPlaybackStatusToSet = {}, downloadFirst = false): Promise<AVPlaybackStatusSuccess> {
    if (!this.audio) return this.status;

    try {
      this.audio.src = source.uri;
      this.audio.load();
      this.status.uri = source.uri;

      // Apply initial status
      await this.setStatusAsync(initialStatus);

      return this.getStatusAsync();
    } catch (error) {
      throw new Error(`Failed to load audio: ${error}`);
    }
  }

  async unloadAsync(): Promise<AVPlaybackStatusSuccess> {
    if (this.audio) {
      this.stopProgressUpdates();
      this.audio.pause();
      this.audio.src = '';
      this.status = {
        isLoaded: false,
        shouldPlay: false,
        isPlaying: false,
        isBuffering: false,
        rate: 1.0,
        shouldCorrectPitch: false,
        volume: 1.0,
        isMuted: false,
        isLooping: false,
        didJustFinish: false,
        positionMillis: 0,
        durationMillis: 0,
        progressUpdateIntervalMillis: 500,
      };
    }
    return this.status;
  }

  getStatusAsync(): AVPlaybackStatusSuccess {
    if (!this.audio) return this.status;

    // Update current position
    this.status.positionMillis = this.audio.currentTime * 1000;
    return { ...this.status };
  }

  async setStatusAsync(status: AVPlaybackStatusToSet): Promise<AVPlaybackStatusSuccess> {
    if (!this.audio) return this.status;

    // Apply all the properties from the status
    if (status.volume !== undefined) {
      this.audio.volume = status.volume;
      this.status.volume = status.volume;
    }

    if (status.isMuted !== undefined) {
      this.audio.muted = status.isMuted;
      this.status.isMuted = status.isMuted;
    }

    if (status.isLooping !== undefined) {
      this.audio.loop = status.isLooping;
      this.status.isLooping = status.isLooping;
    }

    if (status.rate !== undefined) {
      this.audio.playbackRate = status.rate;
      this.status.rate = status.rate;
    }

    if (status.positionMillis !== undefined) {
      this.audio.currentTime = status.positionMillis / 1000;
      this.status.positionMillis = status.positionMillis;
    }

    if (status.progressUpdateIntervalMillis !== undefined) {
      this.status.progressUpdateIntervalMillis = status.progressUpdateIntervalMillis;
      this.setupProgressUpdates();
    }

    if (status.shouldPlay !== undefined) {
      this.status.shouldPlay = status.shouldPlay;
      if (status.shouldPlay) {
        try {
          await this.audio.play();
          this.status.isPlaying = true;
          this.setupProgressUpdates();
        } catch (error) {
          console.error('Error playing audio:', error);
        }
      } else {
        this.audio.pause();
        this.status.isPlaying = false;
      }
    }

    return this.getStatusAsync();
  }

  async playAsync(): Promise<AVPlaybackStatusSuccess> {
    return this.setStatusAsync({ shouldPlay: true });
  }

  async pauseAsync(): Promise<AVPlaybackStatusSuccess> {
    return this.setStatusAsync({ shouldPlay: false });
  }

  async stopAsync(): Promise<AVPlaybackStatusSuccess> {
    await this.pauseAsync();
    return this.setStatusAsync({ positionMillis: 0 });
  }

  async setPositionAsync(positionMillis: number): Promise<AVPlaybackStatusSuccess> {
    return this.setStatusAsync({ positionMillis });
  }

  private setupProgressUpdates() {
    this.stopProgressUpdates();
    
    if (this.status.isPlaying && this.status.progressUpdateIntervalMillis) {
      this.progressUpdateInterval = setInterval(() => {
        this.emit('progressUpdate', this.getStatusAsync());
      }, this.status.progressUpdateIntervalMillis);
    }
  }

  private stopProgressUpdates() {
    if (this.progressUpdateInterval) {
      clearInterval(this.progressUpdateInterval);
      this.progressUpdateInterval = null;
    }
  }
}

// Create the web implementation
const ExpoAV = {
  Audio: {
    Sound: Audio,
    setAudioModeAsync: async (mode: any) => {
      console.log('[Web] Setting audio mode:', mode);
      return true;
    },
  },
  Video: {
    // Basic stub for Video component - would need to be expanded for full functionality
  },
};

export default ExpoAV;
