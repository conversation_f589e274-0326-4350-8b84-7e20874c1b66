'use client';

import React, { useState, useRef } from 'react';
import api, { endpoints } from '@/lib/api';

interface AudioRecorderProps {
  onAudioRecorded: (audioUrl: string, transcription: string) => void;
  postTypeId?: string;
  recordTips?: string;
}

export default function AudioRecorder({ onAudioRecorded, postTypeId, recordTips }: AudioRecorderProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const [recordingTime, setRecordingTime] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [showTips, setShowTips] = useState(false);

  const startRecording = async () => {
    try {
      setError('');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        setAudioBlob(audioBlob);

        // Stop all tracks to release the microphone
        stream.getTracks().forEach(track => track.stop());

        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
      };

      mediaRecorder.start();
      setIsRecording(true);

      // Start timer
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } catch (err) {
      console.error('Error accessing microphone:', err);
      setError('Could not access microphone. Please check your browser permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const processAudio = async () => {
    if (!audioBlob) return;

    setIsProcessing(true);
    setError('');

    try {
      // First, upload the audio file
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.webm');

      const uploadResponse = await api.post(endpoints.uploads.audioUpload, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const audioUrl = uploadResponse.data.url;

      // Then, transcribe the audio
      const transcribeResponse = await api.post(endpoints.ai.transcribe, {
        audioUrl,
        postTypeId,
      });

      const transcription = transcribeResponse.data.text;

      // Pass the audio URL and transcription back to the parent component
      onAudioRecorded(audioUrl, transcription);

      // Reset state
      setAudioBlob(null);
    } catch (err: any) {
      console.error('Error processing audio:', err);
      setError(err.response?.data?.message || 'Failed to process audio. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const cancelRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
    setAudioBlob(null);
  };

  return (
    <div className="mb-4">
      {recordTips && (
        <div className="mb-2">
          <button
            type="button"
            onClick={() => setShowTips(!showTips)}
            className="text-sm text-indigo-600 hover:text-indigo-800"
          >
            {showTips ? 'Hide recording tips' : 'Show recording tips'}
          </button>

          {showTips && (
            <div
              className="mt-2 p-3 bg-gray-50 rounded-md text-sm"
              dangerouslySetInnerHTML={{ __html: recordTips }}
            />
          )}
        </div>
      )}

      {error && (
        <div className="bg-red-50 p-3 rounded-md mb-3">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      <div className="flex items-center space-x-3">
        {!isRecording && !audioBlob && (
          <button
            type="button"
            onClick={startRecording}
            className="flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
            </svg>
            Record Audio
          </button>
        )}

        {isRecording && (
          <>
            <div className="flex items-center">
              <div className="animate-pulse h-3 w-3 bg-red-600 rounded-full mr-2"></div>
              <span className="text-red-600 font-medium">Recording... {formatTime(recordingTime)}</span>
            </div>
            <button
              type="button"
              onClick={stopRecording}
              className="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Stop
            </button>
          </>
        )}

        {audioBlob && !isProcessing && (
          <>
            <audio controls src={URL.createObjectURL(audioBlob)} className="h-10" />
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={processAudio}
                className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              >
                Use Recording
              </button>
              <button
                type="button"
                onClick={cancelRecording}
                className="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Cancel
              </button>
            </div>
          </>
        )}

        {isProcessing && (
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-indigo-500 mr-2"></div>
            <span>Processing audio...</span>
          </div>
        )}
      </div>
    </div>
  );
}
