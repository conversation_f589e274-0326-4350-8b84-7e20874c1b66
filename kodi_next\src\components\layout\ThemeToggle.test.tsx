import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ThemeToggle from './ThemeToggle';
import { ThemeProvider } from '@/contexts/ThemeContext';

// Mock the ThemeContext
jest.mock('@/contexts/ThemeContext', () => {
  const originalModule = jest.requireActual('@/contexts/ThemeContext');
  
  return {
    ...originalModule,
    useTheme: jest.fn(() => ({
      resolvedTheme: 'light',
      setTheme: jest.fn(),
    })),
  };
});

describe('ThemeToggle', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the theme toggle button', () => {
    render(
      <ThemeProvider>
        <ThemeToggle />
      </ThemeProvider>
    );
    
    // Check that the button is rendered
    const button = screen.getByRole('button', { name: /Switch to dark theme/i });
    expect(button).toBeInTheDocument();
    
    // Check that the moon icon is rendered for light mode
    const moonIcon = button.querySelector('svg');
    expect(moonIcon).toBeInTheDocument();
  });

  it('toggles the theme when clicked', async () => {
    // Mock the useTheme hook to return light theme
    const mockSetTheme = jest.fn();
    require('@/contexts/ThemeContext').useTheme.mockReturnValue({
      resolvedTheme: 'light',
      setTheme: mockSetTheme,
    });
    
    render(
      <ThemeProvider>
        <ThemeToggle />
      </ThemeProvider>
    );
    
    // Click the button
    const user = userEvent.setup();
    await user.click(screen.getByRole('button'));
    
    // Check that setTheme was called with 'dark'
    expect(mockSetTheme).toHaveBeenCalledWith('dark');
  });

  it('shows sun icon in dark mode', () => {
    // Mock the useTheme hook to return dark theme
    require('@/contexts/ThemeContext').useTheme.mockReturnValue({
      resolvedTheme: 'dark',
      setTheme: jest.fn(),
    });
    
    render(
      <ThemeProvider>
        <ThemeToggle />
      </ThemeProvider>
    );
    
    // Check that the button is rendered with correct aria-label
    const button = screen.getByRole('button', { name: /Switch to light theme/i });
    expect(button).toBeInTheDocument();
    
    // Check that the sun icon is rendered for dark mode
    const sunIcon = button.querySelector('svg');
    expect(sunIcon).toBeInTheDocument();
  });
});
