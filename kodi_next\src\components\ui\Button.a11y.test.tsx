import React from 'react';
import { render } from '@testing-library/react';
import { axe, runAxe } from '@/test-utils/axe-setup';
import Button from './Button';

describe('Button Accessibility', () => {
  it('should not have accessibility violations', async () => {
    const { container } = render(<Button>Test Button</Button>);
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });

  it('should not have accessibility violations when disabled', async () => {
    const { container } = render(<Button disabled>Disabled Button</Button>);
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });

  it('should not have accessibility violations when loading', async () => {
    const { container } = render(<Button isLoading>Loading Button</Button>);
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });

  it('should not have accessibility violations with different variants', async () => {
    const { container: primaryContainer } = render(<Button variant="primary">Primary Button</Button>);
    const primaryResults = await runAxe(primaryContainer);
    expect(primaryResults).toHaveNoViolations();

    const { container: secondaryContainer } = render(<Button variant="secondary">Secondary Button</Button>);
    const secondaryResults = await runAxe(secondaryContainer);
    expect(secondaryResults).toHaveNoViolations();

    const { container: dangerContainer } = render(<Button variant="danger">Danger Button</Button>);
    const dangerResults = await runAxe(dangerContainer);
    expect(dangerResults).toHaveNoViolations();

    const { container: outlineContainer } = render(<Button variant="outline">Outline Button</Button>);
    const outlineResults = await runAxe(outlineContainer);
    expect(outlineResults).toHaveNoViolations();
  });

  it('should not have accessibility violations with icons', async () => {
    const mockIcon = <svg aria-hidden="true" width="16" height="16" viewBox="0 0 16 16" fill="currentColor" />;
    
    const { container } = render(
      <Button leftIcon={mockIcon} rightIcon={mockIcon}>
        Button with Icons
      </Button>
    );
    
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });
});
