"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EmailService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const common_1 = require("@nestjs/common");
const AWS = require("aws-sdk");
const nodemailer = require("nodemailer");
let EmailService = EmailService_1 = class EmailService {
    logger = new common_1.Logger(EmailService_1.name);
    ses;
    transporter;
    fromName;
    fromAddress;
    provider;
    constructor() {
        this.fromName = process.env.MAIL_FROM_NAME || 'Kodi Support';
        this.fromAddress = process.env.MAIL_FROM_ADDRESS || '<EMAIL>';
        this.provider = (process.env.MAIL_PROVIDER || 'ses');
        if (this.provider === 'ses') {
            this.ses = new AWS.SES({
                accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
                region: process.env.AWS_REGION,
            });
        }
        else {
            this.transporter = nodemailer.createTransport({
                host: process.env.MAIL_HOST,
                port: parseInt(process.env.MAIL_PORT || '587', 10),
                secure: process.env.MAIL_SECURE === 'true',
                auth: {
                    user: process.env.MAIL_USERNAME,
                    pass: process.env.MAIL_PASSWORD,
                },
            });
        }
    }
    async sendEmail(options) {
        const { to, subject, text, html } = options;
        try {
            if (this.provider === 'ses') {
                const params = {
                    Source: `"${this.fromName}" <${this.fromAddress}>`,
                    Destination: {
                        ToAddresses: Array.isArray(to) ? to : [to],
                    },
                    Message: {
                        Subject: {
                            Data: subject,
                        },
                        Body: {
                            ...(text && {
                                Text: {
                                    Data: text,
                                },
                            }),
                            ...(html && {
                                Html: {
                                    Data: html,
                                },
                            }),
                        },
                    },
                };
                const result = await this.ses.sendEmail(params).promise();
                this.logger.log(`Email sent successfully via SES: ${result.MessageId}`);
                return true;
            }
            else {
                const mailOptions = {
                    from: `"${this.fromName}" <${this.fromAddress}>`,
                    to: Array.isArray(to) ? to.join(',') : to,
                    subject,
                    ...(text && { text }),
                    ...(html && { html }),
                };
                const info = await this.transporter.sendMail(mailOptions);
                this.logger.log(`Email sent successfully via SMTP: ${info.messageId}`);
                return true;
            }
        }
        catch (error) {
            this.logger.error(`Failed to send email: ${error.message}`, error.stack);
            return false;
        }
    }
    async sendVerificationEmail(email, token) {
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3010';
        const verificationLink = `${frontendUrl}/verify-email?token=${token}`;
        return this.sendEmail({
            to: email,
            subject: 'Verify Your Email Address',
            html: `
        <h1>Welcome to Kodi!</h1>
        <p>Thank you for signing up. Please verify your email address by clicking the link below:</p>
        <p><a href="${verificationLink}">Verify Email Address</a></p>
        <p>If you did not create an account, please ignore this email.</p>
        <p>This link will expire in 24 hours.</p>
      `,
        });
    }
    async sendPasswordResetEmail(email, token) {
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3010';
        const resetLink = `${frontendUrl}/reset-password?token=${token}`;
        return this.sendEmail({
            to: email,
            subject: 'Reset Your Password',
            html: `
        <h1>Reset Your Password</h1>
        <p>You requested to reset your password. Please click the link below to set a new password:</p>
        <p><a href="${resetLink}">Reset Password</a></p>
        <p>If you did not request a password reset, please ignore this email.</p>
        <p>This link will expire in 1 hour.</p>
      `,
        });
    }
    async sendWelcomeEmail(email, firstName) {
        const name = firstName || 'there';
        return this.sendEmail({
            to: email,
            subject: 'Welcome to Kodi!',
            html: `
        <h1>Welcome to Kodi, ${name}!</h1>
        <p>Thank you for joining Kodi. We're excited to have you on board!</p>
        <p>With Kodi, you can:</p>
        <ul>
          <li>Create voice-based posts with AI-powered insights</li>
          <li>Collaborate with your team</li>
          <li>Get valuable feedback and analysis</li>
        </ul>
        <p>If you have any questions, feel free to reach out to our support team.</p>
      `,
        });
    }
    async sendInviteEmail(email, companyName, inviteCode) {
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3010';
        const inviteLink = `${frontendUrl}/register?inviteCode=${inviteCode}`;
        return this.sendEmail({
            to: email,
            subject: `You've been invited to join ${companyName} on Kodi`,
            html: `
        <h1>You've Been Invited!</h1>
        <p>You've been invited to join <strong>${companyName}</strong> on Kodi.</p>
        <p>Click the link below to accept the invitation and create your account:</p>
        <p><a href="${inviteLink}">Accept Invitation</a></p>
        <p>If you have any questions, please contact the person who invited you.</p>
      `,
        });
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = EmailService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], EmailService);
//# sourceMappingURL=email.service.js.map