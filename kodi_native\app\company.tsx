import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Image,
  Share,
  Clipboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Button from '@/src/components/ui/Button';
import Card from '@/src/components/ui/Card';
import Input from '@/src/components/ui/Input';

export default function CompanyScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [company, setCompany] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [name, setName] = useState('');
  const [inviteCode, setInviteCode] = useState('');
  const [logo, setLogo] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  
  useEffect(() => {
    fetchCompanyData();
  }, []);
  
  const fetchCompanyData = async () => {
    if (!user?.profile?.companyId) {
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      const { data } = await api.get(endpoints.companies.byId(user.profile.companyId));
      setCompany(data);
      setName(data.name);
      setInviteCode(data.invite_code);
      setLogo(data.logo);
    } catch (err) {
      console.error('Failed to fetch company data:', err);
      setError('Failed to load company data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  const handleGoBack = () => {
    router.back();
  };
  
  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Please grant camera roll permissions to change your company logo.');
      return;
    }
    
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });
    
    if (!result.canceled) {
      uploadLogo(result.assets[0].uri);
    }
  };
  
  const uploadLogo = async (uri: string) => {
    try {
      setIsSaving(true);
      
      const formData = new FormData();
      formData.append('logo', {
        uri,
        type: 'image/jpeg',
        name: 'logo.jpg',
      } as any);
      
      const response = await api.post(endpoints.uploads.logo, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      setLogo(response.data.url);
      
      // Update company with new logo
      await api.patch(endpoints.companies.byId(company._id), {
        logo: response.data.url,
      });
      
      Alert.alert('Success', 'Company logo updated successfully');
    } catch (err) {
      console.error('Failed to upload logo:', err);
      Alert.alert('Error', 'Failed to upload logo. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };
  
  const saveCompany = async () => {
    if (!name.trim()) {
      setError('Company name is required');
      return;
    }
    
    try {
      setIsSaving(true);
      setError('');
      
      await api.patch(endpoints.companies.byId(company._id), {
        name,
      });
      
      Alert.alert('Success', 'Company information updated successfully');
    } catch (err: any) {
      console.error('Failed to update company:', err);
      setError(err.response?.data?.message || 'Failed to update company. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };
  
  const generateNewInviteCode = async () => {
    try {
      setIsSaving(true);
      
      const { data } = await api.post(`${endpoints.companies.base}/generate-invite-code`, {
        companyId: company._id,
      });
      
      setInviteCode(data.invite_code);
      Alert.alert('Success', 'New invite code generated successfully');
    } catch (err) {
      console.error('Failed to generate new invite code:', err);
      Alert.alert('Error', 'Failed to generate new invite code. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };
  
  const copyInviteLink = () => {
    const inviteLink = `https://kodi.app/join?code=${inviteCode}`;
    Clipboard.setString(inviteLink);
    Alert.alert('Copied', 'Invite link copied to clipboard');
  };
  
  const shareInviteLink = async () => {
    const inviteLink = `https://kodi.app/join?code=${inviteCode}`;
    try {
      await Share.share({
        message: `Join my company on Kodi! Use this link to sign up: ${inviteLink}`,
      });
    } catch (err) {
      console.error('Failed to share invite link:', err);
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }
  
  if (!company) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.secondary }]}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.foreground }]}>Company</Text>
        </View>
        
        <Card style={styles.noCompanyCard}>
          <Text style={[styles.noCompanyText, { color: colors.foreground }]}>
            You are not associated with any company.
          </Text>
        </Card>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.secondary }]}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.foreground} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: colors.foreground }]}>Company Settings</Text>
        </View>
        
        {error ? (
          <Card style={[styles.errorCard, { backgroundColor: colors.destructive + '20' }]}>
            <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
          </Card>
        ) : null}
        
        <Card style={styles.logoCard}>
          <View style={styles.logoContainer}>
            <View style={[styles.logo, { backgroundColor: colors.muted }]}>
              {logo ? (
                <Image source={{ uri: logo }} style={styles.logoImage} />
              ) : (
                <Text style={[styles.logoPlaceholder, { color: colors.mutedForeground }]}>
                  {name?.[0]?.toUpperCase() || 'C'}
                </Text>
              )}
            </View>
            <TouchableOpacity
              style={[styles.changeLogoButton, { backgroundColor: colors.primary }]}
              onPress={pickImage}
              disabled={isSaving}
            >
              <Ionicons name="camera" size={20} color={colors.primaryForeground} />
            </TouchableOpacity>
          </View>
          <Text style={[styles.logoHint, { color: colors.mutedForeground }]}>
            Tap to change your company logo
          </Text>
        </Card>
        
        <Card style={styles.formCard}>
          <Input
            label="Company Name"
            value={name}
            onChangeText={setName}
            placeholder="Enter company name"
            leftIcon={<Ionicons name="business-outline" size={20} color={colors.mutedForeground} />}
          />
          
          <Text style={[styles.sectionTitle, { color: colors.foreground }]}>Invite Code</Text>
          <View style={styles.inviteCodeContainer}>
            <View style={[styles.inviteCode, { backgroundColor: colors.secondary }]}>
              <Text style={[styles.inviteCodeText, { color: colors.secondaryForeground }]}>
                {inviteCode}
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.copyButton, { backgroundColor: colors.primary }]}
              onPress={copyInviteLink}
            >
              <Ionicons name="copy-outline" size={20} color={colors.primaryForeground} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.inviteActions}>
            <Button
              title="Generate New Code"
              variant="outline"
              onPress={generateNewInviteCode}
              isLoading={isSaving}
              style={styles.generateButton}
            />
            <Button
              title="Share Invite Link"
              leftIcon={<Ionicons name="share-outline" size={20} color={colors.primaryForeground} />}
              onPress={shareInviteLink}
              style={styles.shareButton}
            />
          </View>
        </Card>
        
        <Button
          title="Save Changes"
          onPress={saveCompany}
          isLoading={isSaving}
          style={styles.saveButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noCompanyCard: {
    padding: 24,
    alignItems: 'center',
  },
  noCompanyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  errorCard: {
    padding: 12,
    marginBottom: 16,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 14,
  },
  logoCard: {
    alignItems: 'center',
    marginBottom: 16,
  },
  logoContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  logoPlaceholder: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  changeLogoButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoHint: {
    fontSize: 14,
    marginTop: 8,
  },
  formCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  inviteCodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inviteCode: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
  },
  inviteCodeText: {
    fontSize: 16,
    fontWeight: '500',
  },
  copyButton: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  inviteActions: {
    flexDirection: 'row',
    marginTop: 16,
  },
  generateButton: {
    flex: 1,
    marginRight: 8,
  },
  shareButton: {
    flex: 1,
    marginLeft: 8,
  },
  saveButton: {
    marginBottom: 24,
  },
});
