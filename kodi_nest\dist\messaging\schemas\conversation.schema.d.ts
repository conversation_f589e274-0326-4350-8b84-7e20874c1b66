import { Document, Schema as MongooseSchema } from 'mongoose';
export type ConversationDocument = Conversation & Document;
export declare class Conversation {
    participants: MongooseSchema.Types.ObjectId[];
    title: string;
    isGroup: boolean;
    lastMessageAt: Date;
}
export declare const ConversationSchema: MongooseSchema<Conversation, import("mongoose").Model<Conversation, any, any, any, Document<unknown, any, Conversation> & Conversation & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Conversation, Document<unknown, {}, import("mongoose").FlatRecord<Conversation>> & import("mongoose").FlatRecord<Conversation> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
