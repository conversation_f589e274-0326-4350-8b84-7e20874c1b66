import axios from 'axios';

// Get the API URL from environment or use default
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3011/api';

/**
 * Connection status object
 */
export interface ConnectionStatus {
  server: 'connected' | 'error' | 'checking';
  message: string;
  timestamp: number;
}

/**
 * Check if the server is reachable
 * @returns Promise<ConnectionStatus>
 */
export async function checkServerConnection(): Promise<ConnectionStatus> {
  try {
    // Try to connect to the health endpoint
    const response = await axios.get(`${API_URL}/health`, {
      timeout: 5000, // 5 second timeout
    });

    if (response.status === 200) {
      return {
        server: 'connected',
        message: 'Connected to server',
        timestamp: Date.now(),
      };
    } else {
      return {
        server: 'error',
        message: `Server responded with status: ${response.status}`,
        timestamp: Date.now(),
      };
    }
  } catch (error: any) {
    console.error('Server connection check failed:', error);
    
    // Provide more specific error messages based on the error
    let errorMessage = 'Could not connect to server';
    
    if (error.code === 'ECONNABORTED') {
      errorMessage = 'Connection timed out';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'Connection refused';
    } else if (error.response) {
      errorMessage = `Server error: ${error.response.status}`;
    } else if (error.request) {
      errorMessage = 'No response from server';
    }
    
    return {
      server: 'error',
      message: errorMessage,
      timestamp: Date.now(),
    };
  }
}

/**
 * Check connection and log the result
 * @returns Promise<ConnectionStatus>
 */
export async function logConnectionStatus(): Promise<ConnectionStatus> {
  const status = await checkServerConnection();
  
  if (status.server === 'connected') {
    console.log('✅ Server connection: OK');
  } else {
    console.error(`❌ Server connection: ${status.message}`);
  }
  
  return status;
}
