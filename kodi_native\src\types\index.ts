// User types
export interface User {
  id: string;
  email: string;
  profile: Profile;
  roles: string[];
}

export interface Profile {
  _id: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  transformedAvatar?: {
    small: string;
    medium: string;
    large: string;
  };
  companyId?: string;
  userSettings?: {
    showOnboarder: boolean;
    onboarderStep: number;
    [key: string]: any;
  };
}

// Company types
export interface Company {
  _id: string;
  name: string;
  invite_code: string;
  createdBy: string;
  accountType: number;
  logo?: string;
  transformedLogo?: {
    small: string;
    medium: string;
    large: string;
  };
  hashtags?: string[];
  settings?: Record<string, any>;
}

// Post types
export interface Post {
  _id: string;
  body: string;
  enhancedBody?: string;
  userId: string;
  createdAt: string;
  hashtags: string[];
  user?: {
    firstName?: string;
    lastName?: string;
    avatar?: string;
  };
  teamId?: string;
  team?: {
    name: string;
  };
  postTypeId?: string;
  postType?: {
    name: string;
  };
  audioUrl?: string;
  isDraft?: boolean;
}

export interface Team {
  _id: string;
  name: string;
  description?: string;
  companyId: string;
  createdBy: string;
  members?: string[];
}

export interface PostType {
  _id: string;
  name: string;
  description?: string;
  recordTips?: string;
  companyId: string;
  userId: string;
  archived?: boolean;
  highlightColor?: string;
}

export interface Tag {
  _id: string;
  name: string;
  companyId: string;
  count: number;
}

// Comment types
export interface Comment {
  _id: string;
  body: string;
  userId: string;
  linkedObjectId: string;
  objectType: 'Post' | 'Summary' | 'Insight';
  createdAt: string;
  user?: {
    firstName?: string;
    lastName?: string;
    avatar?: string;
  };
}

// AI types
export interface Summary {
  _id: string;
  title: string;
  content: string;
  userId: string;
  companyId?: string;
  tags?: string[];
  createdAt: string;
}

export interface Insight {
  _id: string;
  title: string;
  content: string;
  userId: string;
  companyId?: string;
  tags?: string[];
  createdAt: string;
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

export interface AiChat {
  _id: string;
  title: string;
  messages: ChatMessage[];
  userId: string;
  companyId?: string;
  createdAt: string;
}

// Auth types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  inviteCode?: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  user: User;
}
