import { Test, TestingModule } from '@nestjs/testing';
import { AiService } from './ai.service';
import { getModelToken } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { Summary } from './schemas/summary.schema';
import { Insight } from './schemas/insight.schema';
import { AiPrompt } from './schemas/ai-prompt.schema';
import { AiChat } from './schemas/ai-chat.schema';
import { AppSettings } from './schemas/app-settings.schema';
import { PostsService } from '../posts/posts.service';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('AiService', () => {
  let service: AiService;
  let summaryModel: Model<Summary>;
  let insightModel: Model<Insight>;
  let aiPromptModel: Model<AiPrompt>;
  let aiChatModel: Model<AiChat>;
  let appSettingsModel: Model<AppSettings>;
  let postsService: PostsService;
  let configService: ConfigService;

  const mockSummaryModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockInsightModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockAiPromptModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockAiChatModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockAppSettingsModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockPostsService = {
    findPostById: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiService,
        {
          provide: getModelToken(Summary.name),
          useValue: mockSummaryModel,
        },
        {
          provide: getModelToken(Insight.name),
          useValue: mockInsightModel,
        },
        {
          provide: getModelToken(AiPrompt.name),
          useValue: mockAiPromptModel,
        },
        {
          provide: getModelToken(AiChat.name),
          useValue: mockAiChatModel,
        },
        {
          provide: getModelToken(AppSettings.name),
          useValue: mockAppSettingsModel,
        },
        {
          provide: PostsService,
          useValue: mockPostsService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AiService>(AiService);
    summaryModel = module.get<Model<Summary>>(getModelToken(Summary.name));
    insightModel = module.get<Model<Insight>>(getModelToken(Insight.name));
    aiPromptModel = module.get<Model<AiPrompt>>(getModelToken(AiPrompt.name));
    aiChatModel = module.get<Model<AiChat>>(getModelToken(AiChat.name));
    appSettingsModel = module.get<Model<AppSettings>>(getModelToken(AppSettings.name));
    postsService = module.get<PostsService>(PostsService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSummary', () => {
    it('should create a new summary', async () => {
      const createSummaryDto = {
        title: 'Test Summary',
        userPrompt: 'Summarize this',
        summary: 'This is a summary',
        userId: 'user1',
        companyId: 'company1',
        lastUpdated: new Date(),
      };

      const savedSummary = {
        _id: 'summary1',
        ...createSummaryDto,
      };

      // Mock the model constructor and save
      mockSummaryModel.new = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedSummary),
      }));

      const result = await service.createSummary(createSummaryDto);

      expect(result).toEqual(savedSummary);
    });
  });

  describe('findAllSummaries', () => {
    it('should return all summaries with no filters', async () => {
      const mockSummaries = [
        { _id: 'summary1', title: 'Summary 1' },
        { _id: 'summary2', title: 'Summary 2' },
      ];

      mockSummaryModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockSummaries),
        }),
      });

      const result = await service.findAllSummaries();

      expect(mockSummaryModel.find).toHaveBeenCalledWith({});
      expect(result).toEqual(mockSummaries);
    });

    it('should apply filters when provided', async () => {
      const mockSummaries = [{ _id: 'summary1', title: 'Summary 1' }];

      mockSummaryModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockSummaries),
        }),
      });

      const result = await service.findAllSummaries('user1', 'company1', 'tag1');

      expect(mockSummaryModel.find).toHaveBeenCalledWith({
        userId: 'user1',
        companyId: 'company1',
        tags: 'tag1',
      });
      expect(result).toEqual(mockSummaries);
    });
  });

  describe('generateSummary', () => {
    it('should generate a summary and save it', async () => {
      const createSummaryDto = {
        title: 'Test Summary',
        userPrompt: 'Summarize this',
        userId: 'user1',
        companyId: 'company1',
      };

      const generatedSummary = {
        _id: 'summary1',
        ...createSummaryDto,
        summary: expect.stringContaining('This is a generated summary'),
        lastUpdated: expect.any(Date),
      };

      // Mock the createSummary method
      jest.spyOn(service, 'createSummary').mockResolvedValue(generatedSummary as any);

      const result = await service.generateSummary(createSummaryDto as any);

      expect(service.createSummary).toHaveBeenCalledWith({
        ...createSummaryDto,
        summary: expect.stringContaining('This is a generated summary'),
        lastUpdated: expect.any(Date),
      });
      expect(result).toEqual(generatedSummary);
    });
  });

  describe('createAiChat', () => {
    it('should create a new AI chat', async () => {
      const createAiChatDto = {
        title: 'Test Chat',
        userId: 'user1',
        companyId: 'company1',
      };

      const savedChat = {
        _id: 'chat1',
        ...createAiChatDto,
        messages: [],
      };

      // Mock the model constructor and save
      mockAiChatModel.new = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedChat),
      }));

      const result = await service.createAiChat(createAiChatDto);

      expect(result).toEqual(savedChat);
    });
  });

  describe('sendChatMessage', () => {
    it('should add a message to an existing chat and generate a response', async () => {
      const chatId = 'chat1';
      const userId = 'user1';
      const message = 'Hello AI';

      const existingChat = {
        _id: chatId,
        title: 'Test Chat',
        userId,
        messages: [],
        save: jest.fn().mockResolvedValue({
          _id: chatId,
          title: 'Hello AI',
          userId,
          messages: [
            {
              role: 'user',
              content: message,
              timestamp: expect.any(Date),
            },
            {
              role: 'assistant',
              content: expect.stringContaining('This is a mock response'),
              timestamp: expect.any(Date),
            },
          ],
        }),
      };

      mockAiChatModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingChat),
      });

      const result = await service.sendChatMessage(chatId, userId, message);

      expect(mockAiChatModel.findById).toHaveBeenCalledWith(chatId);
      expect(existingChat.save).toHaveBeenCalled();
      expect(result.messages.length).toBe(2);
      expect(result.messages[0].role).toBe('user');
      expect(result.messages[0].content).toBe(message);
      expect(result.messages[1].role).toBe('assistant');
      expect(result.messages[1].content).toContain('This is a mock response');
    });

    it('should throw NotFoundException when chat does not exist', async () => {
      mockAiChatModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.sendChatMessage('nonexistent', 'user1', 'Hello')).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException when user is not the chat owner', async () => {
      const existingChat = {
        _id: 'chat1',
        userId: 'user1',
        messages: [],
      };

      mockAiChatModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingChat),
      });

      await expect(service.sendChatMessage('chat1', 'user2', 'Hello')).rejects.toThrow(ForbiddenException);
    });
  });

  describe('transcribeAudio', () => {
    it('should transcribe audio and return the transcription', async () => {
      const transcribeAudioDto = {
        audioUrl: 'https://example.com/audio.mp3',
      };

      // Mock the AI transcription service
      // In a real implementation, this would call an external service

      const result = await service.transcribeAudio(transcribeAudioDto);

      expect(result).toHaveProperty('transcription');
      expect(result.transcription).toContain('This is a mock transcription');
    });
  });
});
