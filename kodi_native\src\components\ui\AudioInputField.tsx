import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { Audio } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import api, { endpoints } from '@/src/api/api';

interface AudioInputFieldProps {
  onAudioProcessed: (text: string) => void;
  placeholder?: string;
  label?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  style?: any;
  inputStyle?: any;
  labelStyle?: any;
  colors: any;
}

export default function AudioInputField({
  onAudioProcessed,
  placeholder = 'Enter text or record audio...',
  label,
  value,
  onChangeText,
  style,
  inputStyle,
  labelStyle,
  colors,
}: AudioInputFieldProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [audioUri, setAudioUri] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const [recordingTime, setRecordingTime] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const startRecording = async () => {
    try {
      setError('');
      
      // Request permissions
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        setError('Permission to access microphone was denied');
        return;
      }

      // Prepare the recording
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      // Start recording
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      
      setRecording(recording);
      setIsRecording(true);

      // Start timer
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } catch (err) {
      console.error('Error starting recording:', err);
      setError('Failed to start recording. Please try again.');
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    try {
      await recording.stopAndUnloadAsync();
      
      // Stop timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Get the recording URI
      const uri = recording.getURI();
      if (!uri) {
        throw new Error('Recording URI is null');
      }
      
      setAudioUri(uri);
      setIsRecording(false);
      setRecording(null);

      // Reset audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
      });
    } catch (err) {
      console.error('Error stopping recording:', err);
      setError('Failed to stop recording. Please try again.');
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const processAudio = async () => {
    if (!audioUri) return;

    setIsProcessing(true);
    setError('');

    try {
      // Create form data for the audio file
      const formData = new FormData();
      formData.append('file', {
        uri: audioUri,
        type: 'audio/m4a',
        name: 'recording.m4a',
      } as any);

      // Upload the audio file
      const uploadResponse = await api.post(endpoints.uploads.audioUpload, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const audioUrl = uploadResponse.data.url;

      // Transcribe the audio
      const transcribeResponse = await api.post(endpoints.ai.transcribe, {
        audioUrl,
      });

      const transcription = transcribeResponse.data.text;

      // Pass the transcription back to the parent component
      onAudioProcessed(transcription);

      // Reset state
      setAudioUri(null);
    } catch (err: any) {
      console.error('Error processing audio:', err);
      setError(err.response?.data?.message || 'Failed to process audio. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const cancelRecording = async () => {
    if (isRecording && recording) {
      try {
        await recording.stopAndUnloadAsync();
        
        // Stop timer
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
        
        setIsRecording(false);
        setRecording(null);
        
        // Reset audio mode
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
        });
      } catch (err) {
        console.error('Error canceling recording:', err);
      }
    }
    
    setAudioUri(null);
  };

  return (
    <View style={[styles.container, style]}>
      {label && (
        <Text style={[styles.label, { color: colors.foreground }, labelStyle]}>
          {label}
        </Text>
      )}
      
      <View style={styles.inputContainer}>
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={colors.mutedForeground}
          style={[
            styles.input, 
            { 
              color: colors.foreground,
              borderColor: colors.border,
              backgroundColor: colors.card,
            },
            inputStyle
          ]}
          editable={!isRecording && !isProcessing}
        />
        
        {!isRecording && !audioUri && !isProcessing && (
          <TouchableOpacity
            style={styles.recordButton}
            onPress={startRecording}
            accessibilityRole="button"
            accessibilityLabel="Record audio"
          >
            <Ionicons name="mic-outline" size={20} color={colors.mutedForeground} />
          </TouchableOpacity>
        )}
      </View>

      {error ? (
        <Text style={[styles.errorText, { color: colors.destructive }]}>
          {error}
        </Text>
      ) : null}

      {isRecording && (
        <View style={styles.recordingContainer}>
          <View style={[styles.recordingIndicator, { backgroundColor: colors.destructive }]} />
          <Text style={[styles.recordingText, { color: colors.destructive }]}>
            Recording... {formatTime(recordingTime)}
          </Text>
          <TouchableOpacity
            style={[styles.stopButton, { backgroundColor: colors.muted }]}
            onPress={stopRecording}
          >
            <Text style={[styles.buttonText, { color: colors.foreground }]}>Stop</Text>
          </TouchableOpacity>
        </View>
      )}

      {audioUri && !isProcessing && (
        <View style={styles.audioControlsContainer}>
          <View style={styles.audioPlayerPlaceholder}>
            <Text style={[styles.audioReadyText, { color: colors.foreground }]}>
              Audio recording ready
            </Text>
          </View>
          <View style={styles.audioButtonsContainer}>
            <TouchableOpacity
              style={[styles.useButton, { backgroundColor: colors.primary }]}
              onPress={processAudio}
            >
              <Text style={[styles.buttonText, { color: colors.primaryForeground }]}>
                Use Recording
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.cancelButton, { backgroundColor: colors.muted }]}
              onPress={cancelRecording}
            >
              <Text style={[styles.buttonText, { color: colors.foreground }]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {isProcessing && (
        <View style={styles.processingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.processingText, { color: colors.mutedForeground }]}>
            Processing audio...
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 6,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  input: {
    flex: 1,
    height: 44,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingRight: 40,
    fontSize: 16,
  },
  recordButton: {
    position: 'absolute',
    right: 12,
    padding: 4,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  recordingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  recordingIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  recordingText: {
    fontSize: 14,
    flex: 1,
  },
  stopButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  buttonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  audioControlsContainer: {
    marginTop: 8,
  },
  audioPlayerPlaceholder: {
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
    marginBottom: 8,
  },
  audioReadyText: {
    fontSize: 14,
  },
  audioButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  useButton: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 4,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 4,
    alignItems: 'center',
  },
  processingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  processingText: {
    fontSize: 14,
    marginLeft: 8,
  },
});
