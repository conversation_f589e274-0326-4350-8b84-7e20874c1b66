'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';

interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

interface AiChat {
  _id: string;
  title: string;
  messages: ChatMessage[];
  userId: string;
  createdAt: string;
}

export default function ChatPage() {
  const { user } = useAuth();
  const [chats, setChats] = useState<AiChat[]>([]);
  const [selectedChat, setSelectedChat] = useState<AiChat | null>(null);
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchChats();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [selectedChat?.messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchChats = async () => {
    try {
      setLoading(true);
      const { data } = await api.get(endpoints.aiChat.base);
      setChats(data);
      if (data.length > 0 && !selectedChat) {
        setSelectedChat(data[0]);
      }
    } catch (err: any) {
      console.error('Failed to fetch chats:', err);
      setError('Failed to load chats. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const createNewChat = async () => {
    try {
      const { data } = await api.post(endpoints.aiChat.base, {
        title: 'New Chat',
        userId: user?.id,
      });
      setChats([data, ...chats]);
      setSelectedChat(data);
    } catch (err: any) {
      console.error('Failed to create new chat:', err);
      setError('Failed to create new chat. Please try again later.');
    }
  };

  const sendMessage = async () => {
    if (!message.trim() || !selectedChat) return;

    const newMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date(),
    };

    try {
      setSending(true);
      // Update UI immediately
      const updatedChat = {
        ...selectedChat,
        messages: [...selectedChat.messages, newMessage],
      };
      setSelectedChat(updatedChat);
      setMessage('');

      // Send to API
      const { data } = await api.post(
        endpoints.aiChat.messages(selectedChat._id),
        { message: newMessage.content }
      );

      // Update with response from API
      setSelectedChat(data);

      // Update chat list
      setChats(
        chats.map((chat) => (chat._id === selectedChat._id ? data : chat))
      );
    } catch (err: any) {
      console.error('Failed to send message:', err);
      setError('Failed to send message. Please try again later.');
    } finally {
      setSending(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-4">
      <h1 className="text-2xl font-bold mb-4">AI Chat</h1>

      {error && (
        <div className="bg-red-50 p-3 rounded-md mb-4 border border-red-200">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <div className="lg:col-span-1">
          <div className="bg-white rounded-md border border-border p-3 mb-3">
            <button
              onClick={createNewChat}
              className="w-full px-3 py-2 bg-primary text-white rounded hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            >
              New Chat
            </button>
          </div>

          <div className="bg-white rounded-md border border-border overflow-hidden">
            <div className="p-3 border-b">
              <h2 className="text-lg font-semibold">Your Chats</h2>
            </div>
            {loading ? (
              <div className="flex justify-center py-6">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : chats.length === 0 ? (
              <div className="p-3 text-center text-gray-500">
                No chats yet. Start a new conversation!
              </div>
            ) : (
              <div className="divide-y max-h-[500px] overflow-y-auto">
                {chats.map((chat) => (
                  <button
                    key={chat._id}
                    onClick={() => setSelectedChat(chat)}
                    className={`w-full text-left p-3 hover:bg-gray-50 ${
                      selectedChat?._id === chat._id ? 'bg-indigo-50' : ''
                    }`}
                  >
                    <p className="font-medium truncate">{chat.title}</p>
                    <p className="text-sm text-gray-500">
                      {new Date(chat.createdAt).toLocaleDateString()}
                    </p>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="lg:col-span-3">
          <div className="bg-white rounded-md border border-border flex flex-col h-[600px]">
            <div className="p-3 border-b">
              <h2 className="text-lg font-semibold">
                {selectedChat ? selectedChat.title : 'Select or create a chat'}
              </h2>
            </div>

            <div className="flex-1 overflow-y-auto p-3">
              {!selectedChat ? (
                <div className="flex items-center justify-center h-full">
                  <p className="text-gray-500">
                    Select an existing chat or create a new one to get started
                  </p>
                </div>
              ) : selectedChat.messages.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <p className="text-gray-500">
                    Send a message to start the conversation
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {selectedChat.messages.map((msg, index) => (
                    <div
                      key={index}
                      className={`flex ${
                        msg.role === 'user' ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      <div
                        className={`max-w-[80%] rounded p-3 ${
                          msg.role === 'user'
                            ? 'bg-primary bg-opacity-10 text-primary-foreground'
                            : 'bg-gray-100 text-gray-900'
                        }`}
                      >
                        <p className="whitespace-pre-wrap">{msg.content}</p>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>

            {selectedChat && (
              <div className="p-3 border-t">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                      }
                    }}
                    placeholder="Type your message..."
                    className="flex-1 rounded border-border focus:border-primary focus:ring-primary"
                    disabled={sending}
                  />
                  <button
                    onClick={sendMessage}
                    disabled={!message.trim() || sending}
                    className="px-3 py-2 bg-primary text-white rounded hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:bg-opacity-70"
                  >
                    {sending ? 'Sending...' : 'Send'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
