import { IsNotEmpty, IsString, IsOptional, IsNumber, IsObject, IsArray } from 'class-validator';
import { CompanyAccountType } from '../schemas/company.schema';

export class CreateCompanyDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  invite_code?: string;

  @IsNotEmpty()
  @IsString()
  createdBy: string;

  @IsOptional()
  @IsNumber()
  accountType?: CompanyAccountType;

  @IsOptional()
  @IsString()
  logo?: string;

  @IsOptional()
  @IsObject()
  transformedLogo?: {
    small: string;
    medium: string;
    large: string;
  };

  @IsOptional()
  @IsArray()
  hashtags?: string[];

  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;
}
