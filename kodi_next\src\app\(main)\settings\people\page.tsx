'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import Link from 'next/link';

interface User {
  _id: string;
  email: string;
  profile: {
    firstName: string;
    lastName: string;
    role?: string;
    avatar?: string;
  };
  roles: string[];
}

export default function PeopleSettingsPage() {
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (user?.profile?.companyId) {
      fetchUsers(user.profile.companyId);
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchUsers = async (companyId: string) => {
    try {
      setLoading(true);
      const { data } = await api.get(`${endpoints.users.base}?companyId=${companyId}`);
      setUsers(data);
    } catch (err: any) {
      console.error('Failed to fetch users:', err);
      setError('Failed to load users. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-5xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">People</h1>
          <div className="flex space-x-4">
            <Link 
              href="/settings"
              className="text-sm text-indigo-600 hover:text-indigo-800"
            >
              Back to Settings
            </Link>
            {user?.roles?.includes('admin') && (
              <Link
                href="/settings/people/invite"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Invite People
              </Link>
            )}
          </div>
        </div>

        {error && (
          <div className="bg-red-50 p-4 rounded-md mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {users.length > 0 ? (
                users.map((user) => (
                  <li key={user._id}>
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                            {user.profile.avatar ? (
                              <img
                                src={user.profile.avatar}
                                alt={`${user.profile.firstName} ${user.profile.lastName}`}
                                className="h-10 w-10 object-cover"
                              />
                            ) : (
                              <span className="text-gray-500 text-sm font-medium">
                                {user.profile.firstName?.[0] || ''}
                                {user.profile.lastName?.[0] || ''}
                              </span>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {user.profile.firstName} {user.profile.lastName}
                            </div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            {user.roles.join(', ')}
                          </span>
                          {user?.roles?.includes('admin') && (
                            <Link
                              href={`/settings/people/${user._id}`}
                              className="ml-4 text-sm text-indigo-600 hover:text-indigo-900"
                            >
                              Edit
                            </Link>
                          )}
                        </div>
                      </div>
                    </div>
                  </li>
                ))
              ) : (
                <li className="px-4 py-5 sm:px-6">
                  <div className="text-center text-gray-500">
                    No users found
                  </div>
                </li>
              )}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
