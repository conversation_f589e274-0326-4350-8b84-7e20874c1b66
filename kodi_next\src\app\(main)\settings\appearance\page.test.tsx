import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AppearanceSettingsPage from './page';
import { ThemeProvider } from '@/contexts/ThemeContext';

// Mock the theme context
const mockSetTheme = jest.fn();
jest.mock('@/contexts/ThemeContext', () => ({
  ...jest.requireActual('@/contexts/ThemeContext'),
  useTheme: () => ({
    theme: 'light',
    setTheme: mockSetTheme,
  }),
}));

describe('AppearanceSettingsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the appearance settings page', () => {
    render(
      <ThemeProvider>
        <AppearanceSettingsPage />
      </ThemeProvider>
    );
    
    // Check for page title
    expect(screen.getByText('Appearance Settings')).toBeInTheDocument();
    
    // Check for back link
    expect(screen.getByText('Back to Settings')).toBeInTheDocument();
    expect(screen.getByText('Back to Settings').closest('a')).toHaveAttribute('href', '/settings');
    
    // Check for theme section
    expect(screen.getByText('Theme')).toBeInTheDocument();
    expect(screen.getByText('Choose your preferred theme for the application')).toBeInTheDocument();
    
    // Check for theme options
    expect(screen.getByText('Light')).toBeInTheDocument();
    expect(screen.getByText('Dark')).toBeInTheDocument();
    expect(screen.getByText('System')).toBeInTheDocument();
  });

  it('selects light theme', async () => {
    render(
      <ThemeProvider>
        <AppearanceSettingsPage />
      </ThemeProvider>
    );
    
    // Find and click the light theme button
    const user = userEvent.setup();
    await user.click(screen.getByText('Light'));
    
    // Check that setTheme was called with correct value
    expect(mockSetTheme).toHaveBeenCalledWith('light');
    
    // Check for success message
    await waitFor(() => {
      expect(screen.getByText('Theme preference saved')).toBeInTheDocument();
    });
  });

  it('selects dark theme', async () => {
    render(
      <ThemeProvider>
        <AppearanceSettingsPage />
      </ThemeProvider>
    );
    
    // Find and click the dark theme button
    const user = userEvent.setup();
    await user.click(screen.getByText('Dark'));
    
    // Check that setTheme was called with correct value
    expect(mockSetTheme).toHaveBeenCalledWith('dark');
    
    // Check for success message
    await waitFor(() => {
      expect(screen.getByText('Theme preference saved')).toBeInTheDocument();
    });
  });

  it('selects system theme', async () => {
    render(
      <ThemeProvider>
        <AppearanceSettingsPage />
      </ThemeProvider>
    );
    
    // Find and click the system theme button
    const user = userEvent.setup();
    await user.click(screen.getByText('System'));
    
    // Check that setTheme was called with correct value
    expect(mockSetTheme).toHaveBeenCalledWith('system');
    
    // Check for success message
    await waitFor(() => {
      expect(screen.getByText('Theme preference saved')).toBeInTheDocument();
    });
  });

  it('highlights the current theme', () => {
    // Mock different theme values
    jest.spyOn(require('@/contexts/ThemeContext'), 'useTheme').mockImplementation(() => ({
      theme: 'dark',
      setTheme: mockSetTheme,
    }));
    
    render(
      <ThemeProvider>
        <AppearanceSettingsPage />
      </ThemeProvider>
    );
    
    // Check that the dark theme button is highlighted
    const darkButton = screen.getByText('Dark').closest('button');
    expect(darkButton).toHaveClass('bg-indigo-600');
    
    // Light and system buttons should not be highlighted
    const lightButton = screen.getByText('Light').closest('button');
    const systemButton = screen.getByText('System').closest('button');
    expect(lightButton).not.toHaveClass('bg-indigo-600');
    expect(systemButton).not.toHaveClass('bg-indigo-600');
  });
});
