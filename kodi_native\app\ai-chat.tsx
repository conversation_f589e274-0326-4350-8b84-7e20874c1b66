import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Card from '@/src/components/ui/Card';
import { ChatMessage } from '@/src/types';

export default function AIChatScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  const params = useLocalSearchParams<{ id?: string }>();
  
  const [chatId, setChatId] = useState<string | null>(params.id || null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState('');
  
  const flatListRef = useRef<FlatList>(null);
  
  useEffect(() => {
    if (chatId) {
      fetchMessages();
    } else {
      // Start with a welcome message
      setMessages([
        {
          role: 'assistant',
          content: 'Hello! How can I help you today?',
          timestamp: new Date(),
        },
      ]);
      setInitialLoading(false);
    }
  }, [chatId]);
  
  const fetchMessages = async () => {
    try {
      setInitialLoading(true);
      const { data } = await api.get(endpoints.aiChat.messages(chatId!));
      setMessages(data);
    } catch (err) {
      console.error('Failed to fetch messages:', err);
      setError('Failed to load chat messages. Please try again later.');
    } finally {
      setInitialLoading(false);
    }
  };
  
  const handleGoBack = () => {
    router.back();
  };
  
  const sendMessage = async () => {
    if (!input.trim()) return;
    
    const userMessage: ChatMessage = {
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
    };
    
    setMessages([...messages, userMessage]);
    setInput('');
    setLoading(true);
    
    try {
      let response;
      
      if (chatId) {
        // Add message to existing chat
        response = await api.post(endpoints.aiChat.messages(chatId), {
          content: userMessage.content,
          role: 'user',
        });
      } else {
        // Create new chat with first message
        response = await api.post(endpoints.aiChat.base, {
          title: userMessage.content.substring(0, 50) + (userMessage.content.length > 50 ? '...' : ''),
          messages: [userMessage],
          userId: user?.id,
          companyId: user?.profile?.companyId,
        });
        
        setChatId(response.data._id);
      }
      
      // Get AI response
      const aiResponse = await api.post(`${endpoints.aiChat.messages(response.data._id || chatId!)}`, {
        role: 'assistant',
        content: 'Thinking...',
      });
      
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: aiResponse.data.content,
        timestamp: new Date(aiResponse.data.timestamp),
      };
      
      setMessages((prev) => [...prev, assistantMessage]);
    } catch (err) {
      console.error('Failed to send message:', err);
      setMessages((prev) => [
        ...prev,
        {
          role: 'assistant',
          content: 'Sorry, I encountered an error. Please try again later.',
          timestamp: new Date(),
        },
      ]);
    } finally {
      setLoading(false);
    }
  };
  
  const renderMessage = ({ item }: { item: ChatMessage }) => {
    const isUser = item.role === 'user';
    
    return (
      <View
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.aiMessageContainer,
        ]}
      >
        <View
          style={[
            styles.messageBubble,
            isUser
              ? [styles.userMessageBubble, { backgroundColor: colors.primary }]
              : [styles.aiMessageBubble, { backgroundColor: colors.secondary }],
          ]}
        >
          <Text
            style={[
              styles.messageText,
              {
                color: isUser ? colors.primaryForeground : colors.secondaryForeground,
              },
            ]}
          >
            {item.content}
          </Text>
        </View>
      </View>
    );
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>AI Chat</Text>
      </View>
      
      {initialLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : error ? (
        <Card style={styles.errorCard}>
          <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
        </Card>
      ) : (
        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
        >
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(_, index) => index.toString()}
            contentContainerStyle={styles.messagesList}
            onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
            onLayout={() => flatListRef.current?.scrollToEnd({ animated: true })}
          />
          
          <View style={[styles.inputContainer, { backgroundColor: colors.card }]}>
            <TextInput
              style={[styles.input, { color: colors.foreground, backgroundColor: colors.input }]}
              placeholder="Type your message..."
              placeholderTextColor={colors.mutedForeground}
              value={input}
              onChangeText={setInput}
              multiline
              maxLength={1000}
              editable={!loading}
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                {
                  backgroundColor: input.trim() ? colors.primary : colors.muted,
                  opacity: input.trim() && !loading ? 1 : 0.6,
                },
              ]}
              onPress={sendMessage}
              disabled={!input.trim() || loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color={colors.primaryForeground} />
              ) : (
                <Ionicons name="send" size={20} color={colors.primaryForeground} />
              )}
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorCard: {
    margin: 16,
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  messagesList: {
    padding: 16,
    paddingBottom: 24,
  },
  messageContainer: {
    marginBottom: 16,
    maxWidth: '80%',
  },
  userMessageContainer: {
    alignSelf: 'flex-end',
  },
  aiMessageContainer: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    borderRadius: 16,
    padding: 12,
  },
  userMessageBubble: {
    borderBottomRightRadius: 4,
  },
  aiMessageBubble: {
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  input: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    maxHeight: 120,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
});
