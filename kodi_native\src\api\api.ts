import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Determine the API URL based on the platform
const getApiUrl = () => {
  if (Platform.OS === 'web') {
    // For web, use the same origin or localhost
    // Only access window if we're in a browser environment
    if (isBrowser) {
      const host = window.location.hostname;
      return host === 'localhost'
        ? 'http://localhost:3011/api'
        : `http://${host}:3011/api`;
    }
    // Default for SSR
    return 'http://localhost:3011/api';
  } else {
    // For native, use localhost with the correct port
    // This works in the Expo development environment
    return 'http://localhost:3011/api';
  }
};

// Initialize with a default value that will be updated on the client side
let API_URL = 'http://localhost:3011/api';

// Only call getApiUrl if we're in a browser environment or on native
if (Platform.OS !== 'web' || isBrowser) {
  API_URL = getApiUrl();
}

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // Enable credentials for web (cookies, auth headers)
  withCredentials: Platform.OS === 'web',
});

// Add a request interceptor to include auth token
api.interceptors.request.use(
  async (config) => {
    const token = await SecureStore.getItemAsync('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a debug interceptor to log requests and responses
api.interceptors.request.use(
  (config) => {
    console.log(`🚀 API Request [${config.method?.toUpperCase()}] ${config.url}`, {
      headers: config.headers,
      params: config.params,
      data: config.data
    });
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add a debug interceptor to log responses
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response [${response.config.method?.toUpperCase()}] ${response.config.url}:`, {
      status: response.status,
      statusText: response.statusText,
      data: response.data ? 'Data received' : 'No data'
    });
    return response;
  },
  (error: AxiosError) => {
    if (error.response) {
      console.error(`❌ API Error [${error.config?.method?.toUpperCase()}] ${error.config?.url}:`, {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.config?.headers,
        params: error.config?.params
      });

      // For 400 Bad Request errors, log more details
      if (error.response.status === 400) {
        console.error('🔍 Bad Request Details:', {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers,
          data: error.config?.data,
          params: error.config?.params,
          responseData: error.response.data
        });
      }
    } else if (error.request) {
      console.error('❌ API Error (No Response):', {
        url: error.config?.url,
        method: error.config?.method
      });
    } else {
      console.error('❌ API Error (Setup):', {
        message: error.message,
        url: error.config?.url
      });
    }

    return Promise.reject(error);
  }
);

// Add a response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // If error is 401 and we haven't retried yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const refreshToken = await SecureStore.getItemAsync('refresh_token');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        const response = await axios.post(`${API_URL}/auth/refresh`, {
          refresh_token: refreshToken,
        });

        const { access_token, refresh_token } = response.data;

        // Update tokens in SecureStore
        await SecureStore.setItemAsync('access_token', access_token);
        await SecureStore.setItemAsync('refresh_token', refresh_token);

        // Update the Authorization header
        originalRequest.headers = {
          ...originalRequest.headers,
          Authorization: `Bearer ${access_token}`,
        };

        // Retry the original request
        return axios(originalRequest);
      } catch (refreshError) {
        // If refresh fails, clear tokens
        await SecureStore.deleteItemAsync('access_token');
        await SecureStore.deleteItemAsync('refresh_token');
        await SecureStore.deleteItemAsync('user');

        // Handle navigation to login in the component that uses this API
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export default api;

// API endpoints
export const endpoints = {
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    refresh: '/auth/refresh',
    me: '/auth/me',
  },
  users: {
    base: '/users',
    byId: (id: string) => `/users/${id}`,
  },
  profiles: {
    base: '/profiles',
    byId: (id: string) => `/profiles/${id}`,
    byUserId: (userId: string) => `/profiles/user/${userId}`,
    updateUserSettings: (userId: string) => `/profiles/user-settings/${userId}`,
  },
  companies: {
    base: '/companies',
    byId: (id: string) => `/companies/${id}`,
    byInviteCode: (code: string) => `/companies/invite/${code}`,
  },
  teams: {
    base: '/teams',
    byId: (id: string) => `/teams/${id}`,
    members: (teamId: string) => `/teams/${teamId}/members`,
    addMember: (teamId: string) => `/teams/${teamId}/members`,
    removeMember: (teamId: string, memberId: string) => `/teams/${teamId}/members/${memberId}`,
  },
  posts: {
    base: '/posts',
    byId: (id: string) => `/posts/${id}`,
    feed: '/posts/feed',
    drafts: '/posts/drafts',
    analyses: '/posts/analyses',
    byTag: (tag: string) => `/posts/tag/${tag}`,
    byTeam: (teamId: string) => `/posts/team/${teamId}`,
    byType: (typeId: string) => `/posts/type/${typeId}`,
  },
  postTypes: {
    base: '/posts/types',
    byId: (id: string) => `/posts/types/${id}`,
    byCompany: (companyId: string) => `/posts/types/company/${companyId}`,
  },
  tags: {
    base: '/posts/tags',
    byId: (id: string) => `/posts/tags/${id}`,
    byCompany: (companyId: string) => `/posts/tags/company/${companyId}`,
  },
  comments: {
    base: '/comments',
    byId: (id: string) => `/comments/${id}`,
    byObject: (objectType: string, objectId: string) => `/comments/${objectType}/${objectId}`,
  },
  summaries: {
    base: '/summaries',
    byId: (id: string) => `/summaries/${id}`,
    generate: '/summaries/generate',
  },
  insights: {
    base: '/insights',
    byId: (id: string) => `/insights/${id}`,
    analyze: '/insights/analyze',
  },
  chat: {
    base: '/chat',
    messages: '/chat/messages',
    send: '/chat/send',
  },
  aiChat: {
    base: '/ai-chat',
    byId: (id: string) => `/ai-chat/${id}`,
    messages: (chatId: string) => `/ai-chat/${chatId}/messages`,
  },
  uploads: {
    base: '/uploads',
    avatar: '/uploads/avatar',
    logo: '/uploads/logo',
    audio: '/uploads/audio',
    audioUpload: '/uploads/audio-upload',
    document: '/uploads/document',
  },
  ai: {
    base: '/ai',
    transcribe: '/ai/transcribe',
  },
};
