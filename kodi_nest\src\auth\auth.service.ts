import { Injectable, UnauthorizedException, ConflictException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { ProfilesService } from '../profiles/profiles.service';
import { CompaniesService } from '../companies/companies.service';
import { RegisterDto } from './dto/register.dto';
import * as bcrypt from 'bcrypt';
import { ObjectId } from 'mongoose';


@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private profilesService: ProfilesService,
    private companiesService: CompaniesService,
    private jwtService: JwtService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    console.log('AuthService.ValidateUser:', email, password);
    const user = await this.usersService.findByEmail(email);
    if (user && await bcrypt.compare(password, user.password)) {
      const { password, ...result } = user.toObject();
      console.log('AuthService.ValidateUser:', result)
      return result;
    }
    console.log('AuthService.ValidateUser: null');
    return null;
  }

  async register(registerDto: RegisterDto) {
    const { email, password, firstName, lastName, inviteCode } = registerDto;
    console.log('AuthService.Register:', registerDto)
    // Check if user already exists
    const existingUser = await this.usersService.findByEmail(email);
    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Find company by invite code if provided
    let companyId: string | null = null;
    if (inviteCode) {
      const company = await this.companiesService.findByInviteCode(inviteCode);
      if (!company) {
        throw new BadRequestException('Invalid invite code');
      }
      companyId = (company._id as ObjectId).toString();
    } else {
      // If no invite code, create a new company for the user
      // Use a default company name if firstName is not provided
      const companyName = firstName ? `${firstName}'s Company` : 'My Company';
      const company = await this.companiesService.create({
        name: companyName,
        createdBy: '', // Will be updated after user creation
      });
      companyId = (company._id as ObjectId).toString();
    }

    // Create user
    const user = await this.usersService.create({
      email,
      password: hashedPassword,
      roles: ['user', 'admin'], // Make the user an admin if they create a company
    });

    // If we created a new company, update the createdBy field
    if (!inviteCode && companyId) {
      await this.companiesService.update(companyId, {
        createdBy: (user._id as ObjectId).toString(),
      });
    }

    // Create profile
    const profile = await this.profilesService.create({
      userId: (user._id as ObjectId).toString(),
      firstName: firstName || '',
      lastName: lastName || '',
      companyId, // Always set companyId since we ensure it's created above
      userSettings: {
        showOnboarder: true,
        onboarderStep: 1,
      },
    });

    // Update user with profile reference
    await this.usersService.update((user._id as ObjectId).toString(), { profile: profile._id } as any);

    // Generate tokens
    return this.generateTokens(user);
  }

  async login(user: any) {
    // Update last login timestamp
    console.log('AuthService.Login:', user)
    await this.usersService.update(user._id.toString(), { lastLogin: new Date() } as any);

    return this.generateTokens(user);
  }

  async refreshToken(refreshToken: string) {
    try {
      // Verify refresh token
      const payload = this.jwtService.verify(refreshToken, {
        secret: process.env.JWT_REFRESH_SECRET,
      });

      // Get user
      const user = await this.usersService.findById(payload.sub);
      if (!user) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Generate new tokens
      return this.generateTokens(user);
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  private generateTokens(user: any) {
    const payload = { email: user.email, sub: user._id, roles: user.roles };

    return {
      access_token: this.jwtService.sign(payload, {
        secret: process.env.JWT_SECRET,
        expiresIn: '1h',
      }),
      refresh_token: this.jwtService.sign(payload, {
        secret: process.env.JWT_REFRESH_SECRET,
        expiresIn: '7d',
      }),
      user: {
        id: user._id,
        email: user.email,
        profile: user.profile,
        roles: user.roles,
      },
    };
  }
}