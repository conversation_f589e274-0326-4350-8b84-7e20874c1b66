# Git
.git
.gitignore

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Build output
dist
build
coverage

# Environment variables
.env
.env.*
!.env.example

# IDE files
.idea
.vscode
*.sublime-project
*.sublime-workspace

# OS files
.DS_Store
Thumbs.db

# Test files
test
coverage
.nyc_output
*.spec.ts
*.test.ts
jest.config.js

# Logs
logs
*.log

# Temporary files
tmp
temp
uploads/*

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# Fly.io
fly.toml
