import { Document, Schema as MongooseSchema } from 'mongoose';
export declare enum CompanyAccountType {
    FREE = 0,
    PAID = 1,
    COMP = 2,
    PREMIUM = 3
}
export type CompanyDocument = Company & Document;
export declare class Company {
    name: string;
    invite_code: string;
    createdBy: MongooseSchema.Types.ObjectId;
    accountType: CompanyAccountType;
    logo: string;
    transformedLogo: {
        small: string;
        medium: string;
        large: string;
    };
    hashtags: string[];
    settings: Record<string, any>;
}
export declare const CompanySchema: MongooseSchema<Company, import("mongoose").Model<Company, any, any, any, Document<unknown, any, Company> & Company & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Company, Document<unknown, {}, import("mongoose").FlatRecord<Company>> & import("mongoose").FlatRecord<Company> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
