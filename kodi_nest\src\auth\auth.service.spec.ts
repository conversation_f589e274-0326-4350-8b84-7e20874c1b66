import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { ProfilesService } from '../profiles/profiles.service';
import { CompaniesService } from '../companies/companies.service';
import * as bcrypt from 'bcrypt';

jest.mock('bcrypt');

describe('AuthService', () => {
  let service: AuthService;
  let usersService: UsersService;
  let jwtService: JwtService;

  const mockUsersService = {
    findByEmail: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  };

  const mockProfilesService = {
    create: jest.fn(),
    findByUserId: jest.fn(),
  };

  const mockCompaniesService = {
    findByInviteCode: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: UsersService, useValue: mockUsersService },
        { provide: ProfilesService, useValue: mockProfilesService },
        { provide: CompaniesService, useValue: mockCompaniesService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get<UsersService>(UsersService);
    jwtService = module.get<JwtService>(JwtService);

    // Default configuration
    mockConfigService.get.mockImplementation((key) => {
      if (key === 'JWT_SECRET') return 'test-secret';
      if (key === 'JWT_REFRESH_SECRET') return 'test-refresh-secret';
      return null;
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateUser', () => {
    it('should return user object when credentials are valid', async () => {
      const mockUser = {
        _id: 'user-id',
        email: '<EMAIL>',
        password: 'hashed-password',
        roles: ['user'],
        toObject: () => ({
          _id: 'user-id',
          email: '<EMAIL>',
          password: 'hashed-password',
          roles: ['user'],
        }),
      };

      mockUsersService.findByEmail.mockResolvedValue(mockUser);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);

      const result = await service.validateUser('<EMAIL>', 'password');

      expect(mockUsersService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(bcrypt.compare).toHaveBeenCalledWith('password', 'hashed-password');
      expect(result).toEqual({
        _id: 'user-id',
        email: '<EMAIL>',
        roles: ['user'],
      });
    });

    it('should return null when user is not found', async () => {
      mockUsersService.findByEmail.mockResolvedValue(null);

      const result = await service.validateUser('<EMAIL>', 'password');

      expect(mockUsersService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(result).toBeNull();
    });

    it('should return null when password is invalid', async () => {
      const mockUser = {
        _id: 'user-id',
        email: '<EMAIL>',
        password: 'hashed-password',
        roles: ['user'],
        toObject: () => ({
          _id: 'user-id',
          email: '<EMAIL>',
          password: 'hashed-password',
          roles: ['user'],
        }),
      };

      mockUsersService.findByEmail.mockResolvedValue(mockUser);
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      const result = await service.validateUser('<EMAIL>', 'wrong-password');

      expect(mockUsersService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(bcrypt.compare).toHaveBeenCalledWith('wrong-password', 'hashed-password');
      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    it('should update last login and return tokens', async () => {
      const mockUser = {
        _id: 'user-id',
        email: '<EMAIL>',
        roles: ['user'],
      };

      mockJwtService.sign.mockImplementation((payload, options) => {
        if (options.expiresIn === '1h') return 'access-token';
        if (options.expiresIn === '7d') return 'refresh-token';
        return '';
      });

      const result = await service.login(mockUser);

      expect(mockUsersService.update).toHaveBeenCalledWith('user-id', expect.objectContaining({
        lastLogin: expect.any(Date),
      }));

      expect(mockJwtService.sign).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        access_token: 'access-token',
        refresh_token: 'refresh-token',
        user: {
          id: 'user-id',
          email: '<EMAIL>',
          profile: undefined,
          roles: ['user'],
        },
      });
    });
  });
});
