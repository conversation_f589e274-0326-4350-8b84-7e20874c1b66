import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import CreatePostForm from './CreatePostForm';
import api from '@/lib/api';
import { server } from '@/test-utils/mocks/server';
import { http, HttpResponse } from 'msw';

// Mock the API
jest.mock('@/lib/api', () => ({
  get: jest.fn(),
  post: jest.fn(),
  endpoints: {
    teams: {
      base: '/teams',
    },
    postTypes: {
      base: '/post-types',
    },
    posts: {
      base: '/posts',
    },
  },
}));

// Mock the AudioRecorder component
jest.mock('./AudioRecorder', () => {
  return function MockAudioRecorder({ onAudioRecorded }: { onAudioRecorded: (audioUrl: string, transcription: string) => void }) {
    return (
      <div data-testid="audio-recorder">
        <button 
          type="button" 
          onClick={() => onAudioRecorded('https://example.com/audio.webm', 'This is a test transcription')}
        >
          Record Audio
        </button>
      </div>
    );
  };
});

// Mock the AuthContext
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn(() => ({
    user: {
      id: 'user-1',
      profile: {
        companyId: 'company-1',
      },
    },
  })),
}));

describe('CreatePostForm', () => {
  const mockOnPostCreated = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock API responses
    (api.get as jest.Mock).mockImplementation((url) => {
      if (url.includes('teams')) {
        return Promise.resolve({
          data: [
            { _id: 'team-1', name: 'Engineering' },
            { _id: 'team-2', name: 'Marketing' },
          ],
        });
      }
      if (url.includes('post-types')) {
        return Promise.resolve({
          data: [
            { _id: 'type-1', name: 'Strategic feedback', recordTips: '<ul><li>Tip 1</li></ul>' },
            { _id: 'type-2', name: 'Product QA' },
          ],
        });
      }
      return Promise.reject(new Error('Unknown URL'));
    });
    
    (api.post as jest.Mock).mockResolvedValue({ data: { _id: 'post-1' } });
  });

  it('renders the create post form', async () => {
    render(<CreatePostForm onPostCreated={mockOnPostCreated} />);
    
    // Check for form elements
    expect(screen.getByText(/Create Post/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Team \(optional\)/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Post Type \(optional\)/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/What's on your mind\?/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Publish immediately/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Save Draft/i })).toBeInTheDocument();
    
    // Check that API calls were made to fetch teams and post types
    await waitFor(() => {
      expect(api.get).toHaveBeenCalledTimes(2);
      expect(api.get).toHaveBeenCalledWith(expect.stringContaining('/teams?companyId=company-1'));
      expect(api.get).toHaveBeenCalledWith(expect.stringContaining('/post-types?companyId=company-1'));
    });
    
    // Check that teams and post types are populated
    await waitFor(() => {
      expect(screen.getByText('Engineering')).toBeInTheDocument();
      expect(screen.getByText('Marketing')).toBeInTheDocument();
      expect(screen.getByText('Strategic feedback')).toBeInTheDocument();
      expect(screen.getByText('Product QA')).toBeInTheDocument();
    });
  });

  it('handles form submission with valid data', async () => {
    const user = userEvent.setup();
    
    render(<CreatePostForm onPostCreated={mockOnPostCreated} />);
    
    // Wait for teams and post types to load
    await waitFor(() => {
      expect(screen.getByText('Engineering')).toBeInTheDocument();
    });
    
    // Fill in the form
    await user.type(screen.getByLabelText(/What's on your mind\?/i), 'This is a test post');
    
    // Select a team
    await user.selectOptions(screen.getByLabelText(/Team \(optional\)/i), 'team-1');
    
    // Select a post type
    await user.selectOptions(screen.getByLabelText(/Post Type \(optional\)/i), 'type-1');
    
    // Check the publish checkbox
    await user.click(screen.getByLabelText(/Publish immediately/i));
    
    // Submit the form
    await user.click(screen.getByRole('button', { name: /Post/i }));
    
    // Check that API call was made with correct data
    await waitFor(() => {
      expect(api.post).toHaveBeenCalledWith('/posts', {
        body: 'This is a test post',
        teamId: 'team-1',
        postTypeId: 'type-1',
        companyId: 'company-1',
        userId: 'user-1',
        isDraft: false,
        audioUrl: undefined,
      });
    });
    
    // Check that onPostCreated callback was called
    expect(mockOnPostCreated).toHaveBeenCalled();
    
    // Check that form was reset
    expect(screen.getByLabelText(/What's on your mind\?/i)).toHaveValue('');
  });

  it('shows error when submitting empty form', async () => {
    const user = userEvent.setup();
    
    render(<CreatePostForm onPostCreated={mockOnPostCreated} />);
    
    // Submit the form without filling it
    await user.click(screen.getByRole('button', { name: /Save Draft/i }));
    
    // Check for error message
    expect(screen.getByText(/Post content cannot be empty/i)).toBeInTheDocument();
    
    // Check that API call was not made
    expect(api.post).not.toHaveBeenCalled();
  });

  it('handles API error during submission', async () => {
    const user = userEvent.setup();
    
    // Mock API error
    (api.post as jest.Mock).mockRejectedValue({
      response: {
        data: {
          message: 'Server error',
        },
      },
    });
    
    render(<CreatePostForm onPostCreated={mockOnPostCreated} />);
    
    // Fill in the form
    await user.type(screen.getByLabelText(/What's on your mind\?/i), 'This is a test post');
    
    // Submit the form
    await user.click(screen.getByRole('button', { name: /Save Draft/i }));
    
    // Check for error message
    await waitFor(() => {
      expect(screen.getByText(/Server error/i)).toBeInTheDocument();
    });
    
    // Check that onPostCreated callback was not called
    expect(mockOnPostCreated).not.toHaveBeenCalled();
  });

  it('handles audio recording', async () => {
    const user = userEvent.setup();
    
    render(<CreatePostForm onPostCreated={mockOnPostCreated} />);
    
    // Click the record audio button
    await user.click(screen.getByText(/Record Audio/i));
    
    // Check that the transcription is displayed
    await waitFor(() => {
      expect(screen.getByLabelText(/Transcription \(you can edit this\)/i)).toHaveValue('This is a test transcription');
    });
    
    // Submit the form
    await user.click(screen.getByRole('button', { name: /Save Draft/i }));
    
    // Check that API call was made with correct data including audio URL
    await waitFor(() => {
      expect(api.post).toHaveBeenCalledWith('/posts', expect.objectContaining({
        body: 'This is a test transcription',
        audioUrl: 'https://example.com/audio.webm',
      }));
    });
  });

  it('shows recording tips when post type with tips is selected', async () => {
    const user = userEvent.setup();
    
    render(<CreatePostForm onPostCreated={mockOnPostCreated} />);
    
    // Wait for post types to load
    await waitFor(() => {
      expect(screen.getByText('Strategic feedback')).toBeInTheDocument();
    });
    
    // Select a post type with recording tips
    await user.selectOptions(screen.getByLabelText(/Post Type \(optional\)/i), 'type-1');
    
    // Check that the recording tips are passed to the AudioRecorder
    await waitFor(() => {
      const audioRecorder = screen.getByTestId('audio-recorder');
      expect(audioRecorder).toHaveAttribute('recordtips', '<ul><li>Tip 1</li></ul>');
    });
  });
});
