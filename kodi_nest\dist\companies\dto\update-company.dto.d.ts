import { CreateCompanyDto } from './create-company.dto';
import { CompanyAccountType } from '../schemas/company.schema';
declare const UpdateCompanyDto_base: import("@nestjs/mapped-types").MappedType<Partial<CreateCompanyDto>>;
export declare class UpdateCompanyDto extends UpdateCompanyDto_base {
    name?: string;
    accountType?: CompanyAccountType;
    logo?: string;
    transformedLogo?: {
        small: string;
        medium: string;
        large: string;
    };
    hashtags?: string[];
    settings?: Record<string, any>;
}
export {};
