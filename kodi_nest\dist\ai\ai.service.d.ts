import { Model } from 'mongoose';
import { SummaryDocument } from './schemas/summary.schema';
import { InsightDocument } from './schemas/insight.schema';
import { AiPromptDocument } from './schemas/ai-prompt.schema';
import { AiChatDocument } from './schemas/ai-chat.schema';
import { AppSettingsDocument } from './schemas/app-settings.schema';
import { CreateSummaryDto } from './dto/create-summary.dto';
import { CreateInsightDto } from './dto/create-insight.dto';
import { CreateAiPromptDto } from './dto/create-ai-prompt.dto';
import { CreateAiChatDto } from './dto/create-ai-chat.dto';
import { CreateAppSettingsDto } from './dto/create-app-settings.dto';
import { TranscribeAudioDto } from './dto/transcribe-audio.dto';
import { PostsService } from '../posts/posts.service';
export declare class AiService {
    private summaryModel;
    private insightModel;
    private aiPromptModel;
    private aiChatModel;
    private appSettingsModel;
    private postsService;
    constructor(summaryModel: Model<SummaryDocument>, insightModel: Model<InsightDocument>, aiPromptModel: Model<AiPromptDocument>, aiChatModel: Model<AiChatDocument>, appSettingsModel: Model<AppSettingsDocument>, postsService: PostsService);
    createSummary(createSummaryDto: CreateSummaryDto): Promise<SummaryDocument>;
    findAllSummaries(userId?: string, companyId?: string, tag?: string): Promise<SummaryDocument[]>;
    findSummaryById(id: string): Promise<SummaryDocument>;
    updateSummary(id: string, updateSummaryDto: any): Promise<SummaryDocument>;
    removeSummary(id: string): Promise<SummaryDocument>;
    generateSummary(createSummaryDto: CreateSummaryDto): Promise<SummaryDocument>;
    createInsight(createInsightDto: CreateInsightDto): Promise<InsightDocument>;
    findAllInsights(userId?: string, companyId?: string, tag?: string): Promise<InsightDocument[]>;
    findInsightById(id: string): Promise<InsightDocument>;
    updateInsight(id: string, updateInsightDto: any): Promise<InsightDocument>;
    removeInsight(id: string): Promise<InsightDocument>;
    analyzeInsight(createInsightDto: CreateInsightDto): Promise<InsightDocument>;
    createAiPrompt(createAiPromptDto: CreateAiPromptDto): Promise<AiPromptDocument>;
    findAllAiPrompts(companyId?: string, isSystem?: boolean): Promise<AiPromptDocument[]>;
    findAiPromptById(id: string): Promise<AiPromptDocument>;
    updateAiPrompt(id: string, updateAiPromptDto: any): Promise<AiPromptDocument>;
    removeAiPrompt(id: string): Promise<AiPromptDocument>;
    createAiChat(createAiChatDto: CreateAiChatDto): Promise<AiChatDocument>;
    findAllAiChats(userId: string): Promise<AiChatDocument[]>;
    findAiChatById(id: string): Promise<AiChatDocument>;
    updateAiChat(id: string, updateAiChatDto: any): Promise<AiChatDocument>;
    removeAiChat(id: string): Promise<AiChatDocument>;
    sendChatMessage(chatId: string, message: string, userId: string): Promise<AiChatDocument>;
    createAppSettings(createAppSettingsDto: CreateAppSettingsDto): Promise<AppSettingsDocument>;
    findAllAppSettings(type?: string, companyId?: string): Promise<AppSettingsDocument[]>;
    findAppSettingsById(id: string): Promise<AppSettingsDocument>;
    updateAppSettings(id: string, updateAppSettingsDto: any): Promise<AppSettingsDocument>;
    removeAppSettings(id: string): Promise<AppSettingsDocument>;
    transcribeAudio(transcribeAudioDto: TranscribeAudioDto): Promise<{
        text: string;
    }>;
}
