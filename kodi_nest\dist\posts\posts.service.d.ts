import { OnModuleInit } from '@nestjs/common';
import { Model } from 'mongoose';
import { PostDocument } from './schemas/post.schema';
import { PostTypeDocument } from './schemas/post-type.schema';
import { TagDocument } from './schemas/tag.schema';
import { CommentDocument } from './schemas/comment.schema';
import { CreatePostDto } from './dto/create-post.dto';
import { UpdatePostDto } from './dto/update-post.dto';
import { CreatePostTypeDto } from './dto/create-post-type.dto';
import { CreateTagDto } from './dto/create-tag.dto';
import { CreateCommentDto } from './dto/create-comment.dto';
import { ModuleRef } from '@nestjs/core';
export declare class PostsService implements OnModuleInit {
    private postModel;
    private postTypeModel;
    private tagModel;
    private commentModel;
    private moduleRef;
    private companiesService;
    constructor(postModel: Model<PostDocument>, postTypeModel: Model<PostTypeDocument>, tagModel: Model<TagDocument>, commentModel: Model<CommentDocument>, moduleRef: ModuleRef);
    onModuleInit(): void;
    createPost(createPostDto: CreatePostDto): Promise<PostDocument>;
    findAllPosts(companyId?: string, teamId?: string, postTypeId?: string, tag?: string, isDraft?: boolean): Promise<PostDocument[]>;
    findPostById(id: string): Promise<PostDocument>;
    updatePost(id: string, updatePostDto: UpdatePostDto): Promise<PostDocument>;
    removePost(id: string): Promise<PostDocument>;
    createPostType(createPostTypeDto: CreatePostTypeDto): Promise<PostTypeDocument>;
    findAllPostTypes(companyId?: string): Promise<PostTypeDocument[]>;
    findPostTypeById(id: string): Promise<PostTypeDocument>;
    updatePostType(id: string, updatePostTypeDto: any): Promise<PostTypeDocument>;
    removePostType(id: string): Promise<PostTypeDocument>;
    createTag(createTagDto: CreateTagDto): Promise<TagDocument>;
    findAllTags(companyId?: string): Promise<TagDocument[]>;
    findTagById(id: string): Promise<TagDocument>;
    updateTag(id: string, updateTagDto: any): Promise<TagDocument>;
    removeTag(id: string): Promise<TagDocument>;
    createComment(createCommentDto: CreateCommentDto): Promise<CommentDocument>;
    findAllComments(objectType: string, objectId: string): Promise<CommentDocument[]>;
    findCommentById(id: string): Promise<CommentDocument>;
    updateComment(id: string, updateCommentDto: any): Promise<CommentDocument>;
    removeComment(id: string): Promise<CommentDocument | null>;
    private extractHashtags;
    private updateTags;
}
