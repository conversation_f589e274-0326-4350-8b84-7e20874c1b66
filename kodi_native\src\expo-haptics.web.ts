/**
 * Web implementation of expo-haptics
 * This provides a no-op implementation for web platforms
 */

// Define the interface to match the native Haptics API
export enum ImpactFeedbackStyle {
  Light = 'light',
  Medium = 'medium',
  Heavy = 'heavy',
}

export enum NotificationFeedbackType {
  Success = 'success',
  Warning = 'warning',
  Error = 'error',
}

// Create the web implementation (no-op)
const ExpoHaptics = {
  // Impact feedback is not available on web
  impactAsync(style: ImpactFeedbackStyle = ImpactFeedbackStyle.Medium): Promise<void> {
    console.log(`[Web] Haptic impact feedback: ${style}`);
    return Promise.resolve();
  },

  // Notification feedback is not available on web
  notificationAsync(type: NotificationFeedbackType = NotificationFeedbackType.Success): Promise<void> {
    console.log(`[Web] Haptic notification feedback: ${type}`);
    return Promise.resolve();
  },

  // Selection feedback is not available on web
  selectionAsync(): Promise<void> {
    console.log('[Web] Haptic selection feedback');
    return Promise.resolve();
  },

  // Vibration is not available on web
  vibrate(): Promise<void> {
    console.log('[Web] Haptic vibration');
    return Promise.resolve();
  },
};

export default ExpoHaptics;
