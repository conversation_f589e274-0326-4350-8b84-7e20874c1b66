'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import Link from 'next/link';

interface Team {
  _id: string;
  name: string;
  description?: string;
  companyId: string;
  memberCount?: number;
}

export default function TeamsSettingsPage() {
  const { user } = useAuth();
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (user?.profile?.companyId) {
      fetchTeams(user.profile.companyId);
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchTeams = async (companyId: string) => {
    try {
      setLoading(true);
      const { data } = await api.get(`${endpoints.teams.base}?companyId=${companyId}`);
      setTeams(data);
    } catch (err: any) {
      console.error('Failed to fetch teams:', err);
      setError('Failed to load teams. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-5xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Teams</h1>
          <div className="flex space-x-4">
            <Link 
              href="/settings"
              className="text-sm text-indigo-600 hover:text-indigo-800"
            >
              Back to Settings
            </Link>
            {user?.roles?.includes('admin') && (
              <Link
                href="/settings/teams/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Create Team
              </Link>
            )}
          </div>
        </div>

        {error && (
          <div className="bg-red-50 p-4 rounded-md mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {teams.length > 0 ? (
                teams.map((team) => (
                  <li key={team._id}>
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{team.name}</div>
                          {team.description && (
                            <div className="text-sm text-gray-500">{team.description}</div>
                          )}
                        </div>
                        <div className="flex items-center">
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            {team.memberCount || 0} members
                          </span>
                          {user?.roles?.includes('admin') && (
                            <Link
                              href={`/settings/teams/${team._id}`}
                              className="ml-4 text-sm text-indigo-600 hover:text-indigo-900"
                            >
                              Manage
                            </Link>
                          )}
                        </div>
                      </div>
                    </div>
                  </li>
                ))
              ) : (
                <li className="px-4 py-5 sm:px-6">
                  <div className="text-center text-gray-500">
                    No teams found
                  </div>
                </li>
              )}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
