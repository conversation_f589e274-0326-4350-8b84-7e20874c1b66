"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const app_module_1 = require("./app.module");
const path_1 = require("path");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.useStaticAssets((0, path_1.join)(process.cwd(), 'uploads'), {
        prefix: '/uploads',
    });
    const developmentOrigins = [
        'http://localhost:3010',
        'http://localhost:3002',
        'http://localhost:19000',
        'http://localhost:19006',
        'http://localhost:8081',
        /^http:\/\/localhost:(19\d{3}|8\d{3})$/,
        /^http:\/\/192\.168\.\d+\.\d+:(19\d{3}|8\d{3}|3010|3002)$/,
        /^http:\/\/\d+\.\d+\.\d+\.\d+:(19\d{3}|8\d{3}|3010|3002)$/,
    ];
    app.enableCors({
        origin: process.env.NODE_ENV === 'production'
            ? [
                process.env.FRONTEND_URL || 'http://localhost:3010',
                process.env.NATIVE_URL || 'http://localhost:19000',
            ].filter(Boolean)
            : developmentOrigins,
        methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
        credentials: true,
        allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
        exposedHeaders: ['Content-Disposition'],
    });
    const apiPrefix = process.env.API_PREFIX || 'api';
    app.setGlobalPrefix(apiPrefix, {
        exclude: ['/health'],
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
    }));
    const port = parseInt(process.env.PORT || '3011', 10);
    const host = process.env.HOST || '0.0.0.0';
    console.log(`Attempting to start server on ${host}:${port}`);
    await app.listen(port, host);
    const serverUrl = await app.getUrl();
    console.log(`Application is running on: ${serverUrl}/${apiPrefix}`);
}
bootstrap();
//# sourceMappingURL=main.js.map