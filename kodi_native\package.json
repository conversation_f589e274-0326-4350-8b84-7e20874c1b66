{"name": "kodi_native", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "npx expo start", "reset-project": "node ./scripts/reset-project.js", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web", "build:web": "npx expo export:web", "lint": "npx expo lint", "outdated": "npm outdated", "update": "npm update"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "axios": "^1.6.7", "expo": "^53.0.9", "expo-av": "~15.1.4", "expo-blur": "~14.1.4", "expo-constants": "~17.1.5", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.4", "expo-router": "~5.0.6", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/webpack-config": "^19.0.0", "@react-native-community/cli": "^18.0.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.0", "cross-env": "^7.0.3", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "expo-cli": "^6.3.10", "react-native-svg-app-icon": "^0.6.1", "react-native-svg-transformer": "^1.3.0", "typescript": "~5.8.3"}, "overrides": {"glob": "^10.3.10", "rimraf": "^5.0.5"}, "resolutions": {"glob": "^10.3.10", "rimraf": "^5.0.5"}, "private": true}