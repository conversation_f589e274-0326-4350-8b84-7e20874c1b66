import { http, HttpResponse } from 'msw';

// Define handlers for mock API responses
export const handlers = [
  // Auth endpoints
  http.post('*/api/auth/login', () => {
    return HttpResponse.json({
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      user: {
        id: 'user-1',
        email: '<EMAIL>',
        roles: ['user'],
      },
    });
  }),

  http.get('*/api/auth/me', () => {
    return HttpResponse.json({
      id: 'user-1',
      email: '<EMAIL>',
      roles: ['user'],
      profile: {
        id: 'profile-1',
        userId: 'user-1',
        firstName: 'Test',
        lastName: 'User',
        companyId: 'company-1',
      },
    });
  }),

  // Posts endpoints
  http.get('*/api/posts/feed', () => {
    return HttpResponse.json([
      {
        id: 'post-1',
        title: 'Test Post 1',
        content: 'This is test post 1',
        userId: 'user-1',
        companyId: 'company-1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'post-2',
        title: 'Test Post 2',
        content: 'This is test post 2',
        userId: 'user-2',
        companyId: 'company-1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ]);
  }),

  // Companies endpoints
  http.get('*/api/companies/:id', ({ params }) => {
    return HttpResponse.json({
      id: params.id,
      name: 'Test Company',
      description: 'This is a test company',
      inviteCode: 'TEST123',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
  }),

  // Teams endpoints
  http.get('*/api/teams', () => {
    return HttpResponse.json([
      {
        id: 'team-1',
        name: 'Engineering',
        description: 'Engineering team',
        companyId: 'company-1',
      },
      {
        id: 'team-2',
        name: 'Marketing',
        description: 'Marketing team',
        companyId: 'company-1',
      },
    ]);
  }),

  // Post types endpoints
  http.get('*/api/post-types', () => {
    return HttpResponse.json([
      {
        id: 'post-type-1',
        name: 'Strategic Feedback',
        description: 'Strategic feedback for the company',
        companyId: 'company-1',
      },
      {
        id: 'post-type-2',
        name: 'Product QA',
        description: 'Quality assurance for products',
        companyId: 'company-1',
      },
    ]);
  }),

  // Tags endpoints
  http.get('*/api/tags', () => {
    return HttpResponse.json([
      {
        id: 'tag-1',
        name: 'Important',
        companyId: 'company-1',
      },
      {
        id: 'tag-2',
        name: 'Urgent',
        companyId: 'company-1',
      },
    ]);
  }),

  // Fallback for unhandled requests
  http.all('*', ({ request }) => {
    console.error(`Unhandled request: ${request.method} ${request.url}`);
    return HttpResponse.error();
  }),
];
