# Kodi Application

This project is a modern reimplementation of a legacy Meteor application, using Next.js for the frontend and NestJS for the backend.

## Project Structure

- `legacy/` - The original Meteor application (reference only)
- `kodi_next/` - Next.js frontend application
- `kodi_nest/` - NestJS backend API

## Features

- User authentication and profile management
- Company management with invite codes
- Team management and memberships
- Post creation and management with tags
- Feed display with filtering capabilities
- Summaries and insights generation
- Audio recording and file uploads
- AI-powered analysis and chat functionality

## Technology Stack

### Frontend (kodi_next)
- Next.js 14+
- React 18+
- TypeScript
- Tailwind CSS
- Geist font
- Axios for API requests

### Backend (kodi_nest)
- NestJS 10+
- TypeScript
- MongoDB with Mongoose
- JWT Authentication
- AWS S3 for file storage

## Getting Started

### Prerequisites
- Node.js 18+
- MongoDB
- AWS account (for S3 file storage)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/kodi.git
cd kodi
```

2. Install dependencies for both projects:
```bash
# Install Next.js frontend dependencies
cd kodi_next
npm install

# Install NestJS backend dependencies
cd ../kodi_nest
npm install
```

3. Set up environment variables:
   - Create `.env.local` in the `kodi_next` directory
   - Create `.env` in the `kodi_nest` directory

4. Start the development servers:
```bash
# Start the NestJS backend
cd kodi_nest
npm run start:dev

# In a separate terminal, start the Next.js frontend
cd kodi_next
npm run dev
```

5. Open your browser and navigate to:
   - Frontend: http://localhost:3010
   - Backend API: http://localhost:3011/api

## Database Collections

All collections are prefixed with 'kd_' to segregate data in the database:

- kd_users - User accounts
- kd_profiles - User profiles
- kd_companies - Company information
- kd_teams - Teams within companies
- kd_memberships - Team memberships
- kd_posts - User posts
- kd_post_types - Post categories
- kd_tags - Hashtags and tags
- kd_comments - Comments on posts and other content
- kd_summaries - AI-generated summaries
- kd_insights - AI-generated insights
- kd_ai_prompts - Templates for AI generation
- kd_ai_chats - Chat conversations with AI
- kd_file_uploads - Uploaded files metadata
- kd_app_settings - Application settings

## License

This project is licensed under the MIT License - see the LICENSE file for details.
