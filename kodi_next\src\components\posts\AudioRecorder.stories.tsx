import type { Meta, StoryObj } from '@storybook/react';
import AudioRecorder from './AudioRecorder';

const meta: Meta<typeof AudioRecorder> = {
  title: 'Components/Posts/AudioRecorder',
  component: AudioRecorder,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof AudioRecorder>;

export const Default: Story = {
  args: {
    onAudioRecorded: (audioUrl, transcription) => {
      console.log('Audio URL:', audioUrl);
      console.log('Transcription:', transcription);
    },
  },
};

export const WithRecordingTips: Story = {
  args: {
    onAudioRecorded: (audioUrl, transcription) => {
      console.log('Audio URL:', audioUrl);
      console.log('Transcription:', transcription);
    },
    recordTips: `
      <ul>
        <li>Speak clearly and at a moderate pace</li>
        <li>Minimize background noise</li>
        <li>Keep the microphone at a consistent distance</li>
        <li>Pause briefly between different thoughts or sections</li>
        <li>Summarize key points at the beginning or end</li>
      </ul>
    `,
  },
};
