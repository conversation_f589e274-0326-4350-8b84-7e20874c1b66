import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type TagDocument = Tag & Document;

@Schema({
  collection: 'kd_tags',
  timestamps: true,
})
export class Tag {
  @Prop({ required: true, index: true })
  name: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company', required: false, index: true })
  companyId: MongooseSchema.Types.ObjectId;

  @Prop({ type: Number, default: 0, index: true })
  count: number;

  @Prop({ required: false })
  highlightColor: string;
}

export const TagSchema = SchemaFactory.createForClass(Tag);
