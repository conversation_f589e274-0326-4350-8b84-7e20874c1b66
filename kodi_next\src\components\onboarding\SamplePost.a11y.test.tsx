import React from 'react';
import { render } from '@testing-library/react';
import { axe, runAxe } from '@/test-utils/axe-setup';
import SamplePost from './SamplePost';

describe('SamplePost Accessibility', () => {
  it('should not have accessibility violations', async () => {
    const { container } = render(<SamplePost companyName="Test Company" />);
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });

  it('should not have accessibility violations with a long company name', async () => {
    const longCompanyName = 'Super Long Company Name That Might Wrap Around The Screen Incorporated';
    const { container } = render(<SamplePost companyName={longCompanyName} />);
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });
});
