import { Test, TestingModule } from '@nestjs/testing';
import { AiService } from './ai.service';
import { getModelToken } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { Summary } from './schemas/summary.schema';
import { Insight } from './schemas/insight.schema';
import { AiPrompt } from './schemas/ai-prompt.schema';
import { AiChat } from './schemas/ai-chat.schema';
import { AppSettings } from './schemas/app-settings.schema';
import { PostsService } from '../posts/posts.service';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('AiService - AppSettings', () => {
  let service: AiService;
  let appSettingsModel: Model<AppSettings>;

  const mockSummaryModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockInsightModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockAiPromptModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockAiChatModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockAppSettingsModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockPostsService = {
    findPostById: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiService,
        {
          provide: getModelToken(Summary.name),
          useValue: mockSummaryModel,
        },
        {
          provide: getModelToken(Insight.name),
          useValue: mockInsightModel,
        },
        {
          provide: getModelToken(AiPrompt.name),
          useValue: mockAiPromptModel,
        },
        {
          provide: getModelToken(AiChat.name),
          useValue: mockAiChatModel,
        },
        {
          provide: getModelToken(AppSettings.name),
          useValue: mockAppSettingsModel,
        },
        {
          provide: PostsService,
          useValue: mockPostsService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AiService>(AiService);
    appSettingsModel = module.get<Model<AppSettings>>(getModelToken(AppSettings.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAppSettings', () => {
    it('should return app settings for a company', async () => {
      const mockSettings = {
        _id: 'settings1',
        companyId: 'company1',
        aiSettings: {
          model: 'gpt-4',
          temperature: 0.7,
          maxTokens: 2000,
        },
      };

      mockAppSettingsModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockSettings),
      });

      const result = await service.getAppSettings('company1');

      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ companyId: 'company1' });
      expect(result).toEqual(mockSettings);
    });

    it('should create default settings if none exist', async () => {
      // First call returns null (no settings found)
      mockAppSettingsModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      const defaultSettings = {
        _id: 'settings1',
        companyId: 'company1',
        aiSettings: {
          model: 'gpt-3.5-turbo',
          temperature: 0.7,
          maxTokens: 1000,
        },
      };

      // Mock the create method
      mockAppSettingsModel.create.mockResolvedValue(defaultSettings);

      const result = await service.getAppSettings('company1');

      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ companyId: 'company1' });
      expect(mockAppSettingsModel.create).toHaveBeenCalledWith({
        companyId: 'company1',
        aiSettings: expect.any(Object),
      });
      expect(result).toEqual(defaultSettings);
    });
  });

  describe('updateAppSettings', () => {
    it('should update existing app settings', async () => {
      const existingSettings = {
        _id: 'settings1',
        companyId: 'company1',
        aiSettings: {
          model: 'gpt-3.5-turbo',
          temperature: 0.7,
          maxTokens: 1000,
        },
      };

      const updateSettingsDto = {
        aiSettings: {
          model: 'gpt-4',
          temperature: 0.8,
          maxTokens: 2000,
        },
      };

      const updatedSettings = {
        _id: 'settings1',
        companyId: 'company1',
        aiSettings: {
          model: 'gpt-4',
          temperature: 0.8,
          maxTokens: 2000,
        },
      };

      mockAppSettingsModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingSettings),
      });

      mockAppSettingsModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedSettings),
      });

      const result = await service.updateAppSettings('company1', updateSettingsDto);

      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ companyId: 'company1' });
      expect(mockAppSettingsModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'settings1',
        updateSettingsDto,
        { new: true }
      );
      expect(result).toEqual(updatedSettings);
    });

    it('should create settings if none exist', async () => {
      // First call returns null (no settings found)
      mockAppSettingsModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      const updateSettingsDto = {
        aiSettings: {
          model: 'gpt-4',
          temperature: 0.8,
          maxTokens: 2000,
        },
      };

      const createdSettings = {
        _id: 'settings1',
        companyId: 'company1',
        ...updateSettingsDto,
      };

      // Mock the create method
      mockAppSettingsModel.create.mockResolvedValue(createdSettings);

      const result = await service.updateAppSettings('company1', updateSettingsDto);

      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ companyId: 'company1' });
      expect(mockAppSettingsModel.create).toHaveBeenCalledWith({
        companyId: 'company1',
        ...updateSettingsDto,
      });
      expect(result).toEqual(createdSettings);
    });

    it('should validate AI model settings', async () => {
      const updateSettingsDto = {
        aiSettings: {
          model: 'invalid-model', // Invalid model
          temperature: 2.0,       // Invalid temperature (> 1.0)
          maxTokens: -100,        // Invalid maxTokens (negative)
        },
      };

      await expect(service.updateAppSettings('company1', updateSettingsDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('resetAppSettings', () => {
    it('should reset app settings to defaults', async () => {
      const existingSettings = {
        _id: 'settings1',
        companyId: 'company1',
        aiSettings: {
          model: 'gpt-4',
          temperature: 0.8,
          maxTokens: 2000,
        },
      };

      const defaultSettings = {
        _id: 'settings1',
        companyId: 'company1',
        aiSettings: {
          model: 'gpt-3.5-turbo',
          temperature: 0.7,
          maxTokens: 1000,
        },
      };

      mockAppSettingsModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingSettings),
      });

      mockAppSettingsModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(defaultSettings),
      });

      const result = await service.resetAppSettings('company1');

      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ companyId: 'company1' });
      expect(mockAppSettingsModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'settings1',
        {
          aiSettings: expect.any(Object),
        },
        { new: true }
      );
      expect(result).toEqual(defaultSettings);
    });

    it('should create default settings if none exist', async () => {
      // First call returns null (no settings found)
      mockAppSettingsModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      const defaultSettings = {
        _id: 'settings1',
        companyId: 'company1',
        aiSettings: {
          model: 'gpt-3.5-turbo',
          temperature: 0.7,
          maxTokens: 1000,
        },
      };

      // Mock the create method
      mockAppSettingsModel.create.mockResolvedValue(defaultSettings);

      const result = await service.resetAppSettings('company1');

      expect(mockAppSettingsModel.findOne).toHaveBeenCalledWith({ companyId: 'company1' });
      expect(mockAppSettingsModel.create).toHaveBeenCalledWith({
        companyId: 'company1',
        aiSettings: expect.any(Object),
      });
      expect(result).toEqual(defaultSettings);
    });
  });
});
