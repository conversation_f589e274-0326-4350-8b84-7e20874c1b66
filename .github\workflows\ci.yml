name: CI/CD Pipeline

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'kodi_next/package.json'

      - name: Install dependencies
        run: |
          cd kodi_next
          npm ci

      - name: Run linter
        run: |
          cd kodi_next
          npm run lint

      - name: Run tests
        run: |
          cd kodi_next
          npm test

      - name: Run Storybook tests
        run: |
          cd kodi_next
          npm run test-storybook -- --ci

      - name: Build Storybook
        run: |
          cd kodi_next
          npm run build-storybook

      - name: Run Lighthouse CI
        run: |
          cd kodi_next
          npm run lighthouse

      - name: Upload test coverage
        uses: actions/upload-artifact@v3
        with:
          name: frontend-coverage
          path: kodi_next/coverage

  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'kodi_nest/package.json'

      - name: Install dependencies
        run: |
          cd kodi_nest
          npm ci

      - name: Run linter
        run: |
          cd kodi_nest
          npm run lint

      - name: Run tests
        run: |
          cd kodi_nest
          npm test

      - name: Run E2E tests
        run: |
          cd kodi_nest
          npm run test:e2e

      - name: Upload test coverage
        uses: actions/upload-artifact@v3
        with:
          name: backend-coverage
          path: kodi_nest/coverage

  visual-regression-tests:
    runs-on: ubuntu-latest
    needs: [frontend-tests]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'kodi_next/package.json'

      - name: Install dependencies
        run: |
          cd kodi_next
          npm ci

      - name: Publish to Chromatic
        uses: chromaui/action@v1
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          workingDir: kodi_next
          buildScriptName: build-storybook

  performance-tests:
    runs-on: ubuntu-latest
    needs: [frontend-tests, backend-tests]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install k6
        run: |
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Start backend server
        run: |
          cd kodi_nest
          npm ci
          npm run build
          npm run start &
          sleep 10

      - name: Run backend performance tests
        run: |
          k6 run kodi_nest/performance/auth.js
          k6 run kodi_nest/performance/posts.js
