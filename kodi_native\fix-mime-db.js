const fs = require('fs');
const path = require('path');

console.log('Starting mime-db fix script...');

// Path to the mime-db directory
const mimeDbPath = path.join(__dirname, 'node_modules', 'mime-db');

// Check if the mime-db directory exists
if (!fs.existsSync(mimeDbPath)) {
  console.log('mime-db directory not found. Creating it...');
  fs.mkdirSync(mimeDbPath, { recursive: true });
}

// Create the db.json file
const dbJsonPath = path.join(mimeDbPath, 'db.json');
const dbJsonContent = JSON.stringify({
  "application/json": {
    "source": "iana",
    "charset": "UTF-8",
    "compressible": true,
    "extensions": ["json", "map"]
  },
  "application/javascript": {
    "source": "iana",
    "charset": "UTF-8",
    "compressible": true,
    "extensions": ["js", "mjs"]
  },
  "text/plain": {
    "source": "iana",
    "compressible": true,
    "extensions": ["txt", "text", "conf", "def", "list", "log", "in", "ini"]
  },
  "text/html": {
    "source": "iana",
    "compressible": true,
    "extensions": ["html", "htm", "shtml"]
  },
  "image/jpeg": {
    "source": "iana",
    "compressible": false,
    "extensions": ["jpeg", "jpg", "jpe"]
  },
  "image/png": {
    "source": "iana",
    "compressible": false,
    "extensions": ["png"]
  }
});

console.log(`Creating ${dbJsonPath}...`);
fs.writeFileSync(dbJsonPath, dbJsonContent);
console.log(`Created ${dbJsonPath}`);

// Update the package.json to include the fix script
const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = require(packageJsonPath);

if (!packageJson.scripts.postinstall.includes('node fix-mime-db.js')) {
  packageJson.scripts.postinstall = `${packageJson.scripts.postinstall} && node fix-mime-db.js`;
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('Updated package.json to include the fix-mime-db.js script in postinstall');
}

console.log('mime-db fix completed successfully!');
