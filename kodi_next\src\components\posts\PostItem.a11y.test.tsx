import React from 'react';
import { render } from '@testing-library/react';
import { axe, runAxe } from '@/test-utils/axe-setup';
import PostItem from './PostItem';

describe('PostItem Accessibility', () => {
  const mockPost = {
    _id: 'post-1',
    body: 'This is a test post with #hashtag',
    userId: 'user-1',
    createdAt: '2023-01-01T12:00:00Z',
    hashtags: ['hashtag'],
    user: {
      firstName: 'John',
      lastName: 'Doe',
    },
  };

  it('should not have accessibility violations', async () => {
    const { container } = render(<PostItem post={mockPost} />);
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });

  it('should not have accessibility violations with enhanced body', async () => {
    const postWithEnhancedBody = {
      ...mockPost,
      enhancedBody: '<p>This is <strong>enhanced</strong> content</p>',
    };
    
    const { container } = render(<PostItem post={postWithEnhancedBody} />);
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });

  it('should not have accessibility violations with audio', async () => {
    const postWithAudio = {
      ...mockPost,
      audioUrl: 'https://example.com/audio.webm',
    };
    
    const { container } = render(<PostItem post={postWithAudio} />);
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });

  it('should not have accessibility violations with team and post type', async () => {
    const postWithTeamAndType = {
      ...mockPost,
      teamId: 'team-1',
      team: {
        name: 'Engineering',
      },
      postTypeId: 'type-1',
      postType: {
        name: 'Strategic feedback',
      },
    };
    
    const { container } = render(<PostItem post={postWithTeamAndType} />);
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });
});
