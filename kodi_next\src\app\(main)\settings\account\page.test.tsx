import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AccountSettingsPage from './page';
import { AuthProvider } from '@/contexts/AuthContext';

// Mock the auth context
const mockUpdateUserSettings = jest.fn();
jest.mock('@/contexts/AuthContext', () => ({
  ...jest.requireActual('@/contexts/AuthContext'),
  useAuth: () => ({
    user: {
      id: 'user-1',
      email: '<EMAIL>',
      roles: ['user', 'admin'],
      profile: {
        _id: 'profile-1',
        firstName: 'Test',
        lastName: 'User',
        userSettings: {
          showOnboarder: true,
        },
      },
    },
    updateUserSettings: mockUpdateUserSettings,
  }),
}));

describe('AccountSettingsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the account settings page with user information', () => {
    render(
      <AuthProvider>
        <AccountSettingsPage />
      </AuthProvider>
    );
    
    // Check for page title
    expect(screen.getByText('Account Settings')).toBeInTheDocument();
    
    // Check for back link
    expect(screen.getByText('Back to Settings')).toBeInTheDocument();
    expect(screen.getByText('Back to Settings').closest('a')).toHaveAttribute('href', '/settings');
    
    // Check for account information section
    expect(screen.getByText('Account Information')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Role')).toBeInTheDocument();
    expect(screen.getByText('user, admin')).toBeInTheDocument();
    
    // Check for preferences section
    expect(screen.getByText('Preferences')).toBeInTheDocument();
    expect(screen.getByText('Onboarding Guide')).toBeInTheDocument();
    expect(screen.getByText('Show the onboarding guide to help you get started')).toBeInTheDocument();
    
    // Check for toggle button
    const toggleButton = screen.getByRole('button');
    expect(toggleButton).toBeInTheDocument();
    expect(toggleButton).toHaveClass('bg-indigo-600'); // Enabled state
  });

  it('toggles onboarding setting', async () => {
    mockUpdateUserSettings.mockResolvedValue({});
    
    render(
      <AuthProvider>
        <AccountSettingsPage />
      </AuthProvider>
    );
    
    // Find and click the toggle button
    const user = userEvent.setup();
    const toggleButton = screen.getByRole('button');
    await user.click(toggleButton);
    
    // Check that updateUserSettings was called with correct data
    expect(mockUpdateUserSettings).toHaveBeenCalledWith({
      showOnboarder: false,
    });
    
    // Check for success message
    await waitFor(() => {
      expect(screen.getByText('Onboarding settings updated')).toBeInTheDocument();
    });
  });

  it('handles error when updating settings', async () => {
    // Mock error response
    mockUpdateUserSettings.mockRejectedValue({
      response: {
        data: {
          message: 'Failed to update settings',
        },
      },
    });
    
    render(
      <AuthProvider>
        <AccountSettingsPage />
      </AuthProvider>
    );
    
    // Find and click the toggle button
    const user = userEvent.setup();
    const toggleButton = screen.getByRole('button');
    await user.click(toggleButton);
    
    // Check for error message
    await waitFor(() => {
      expect(screen.getByText('Failed to update settings')).toBeInTheDocument();
    });
  });

  it('renders correctly for users without settings', () => {
    // Mock user without settings
    jest.spyOn(require('@/contexts/AuthContext'), 'useAuth').mockImplementation(() => ({
      user: {
        id: 'user-2',
        email: '<EMAIL>',
        roles: ['user'],
        profile: {
          _id: 'profile-2',
          firstName: 'New',
          lastName: 'User',
          // No userSettings
        },
      },
      updateUserSettings: mockUpdateUserSettings,
    }));
    
    render(
      <AuthProvider>
        <AccountSettingsPage />
      </AuthProvider>
    );
    
    // Page should render without errors
    expect(screen.getByText('Account Settings')).toBeInTheDocument();
    
    // Toggle should be in disabled state (gray)
    const toggleButton = screen.getByRole('button');
    expect(toggleButton).toHaveClass('bg-gray-200');
  });
});
