import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MulterModule } from '@nestjs/platform-express';
import { JwtModule } from '@nestjs/jwt';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { CompaniesModule } from './companies/companies.module';
import { ProfilesModule } from './profiles/profiles.module';
import { UploadsModule } from './uploads/uploads.module';
import { MessagingModule } from './messaging/messaging.module';
import { TeamsModule } from './teams/teams.module';
import { PostsModule } from './posts/posts.module';
import { AiModule } from './ai/ai.module';
import { HealthModule } from './health/health.module';
import { EmailModule } from './email/email.module';
import { UserContextMiddleware } from './auth/middleware/user-context.middleware';

@Module({
  imports: [
    MulterModule.register({
      dest: './uploads',
    }),
    MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb+srv://CKT_place3:<EMAIL>/place3?retryWrites=true&w=majority'),
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '1h' },
    }),
    UsersModule,
    AuthModule,
    CompaniesModule,
    ProfilesModule,
    UploadsModule,
    MessagingModule,
    TeamsModule,
    PostsModule,
    AiModule,
    HealthModule,
    EmailModule,
  ],
  providers: [UserContextMiddleware],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(UserContextMiddleware)
      .forRoutes('posts/types', 'teams', 'posts/tags');
  }
}
