/**
 * Polyfill for expo-secure-store on web platforms
 * This provides a localStorage-based implementation for web
 */

// Constants to match native API
export const WHEN_UNLOCKED = 'whenUnlocked';
export const AFTER_FIRST_UNLOCK = 'afterFirstUnlock';
export const ALWAYS = 'always';
export const WHEN_UNLOCKED_THIS_DEVICE_ONLY = 'whenUnlockedThisDeviceOnly';
export const WHEN_PASSCODE_SET_THIS_DEVICE_ONLY = 'whenPasscodeSetThisDeviceOnly';
export const AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY = 'afterFirstUnlockThisDeviceOnly';
export const ALWAYS_THIS_DEVICE_ONLY = 'alwaysThisDeviceOnly';

/**
 * Check if SecureStore is available on the current platform
 */
export async function isAvailableAsync(): Promise<boolean> {
  return typeof window !== 'undefined' && typeof localStorage !== 'undefined';
}

/**
 * Store a value with the given key
 */
export async function setItemAsync(
  key: string,
  value: string,
  options: any = {}
): Promise<void> {
  try {
    localStorage.setItem(`secure_store_${key}`, value);
  } catch (error) {
    console.error('Error writing to secure store:', error);
    throw error;
  }
}

/**
 * Get a value for the given key
 */
export async function getItemAsync(
  key: string,
  options: any = {}
): Promise<string | null> {
  try {
    return localStorage.getItem(`secure_store_${key}`);
  } catch (error) {
    console.error('Error reading from secure store:', error);
    return null;
  }
}

/**
 * Delete a value with the given key
 */
export async function deleteItemAsync(
  key: string,
  options: any = {}
): Promise<void> {
  try {
    localStorage.removeItem(`secure_store_${key}`);
  } catch (error) {
    console.error('Error deleting from secure store:', error);
    throw error;
  }
}

/**
 * Synchronous version of setItemAsync
 */
export function setItem(
  key: string,
  value: string,
  options: any = {}
): void {
  try {
    localStorage.setItem(`secure_store_${key}`, value);
  } catch (error) {
    console.error('Error writing to secure store:', error);
    throw error;
  }
}

/**
 * Synchronous version of getItemAsync
 */
export function getItem(
  key: string,
  options: any = {}
): string | null {
  try {
    return localStorage.getItem(`secure_store_${key}`);
  } catch (error) {
    console.error('Error reading from secure store:', error);
    return null;
  }
}

/**
 * Check if biometric authentication can be used
 * Always returns false on web
 */
export function canUseBiometricAuthentication(): boolean {
  return false;
}
