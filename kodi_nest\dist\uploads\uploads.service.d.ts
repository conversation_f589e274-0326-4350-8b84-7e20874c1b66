export declare class UploadsService {
    private s3;
    constructor();
    generatePresignedUrl(fileType: string, isPrivate?: boolean): Promise<{
        uploadUrl: string;
        key: string;
        url: string;
    }>;
    handleFileUpload(file: Express.Multer.File, type: string): {
        url: string;
        filename: string;
        originalname: string;
        mimetype: string;
        size: number;
    };
}
