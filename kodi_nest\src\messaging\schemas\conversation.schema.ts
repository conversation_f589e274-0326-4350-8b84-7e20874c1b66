import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type ConversationDocument = Conversation & Document;

@Schema({
  collection: 'kd_conversations',
  timestamps: true,
})
export class Conversation {
  @Prop({ type: [MongooseSchema.Types.ObjectId], ref: 'User', required: true })
  participants: MongooseSchema.Types.ObjectId[];

  @Prop({ type: String, default: null })
  title: string;

  @Prop({ type: Boolean, default: false })
  isGroup: boolean;

  @Prop({ type: Date, default: Date.now })
  lastMessageAt: Date;
}

export const ConversationSchema = SchemaFactory.createForClass(Conversation);
