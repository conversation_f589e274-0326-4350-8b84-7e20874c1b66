import { IsNotEmpty, <PERSON>String, <PERSON><PERSON><PERSON>al, IsA<PERSON>y, IsBoolean } from 'class-validator';

export class CreatePostDto {
  @IsNotEmpty()
  @IsString()
  body: string;

  @IsOptional()
  @IsString()
  enhancedBody?: string;

  @IsOptional()
  @IsString()
  companyId?: string;

  @IsOptional()
  @IsString()
  teamId?: string;

  @IsOptional()
  @IsString()
  postTypeId?: string;

  @IsOptional()
  @IsArray()
  hashtags?: string[];

  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsOptional()
  @IsBoolean()
  isDraft?: boolean;

  @IsOptional()
  @IsString()
  audioUrl?: string;
}
