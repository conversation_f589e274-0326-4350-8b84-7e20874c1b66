import { Document, Schema as MongooseSchema } from 'mongoose';
export type MembershipDocument = Membership & Document;
export declare class Membership {
    teamId: MongooseSchema.Types.ObjectId;
    memberId: MongooseSchema.Types.ObjectId;
}
export declare const MembershipSchema: MongooseSchema<Membership, import("mongoose").Model<Membership, any, any, any, Document<unknown, any, Membership> & Membership & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Membership, Document<unknown, {}, import("mongoose").FlatRecord<Membership>> & import("mongoose").FlatRecord<Membership> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
