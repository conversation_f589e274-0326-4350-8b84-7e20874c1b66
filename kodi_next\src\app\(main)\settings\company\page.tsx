'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import Link from 'next/link';

interface Company {
  _id: string;
  name: string;
  invite_code: string;
  logo?: string;
}

export default function CompanySettingsPage() {
  const { user } = useAuth();
  const [company, setCompany] = useState<Company | null>(null);
  const [inviteUrl, setInviteUrl] = useState('');
  const [copied, setCopied] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [companyName, setCompanyName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    if (user?.profile?.companyId) {
      fetchCompany(user.profile.companyId);
    } else {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (company) {
      setCompanyName(company.name);
      
      if (company.invite_code) {
        const baseUrl = window.location.origin;
        setInviteUrl(`${baseUrl}/register?inviteCode=${company.invite_code}`);
      }
    }
  }, [company]);

  const fetchCompany = async (companyId: string) => {
    try {
      setLoading(true);
      const { data } = await api.get(endpoints.companies.byId(companyId));
      setCompany(data);
    } catch (err: any) {
      console.error('Failed to fetch company:', err);
      setError('Failed to load company information. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const copyInviteUrl = () => {
    navigator.clipboard.writeText(inviteUrl);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleSave = async () => {
    if (!company) return;
    
    try {
      setIsSaving(true);
      setError('');
      
      await api.patch(endpoints.companies.byId(company._id), {
        name: companyName
      });
      
      setCompany({
        ...company,
        name: companyName
      });
      
      setIsEditing(false);
      setSuccessMessage('Company information updated successfully');
      
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (err: any) {
      console.error('Failed to update company:', err);
      setError(err.response?.data?.message || 'Failed to update company. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Company Settings</h1>
          <Link 
            href="/settings"
            className="text-sm text-indigo-600 hover:text-indigo-800"
          >
            Back to Settings
          </Link>
        </div>

        {error && (
          <div className="bg-red-50 p-4 rounded-md mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {successMessage && (
          <div className="bg-green-50 p-4 rounded-md mb-6">
            <p className="text-green-700">{successMessage}</p>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : (
          <>
            {company && (
              <div className="bg-white rounded-lg shadow p-6 mb-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Company Information</h2>
                  {!isEditing && user?.roles?.includes('admin') && (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="text-sm text-indigo-600 hover:text-indigo-800"
                    >
                      Edit
                    </button>
                  )}
                </div>
                
                <div className="space-y-4">
                  {isEditing ? (
                    <>
                      <div>
                        <label htmlFor="companyName" className="block text-sm font-medium text-gray-700">
                          Company Name
                        </label>
                        <input
                          type="text"
                          id="companyName"
                          value={companyName}
                          onChange={(e) => setCompanyName(e.target.value)}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                      </div>
                      <div className="flex justify-end space-x-3">
                        <button
                          onClick={() => {
                            setIsEditing(false);
                            setCompanyName(company.name);
                          }}
                          className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          disabled={isSaving}
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleSave}
                          className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          disabled={isSaving}
                        >
                          {isSaving ? 'Saving...' : 'Save'}
                        </button>
                      </div>
                    </>
                  ) : (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Company Name</p>
                      <p className="mt-1">{company.name}</p>
                    </div>
                  )}

                  {user?.roles?.includes('admin') && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Invite Link</p>
                      <div className="mt-1 flex items-center">
                        <input
                          type="text"
                          readOnly
                          value={inviteUrl}
                          className="flex-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <button
                          onClick={copyInviteUrl}
                          className="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                          {copied ? 'Copied!' : 'Copy'}
                        </button>
                      </div>
                      <p className="mt-2 text-sm text-gray-500">
                        Share this link to invite people to your company
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
