import React from 'react';
import { render } from '@testing-library/react';
import { axe, runAxe } from '@/test-utils/axe-setup';
import Navbar from './Navbar';
import { AuthProvider } from '@/contexts/AuthContext';
import { ThemeProvider } from '@/contexts/ThemeContext';

// Mock the useAuth hook
jest.mock('@/contexts/AuthContext', () => ({
  ...jest.requireActual('@/contexts/AuthContext'),
  useAuth: () => ({
    user: {
      email: '<EMAIL>',
      profile: {
        firstName: 'Test',
        lastName: 'User',
        avatar: null,
      },
    },
    logout: jest.fn(),
    isAuthenticated: true,
  }),
}));

// Mock the usePathname hook
jest.mock('next/navigation', () => ({
  usePathname: () => '/feed',
}));

// Mock the ThemeToggle component
jest.mock('./ThemeToggle', () => {
  return function MockThemeToggle() {
    return <button aria-label="Toggle theme">Theme Toggle</button>;
  };
});

describe('Navbar Accessibility', () => {
  it('should not have accessibility violations', async () => {
    const { container } = render(
      <ThemeProvider>
        <AuthProvider>
          <Navbar />
        </AuthProvider>
      </ThemeProvider>
    );
    
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });

  it('should not have accessibility violations when mobile menu is open', async () => {
    // Mock window.innerWidth to simulate mobile view
    global.innerWidth = 500;
    global.dispatchEvent(new Event('resize'));
    
    const { container, getByRole } = render(
      <ThemeProvider>
        <AuthProvider>
          <Navbar />
        </AuthProvider>
      </ThemeProvider>
    );
    
    // Open mobile menu
    const menuButton = getByRole('button', { name: /Open main menu/i });
    menuButton.click();
    
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });

  it('should not have accessibility violations when user menu is open', async () => {
    const { container, getByRole } = render(
      <ThemeProvider>
        <AuthProvider>
          <Navbar />
        </AuthProvider>
      </ThemeProvider>
    );
    
    // Open user menu
    const userMenuButton = getByRole('button', { name: /Open user menu/i });
    userMenuButton.click();
    
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });
});
