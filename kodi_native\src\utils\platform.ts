/**
 * Platform detection utilities
 */
import { Platform } from 'react-native';

/**
 * Check if the app is running on web
 */
export const isWeb = (): boolean => {
  return Platform.OS === 'web';
};

/**
 * Check if the app is running on iOS
 */
export const isIOS = (): boolean => {
  return Platform.OS === 'ios';
};

/**
 * Check if the app is running on Android
 */
export const isAndroid = (): boolean => {
  return Platform.OS === 'android';
};

/**
 * Check if the app is running on a mobile device (iOS or Android)
 */
export const isMobile = (): boolean => {
  return isIOS() || isAndroid();
};

/**
 * Get platform-specific styles
 * @param webStyles Styles for web
 * @param mobileStyles Styles for mobile (iOS and Android)
 * @param iOSStyles Styles specific to iOS
 * @param androidStyles Styles specific to Android
 * @returns Platform-specific styles
 */
export const getPlatformStyles = (
  webStyles = {},
  mobileStyles = {},
  iOSStyles = {},
  androidStyles = {}
) => {
  if (isWeb()) {
    return { ...mobileStyles, ...webStyles };
  }
  if (isIOS()) {
    return { ...mobileStyles, ...iOSStyles };
  }
  if (isAndroid()) {
    return { ...mobileStyles, ...androidStyles };
  }
  return mobileStyles;
};

export default {
  isWeb,
  isIOS,
  isAndroid,
  isMobile,
  getPlatformStyles,
};
