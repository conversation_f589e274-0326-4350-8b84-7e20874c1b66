import { Document, Schema as MongooseSchema } from 'mongoose';
export type AiPromptDocument = AiPrompt & Document;
export declare class AiPrompt {
    name: string;
    template: string;
    description: string;
    companyId: MongooseSchema.Types.ObjectId;
    userId: MongooseSchema.Types.ObjectId;
    isSystem: boolean;
}
export declare const AiPromptSchema: MongooseSchema<AiPrompt, import("mongoose").Model<AiPrompt, any, any, any, Document<unknown, any, AiPrompt> & AiPrompt & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, AiPrompt, Document<unknown, {}, import("mongoose").FlatRecord<AiPrompt>> & import("mongoose").FlatRecord<AiPrompt> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
