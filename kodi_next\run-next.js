const { spawn } = require('child_process');

console.log('Starting Next.js development server...');
const nextProcess = spawn('npx', ['next', 'dev', '-p', '3010'], {
  stdio: 'inherit',
  shell: true
});

nextProcess.on('error', (error) => {
  console.error('Error starting Next.js:', error.message);
});

nextProcess.on('close', (code) => {
  console.log(`Next.js process exited with code ${code}`);
});
