import { Document, Schema as MongooseSchema } from 'mongoose';
export type TeamDocument = Team & Document;
export declare class Team {
    name: string;
    companyId: MongooseSchema.Types.ObjectId;
    description: string;
    userId: MongooseSchema.Types.ObjectId;
    highlightColor: string;
}
export declare const TeamSchema: MongooseSchema<Team, import("mongoose").Model<Team, any, any, any, Document<unknown, any, Team> & Team & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Team, Document<unknown, {}, import("mongoose").FlatRecord<Team>> & import("mongoose").FlatRecord<Team> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
