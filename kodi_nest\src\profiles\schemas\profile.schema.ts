import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type ProfileDocument = Profile & Document;

@Schema({
  collection: 'kd_profiles',
  timestamps: true,
})
export class Profile {
  @Prop({ required: false })
  firstName: string;

  @Prop({ required: false })
  lastName: string;

  @Prop({ required: false })
  role: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company', required: false })
  companyId: MongooseSchema.Types.ObjectId;

  @Prop({ required: false })
  avatar: string;

  @Prop({ type: Object, required: false })
  transformedAvatar: {
    small: string;
    medium: string;
    large: string;
  };

  @Prop({ type: [String], default: [] })
  hashtags: string[];

  @Prop({ type: Object, default: { showOnboarder: true, onboarderStep: 1 } })
  userSettings: {
    showOnboarder: boolean;
    onboarderStep: number;
    [key: string]: any;
  };

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: MongooseSchema.Types.ObjectId;
}

export const ProfileSchema = SchemaFactory.createForClass(Profile);
