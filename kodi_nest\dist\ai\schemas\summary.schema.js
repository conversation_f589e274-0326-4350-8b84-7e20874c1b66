"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SummarySchema = exports.Summary = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let Summary = class Summary {
    tag;
    teamId;
    postTypeId;
    timeRange;
    userPrompt;
    promptId;
    summary;
    postIds;
    userId;
    companyId;
    lastUpdated;
    comments;
};
exports.Summary = Summary;
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], Summary.prototype, "tag", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'Team', required: false, index: true }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Summary.prototype, "teamId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'PostType', required: false, index: true }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Summary.prototype, "postTypeId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, index: true }),
    __metadata("design:type", String)
], Summary.prototype, "timeRange", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], Summary.prototype, "userPrompt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'AiPrompt', required: false }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Summary.prototype, "promptId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], Summary.prototype, "summary", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [{ type: mongoose_2.Schema.Types.ObjectId, ref: 'Post' }] }),
    __metadata("design:type", Array)
], Summary.prototype, "postIds", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'User', required: true, index: true }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Summary.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'Company', required: false, index: true }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Summary.prototype, "companyId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Date, required: false }),
    __metadata("design:type", Date)
], Summary.prototype, "lastUpdated", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [{ type: mongoose_2.Schema.Types.ObjectId, ref: 'Comment' }] }),
    __metadata("design:type", Array)
], Summary.prototype, "comments", void 0);
exports.Summary = Summary = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'kd_summaries',
        timestamps: true,
    })
], Summary);
exports.SummarySchema = mongoose_1.SchemaFactory.createForClass(Summary);
//# sourceMappingURL=summary.schema.js.map