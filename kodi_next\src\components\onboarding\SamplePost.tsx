import React from 'react';
import Link from 'next/link';

interface SamplePostProps {
  companyName: string;
}

export default function SamplePost({ companyName }: SamplePostProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6 border-l-4 border-indigo-500">
      <div className="flex items-center mb-4">
        <div className="h-10 w-10 rounded-full bg-indigo-100 flex-shrink-0 flex items-center justify-center">
          <span className="text-indigo-600 text-sm font-medium">KD</span>
        </div>
        <div className="ml-3">
          <p className="font-medium"><PERSON><PERSON> Assistant</p>
          <div className="flex items-center space-x-2">
            <p className="text-sm text-gray-500">
              {new Date().toLocaleDateString()}
            </p>
            <span className="text-sm text-gray-500">
              • Type: Welcome
            </span>
          </div>
        </div>
      </div>

      <div className="prose max-w-none">
        <h3>Welcome to Ko<PERSON>!</h3>
        <p>
          This is a sample post to help you get started with <PERSON><PERSON>. Here's what you can do:
        </p>
        <ul>
          <li>Create posts by clicking the POST button in the navigation bar</li>
          <li>Record audio and have it automatically transcribed</li>
          <li>Use hashtags to categorize your posts</li>
          <li>Filter posts by team, type, or tag</li>
          <li>Generate AI-powered analysis of your content</li>
        </ul>
        <p>
          As an admin of {companyName}, you can also:
        </p>
        <ul>
          <li>Invite team members using your company's invite code</li>
          <li>Create and manage teams</li>
          <li>Define custom post types</li>
          <li>Configure AI prompts</li>
        </ul>
        <p>
          We hope you enjoy using Kodi for your team's voice-based communication!
        </p>
      </div>

      <div className="mt-4 flex flex-wrap gap-2">
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
          #welcome
        </span>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
          #gettingStarted
        </span>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
          #kodi
        </span>
      </div>

      <div className="mt-6 flex justify-center">
        <Link
          href="/post/type-selection"
          className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Create Your First Post
        </Link>
      </div>
    </div>
  );
}
