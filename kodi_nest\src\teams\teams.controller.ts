import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, ForbiddenException, Req } from '@nestjs/common';
import { Request } from 'express';
import { TeamsService } from './teams.service';
import { CreateTeamDto } from './dto/create-team.dto';
import { UpdateTeamDto } from './dto/update-team.dto';
import { CreateMembershipDto } from './dto/create-membership.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@Controller('teams')
export class TeamsController {
  constructor(private readonly teamsService: TeamsService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Body() createTeamDto: CreateTeamDto) {
    return this.teamsService.create(createTeamDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  findAll(
    @Req() req: Request,
    @Query('companyId') companyId?: string,
    @Query('userId') userId?: string
  ) {
    // Get the user's company ID from the context
    const userCompanyId = req.userContext?.companyId;

    if (userId) {
      // If userId is provided, get teams for that user but still filter by company
      return this.teamsService.getUserTeams(userId, userCompanyId);
    }

    // Use the company ID from the query only if it matches the user's company ID
    // Otherwise, always use the user's company ID for security
    const effectiveCompanyId = companyId && userCompanyId && companyId === userCompanyId
      ? companyId
      : userCompanyId;

    if (effectiveCompanyId) {
      return this.teamsService.findByCompany(effectiveCompanyId);
    }

    // If no company ID is available, return an empty array for security
    // We don't want to expose all teams across all companies
    return [];
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.teamsService.findById(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateTeamDto: UpdateTeamDto,
    @Body('userId') userId: string,
  ) {
    // Check if user is a member of the team
    const isMember = await this.teamsService.isMember(id, userId);
    if (!isMember) {
      throw new ForbiddenException('You are not a member of this team');
    }

    return this.teamsService.update(id, updateTeamDto);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'coAdmin')
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.teamsService.remove(id);
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id/members')
  getTeamMembers(@Param('id') id: string) {
    return this.teamsService.getTeamMembers(id);
  }

  @UseGuards(JwtAuthGuard)
  @Post(':id/members')
  addMember(
    @Param('id') teamId: string,
    @Body('memberId') memberId: string,
  ) {
    const createMembershipDto: CreateMembershipDto = {
      teamId,
      memberId,
    };
    return this.teamsService.addMember(createMembershipDto);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id/members/:memberId')
  async removeMember(
    @Param('id') teamId: string,
    @Param('memberId') memberId: string,
    @Body('userId') userId: string,
  ) {
    // Check if user is a member of the team
    const isMember = await this.teamsService.isMember(teamId, userId);
    if (!isMember) {
      throw new ForbiddenException('You are not a member of this team');
    }

    return this.teamsService.removeMember(teamId, memberId);
  }
}
