// This is a simple shim for nanoid/non-secure
// It provides a basic implementation that matches the API
// but uses Math.random() instead of crypto

// Simple non-secure ID generator
function nanoid(size = 21) {
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let id = '';
  for (let i = 0; i < size; i++) {
    id += alphabet.charAt(Math.floor(Math.random() * alphabet.length));
  }
  return id;
}

module.exports = nanoid;
