import { Test, TestingModule } from '@nestjs/testing';
import { ProfilesService } from './profiles.service';
import { getModelToken } from '@nestjs/mongoose';
import { Profile } from './schemas/profile.schema';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('ProfilesService', () => {
  let service: ProfilesService;
  let profileModel: Model<Profile>;

  const mockProfileModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProfilesService,
        {
          provide: getModelToken(Profile.name),
          useValue: mockProfileModel,
        },
      ],
    }).compile();

    service = module.get<ProfilesService>(ProfilesService);
    profileModel = module.get<Model<Profile>>(getModelToken(Profile.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new profile', async () => {
      const createProfileDto = {
        userId: 'user1',
        firstName: 'Test',
        lastName: 'User',
        companyId: 'company1',
      };

      const savedProfile = {
        _id: 'profile1',
        ...createProfileDto,
      };

      // Mock the model constructor and save
      mockProfileModel.new = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedProfile),
      }));

      const result = await service.create(createProfileDto);

      expect(result).toEqual(savedProfile);
    });
  });

  describe('findAll', () => {
    it('should return all profiles', async () => {
      const mockProfiles = [
        { _id: 'profile1', firstName: 'Test1', lastName: 'User1' },
        { _id: 'profile2', firstName: 'Test2', lastName: 'User2' },
      ];

      mockProfileModel.find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockProfiles),
      });

      const result = await service.findAll();

      expect(mockProfileModel.find).toHaveBeenCalled();
      expect(result).toEqual(mockProfiles);
    });
  });

  describe('findByCompany', () => {
    it('should return profiles for a specific company', async () => {
      const mockProfiles = [
        { _id: 'profile1', firstName: 'Test1', lastName: 'User1', companyId: 'company1' },
      ];

      mockProfileModel.find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockProfiles),
      });

      const result = await service.findByCompany('company1');

      expect(mockProfileModel.find).toHaveBeenCalledWith({ companyId: 'company1' });
      expect(result).toEqual(mockProfiles);
    });
  });

  describe('findById', () => {
    it('should return a profile when it exists', async () => {
      const mockProfile = { _id: 'profile1', firstName: 'Test', lastName: 'User' };

      mockProfileModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockProfile),
      });

      const result = await service.findById('profile1');

      expect(mockProfileModel.findById).toHaveBeenCalledWith('profile1');
      expect(result).toEqual(mockProfile);
    });

    it('should throw NotFoundException when profile does not exist', async () => {
      mockProfileModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.findById('nonexistent')).rejects.toThrow(NotFoundException);
      expect(mockProfileModel.findById).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('findByUserId', () => {
    it('should return a profile when user exists', async () => {
      const mockProfile = { _id: 'profile1', userId: 'user1', firstName: 'Test', lastName: 'User' };

      mockProfileModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockProfile),
      });

      const result = await service.findByUserId('user1');

      expect(mockProfileModel.findOne).toHaveBeenCalledWith({ userId: 'user1' });
      expect(result).toEqual(mockProfile);
    });

    it('should return null when user does not have a profile', async () => {
      mockProfileModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      const result = await service.findByUserId('nonexistent');

      expect(mockProfileModel.findOne).toHaveBeenCalledWith({ userId: 'nonexistent' });
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update a profile when it exists', async () => {
      const updateProfileDto = {
        firstName: 'Updated',
        lastName: 'User',
      };

      const updatedProfile = {
        _id: 'profile1',
        userId: 'user1',
        ...updateProfileDto,
      };

      mockProfileModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedProfile),
      });

      const result = await service.update('profile1', updateProfileDto);

      expect(mockProfileModel.findByIdAndUpdate).toHaveBeenCalledWith('profile1', updateProfileDto, { new: true });
      expect(result).toEqual(updatedProfile);
    });

    it('should throw NotFoundException when profile does not exist', async () => {
      mockProfileModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.update('nonexistent', { firstName: 'Updated' })).rejects.toThrow(NotFoundException);
      expect(mockProfileModel.findByIdAndUpdate).toHaveBeenCalledWith('nonexistent', { firstName: 'Updated' }, { new: true });
    });
  });

  describe('updateUserSettings', () => {
    it('should update user settings when profile exists', async () => {
      const existingProfile = {
        _id: 'profile1',
        userId: 'user1',
        firstName: 'Test',
        lastName: 'User',
        userSettings: {
          showOnboarder: true,
        },
      };

      const updatedProfile = {
        ...existingProfile,
        userSettings: {
          showOnboarder: false,
        },
      };

      mockProfileModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingProfile),
      });

      mockProfileModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedProfile),
      });

      const result = await service.updateUserSettings('profile1', { showOnboarder: false });

      expect(mockProfileModel.findById).toHaveBeenCalledWith('profile1');
      expect(mockProfileModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'profile1',
        { userSettings: { showOnboarder: false } },
        { new: true }
      );
      expect(result).toEqual(updatedProfile);
    });

    it('should initialize user settings if they do not exist', async () => {
      const existingProfile = {
        _id: 'profile1',
        userId: 'user1',
        firstName: 'Test',
        lastName: 'User',
        // No userSettings
      };

      const updatedProfile = {
        ...existingProfile,
        userSettings: {
          showOnboarder: false,
        },
      };

      mockProfileModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingProfile),
      });

      mockProfileModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedProfile),
      });

      const result = await service.updateUserSettings('profile1', { showOnboarder: false });

      expect(mockProfileModel.findById).toHaveBeenCalledWith('profile1');
      expect(mockProfileModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'profile1',
        { userSettings: { showOnboarder: false } },
        { new: true }
      );
      expect(result).toEqual(updatedProfile);
    });

    it('should throw NotFoundException when profile does not exist', async () => {
      mockProfileModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.updateUserSettings('nonexistent', { showOnboarder: false })).rejects.toThrow(NotFoundException);
      expect(mockProfileModel.findById).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('remove', () => {
    it('should remove a profile when it exists', async () => {
      const deletedProfile = { _id: 'profile1', firstName: 'Test', lastName: 'User' };

      mockProfileModel.findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(deletedProfile),
      });

      const result = await service.remove('profile1');

      expect(mockProfileModel.findByIdAndDelete).toHaveBeenCalledWith('profile1');
      expect(result).toEqual(deletedProfile);
    });

    it('should throw NotFoundException when profile does not exist', async () => {
      mockProfileModel.findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.remove('nonexistent')).rejects.toThrow(NotFoundException);
      expect(mockProfileModel.findByIdAndDelete).toHaveBeenCalledWith('nonexistent');
    });
  });
});
