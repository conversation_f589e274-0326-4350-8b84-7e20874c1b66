import { Document, Schema as MongooseSchema } from 'mongoose';
export type TagDocument = Tag & Document;
export declare class Tag {
    name: string;
    companyId: MongooseSchema.Types.ObjectId;
    count: number;
    highlightColor: string;
}
export declare const TagSchema: MongooseSchema<Tag, import("mongoose").Model<Tag, any, any, any, Document<unknown, any, Tag> & Tag & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Tag, Document<unknown, {}, import("mongoose").FlatRecord<Tag>> & import("mongoose").FlatRecord<Tag> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
