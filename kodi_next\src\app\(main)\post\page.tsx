'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function PostPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the post type selection page
    router.push('/post/type-selection');
  }, [router]);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
        <p className="text-center text-gray-600">Redirecting to post creation...</p>
      </div>
    </div>
  );
}
