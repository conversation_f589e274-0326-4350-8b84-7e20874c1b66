import { MessagingService } from './messaging.service';
export declare class MessagingController {
    private readonly messagingService;
    constructor(messagingService: MessagingService);
    createConversation(createConversationDto: {
        participants: string[];
        title?: string;
        isGroup?: boolean;
    }, req: any): Promise<import("./schemas/conversation.schema").ConversationDocument>;
    findUserConversations(req: any): Promise<import("./schemas/conversation.schema").ConversationDocument[]>;
    findConversation(id: string): Promise<import("./schemas/conversation.schema").ConversationDocument>;
    findConversationMessages(id: string): Promise<import("./schemas/message.schema").MessageDocument[]>;
    createMessage(id: string, createMessageDto: {
        content: string;
    }, req: any): Promise<import("./schemas/message.schema").MessageDocument>;
    markMessageAsRead(id: string, req: any): Promise<import("./schemas/message.schema").MessageDocument>;
}
