"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AiService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const summary_schema_1 = require("./schemas/summary.schema");
const insight_schema_1 = require("./schemas/insight.schema");
const ai_prompt_schema_1 = require("./schemas/ai-prompt.schema");
const ai_chat_schema_1 = require("./schemas/ai-chat.schema");
const app_settings_schema_1 = require("./schemas/app-settings.schema");
const posts_service_1 = require("../posts/posts.service");
let AiService = class AiService {
    summaryModel;
    insightModel;
    aiPromptModel;
    aiChatModel;
    appSettingsModel;
    postsService;
    constructor(summaryModel, insightModel, aiPromptModel, aiChatModel, appSettingsModel, postsService) {
        this.summaryModel = summaryModel;
        this.insightModel = insightModel;
        this.aiPromptModel = aiPromptModel;
        this.aiChatModel = aiChatModel;
        this.appSettingsModel = appSettingsModel;
        this.postsService = postsService;
    }
    async createSummary(createSummaryDto) {
        const newSummary = new this.summaryModel(createSummaryDto);
        return newSummary.save();
    }
    async findAllSummaries(userId, companyId, tag) {
        const query = {};
        if (userId) {
            query.userId = userId;
        }
        if (companyId) {
            query.companyId = companyId;
        }
        if (tag) {
            query.tag = tag;
        }
        return this.summaryModel
            .find(query)
            .sort({ createdAt: -1 })
            .exec();
    }
    async findSummaryById(id) {
        const summary = await this.summaryModel.findById(id).exec();
        if (!summary) {
            throw new common_1.NotFoundException(`Summary with ID ${id} not found`);
        }
        return summary;
    }
    async updateSummary(id, updateSummaryDto) {
        const updatedSummary = await this.summaryModel
            .findByIdAndUpdate(id, updateSummaryDto, { new: true })
            .exec();
        if (!updatedSummary) {
            throw new common_1.NotFoundException(`Summary with ID ${id} not found`);
        }
        return updatedSummary;
    }
    async removeSummary(id) {
        const deletedSummary = await this.summaryModel.findByIdAndDelete(id).exec();
        if (!deletedSummary) {
            throw new common_1.NotFoundException(`Summary with ID ${id} not found`);
        }
        return deletedSummary;
    }
    async generateSummary(createSummaryDto) {
        const mockSummary = `This is a generated summary based on the prompt: "${createSummaryDto.userPrompt}"

    Key points:
    - Point 1: Lorem ipsum dolor sit amet
    - Point 2: Consectetur adipiscing elit
    - Point 3: Sed do eiusmod tempor incididunt

    Conclusion: This is a placeholder summary that would normally be generated by an AI service.`;
        createSummaryDto.summary = mockSummary;
        createSummaryDto.lastUpdated = new Date();
        return this.createSummary(createSummaryDto);
    }
    async createInsight(createInsightDto) {
        const newInsight = new this.insightModel(createInsightDto);
        return newInsight.save();
    }
    async findAllInsights(userId, companyId, tag) {
        const query = {};
        if (userId) {
            query.userId = userId;
        }
        if (companyId) {
            query.companyId = companyId;
        }
        if (tag) {
            query.tag = tag;
        }
        return this.insightModel
            .find(query)
            .sort({ createdAt: -1 })
            .exec();
    }
    async findInsightById(id) {
        const insight = await this.insightModel.findById(id).exec();
        if (!insight) {
            throw new common_1.NotFoundException(`Insight with ID ${id} not found`);
        }
        return insight;
    }
    async updateInsight(id, updateInsightDto) {
        const updatedInsight = await this.insightModel
            .findByIdAndUpdate(id, updateInsightDto, { new: true })
            .exec();
        if (!updatedInsight) {
            throw new common_1.NotFoundException(`Insight with ID ${id} not found`);
        }
        return updatedInsight;
    }
    async removeInsight(id) {
        const deletedInsight = await this.insightModel.findByIdAndDelete(id).exec();
        if (!deletedInsight) {
            throw new common_1.NotFoundException(`Insight with ID ${id} not found`);
        }
        return deletedInsight;
    }
    async analyzeInsight(createInsightDto) {
        const mockSummary = `This is a generated insight analysis for "${createInsightDto.title}"

    Analysis:
    - Finding 1: Lorem ipsum dolor sit amet
    - Finding 2: Consectetur adipiscing elit
    - Finding 3: Sed do eiusmod tempor incididunt

    Recommendation: This is a placeholder insight that would normally be generated by an AI service.`;
        createInsightDto.summary = mockSummary;
        return this.createInsight(createInsightDto);
    }
    async createAiPrompt(createAiPromptDto) {
        const newAiPrompt = new this.aiPromptModel(createAiPromptDto);
        return newAiPrompt.save();
    }
    async findAllAiPrompts(companyId, isSystem) {
        const query = {};
        if (companyId) {
            query.companyId = companyId;
        }
        if (isSystem !== undefined) {
            query.isSystem = isSystem;
        }
        return this.aiPromptModel.find(query).exec();
    }
    async findAiPromptById(id) {
        const aiPrompt = await this.aiPromptModel.findById(id).exec();
        if (!aiPrompt) {
            throw new common_1.NotFoundException(`AI Prompt with ID ${id} not found`);
        }
        return aiPrompt;
    }
    async updateAiPrompt(id, updateAiPromptDto) {
        const updatedAiPrompt = await this.aiPromptModel
            .findByIdAndUpdate(id, updateAiPromptDto, { new: true })
            .exec();
        if (!updatedAiPrompt) {
            throw new common_1.NotFoundException(`AI Prompt with ID ${id} not found`);
        }
        return updatedAiPrompt;
    }
    async removeAiPrompt(id) {
        const deletedAiPrompt = await this.aiPromptModel.findByIdAndDelete(id).exec();
        if (!deletedAiPrompt) {
            throw new common_1.NotFoundException(`AI Prompt with ID ${id} not found`);
        }
        return deletedAiPrompt;
    }
    async createAiChat(createAiChatDto) {
        const newAiChat = new this.aiChatModel(createAiChatDto);
        return newAiChat.save();
    }
    async findAllAiChats(userId) {
        return this.aiChatModel
            .find({ userId })
            .sort({ updatedAt: -1 })
            .exec();
    }
    async findAiChatById(id) {
        const aiChat = await this.aiChatModel.findById(id).exec();
        if (!aiChat) {
            throw new common_1.NotFoundException(`AI Chat with ID ${id} not found`);
        }
        return aiChat;
    }
    async updateAiChat(id, updateAiChatDto) {
        const updatedAiChat = await this.aiChatModel
            .findByIdAndUpdate(id, updateAiChatDto, { new: true })
            .exec();
        if (!updatedAiChat) {
            throw new common_1.NotFoundException(`AI Chat with ID ${id} not found`);
        }
        return updatedAiChat;
    }
    async removeAiChat(id) {
        const deletedAiChat = await this.aiChatModel.findByIdAndDelete(id).exec();
        if (!deletedAiChat) {
            throw new common_1.NotFoundException(`AI Chat with ID ${id} not found`);
        }
        return deletedAiChat;
    }
    async sendChatMessage(chatId, message, userId) {
        const chat = await this.aiChatModel.findById(chatId).exec();
        if (!chat) {
            throw new common_1.NotFoundException(`AI Chat with ID ${chatId} not found`);
        }
        if (chat.userId.toString() !== userId) {
            throw new common_1.ForbiddenException('You do not have permission to access this chat');
        }
        const userMessage = {
            role: 'user',
            content: message,
            timestamp: new Date(),
        };
        const aiResponse = {
            role: 'assistant',
            content: `This is a mock response to: "${message}"

      I'm an AI assistant that would normally provide a helpful response based on your message and the conversation context.`,
            timestamp: new Date(),
        };
        chat.messages.push(userMessage, aiResponse);
        if (chat.messages.length === 2) {
            chat.title = message.substring(0, 50) + (message.length > 50 ? '...' : '');
        }
        return chat.save();
    }
    async createAppSettings(createAppSettingsDto) {
        const newAppSettings = new this.appSettingsModel(createAppSettingsDto);
        return newAppSettings.save();
    }
    async findAllAppSettings(type, companyId) {
        const query = {};
        if (type) {
            query.type = type;
        }
        if (companyId) {
            query.companyId = companyId;
        }
        return this.appSettingsModel.find(query).exec();
    }
    async findAppSettingsById(id) {
        const appSettings = await this.appSettingsModel.findById(id).exec();
        if (!appSettings) {
            throw new common_1.NotFoundException(`App Settings with ID ${id} not found`);
        }
        return appSettings;
    }
    async updateAppSettings(id, updateAppSettingsDto) {
        const updatedAppSettings = await this.appSettingsModel
            .findByIdAndUpdate(id, updateAppSettingsDto, { new: true })
            .exec();
        if (!updatedAppSettings) {
            throw new common_1.NotFoundException(`App Settings with ID ${id} not found`);
        }
        return updatedAppSettings;
    }
    async removeAppSettings(id) {
        const deletedAppSettings = await this.appSettingsModel.findByIdAndDelete(id).exec();
        if (!deletedAppSettings) {
            throw new common_1.NotFoundException(`App Settings with ID ${id} not found`);
        }
        return deletedAppSettings;
    }
    async transcribeAudio(transcribeAudioDto) {
        const { audioUrl, postTypeId } = transcribeAudioDto;
        let transcription = 'This is a simulated transcription of the audio recording.';
        if (postTypeId) {
            try {
                const postType = await this.postsService.findPostTypeById(postTypeId);
                if (postType) {
                    transcription += ` This is for the post type: ${postType.name}.`;
                }
            }
            catch (error) {
                console.error('Error finding post type:', error);
            }
        }
        return { text: transcription };
    }
};
exports.AiService = AiService;
exports.AiService = AiService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(summary_schema_1.Summary.name)),
    __param(1, (0, mongoose_1.InjectModel)(insight_schema_1.Insight.name)),
    __param(2, (0, mongoose_1.InjectModel)(ai_prompt_schema_1.AiPrompt.name)),
    __param(3, (0, mongoose_1.InjectModel)(ai_chat_schema_1.AiChat.name)),
    __param(4, (0, mongoose_1.InjectModel)(app_settings_schema_1.AppSettings.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        posts_service_1.PostsService])
], AiService);
//# sourceMappingURL=ai.service.js.map