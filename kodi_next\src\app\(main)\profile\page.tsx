'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';

export default function ProfilePage() {
  const { user, updateUserSettings } = useAuth();
  const [firstName, setFirstName] = useState(user?.profile?.firstName || '');
  const [lastName, setLastName] = useState(user?.profile?.lastName || '');
  const [role, setRole] = useState(user?.profile?.role || '');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const handleEdit = () => {
    setIsEditing(true);
    setSuccessMessage('');
    setError('');
  };

  const handleCancel = () => {
    setFirstName(user?.profile?.firstName || '');
    setLastName(user?.profile?.lastName || '');
    setRole(user?.profile?.role || '');
    setIsEditing(false);
    setError('');
  };

  const handleSave = async () => {
    setIsSaving(true);
    setError('');
    setSuccessMessage('');

    try {
      await api.patch(endpoints.profiles.byId(user?.profile?._id), {
        firstName,
        lastName,
        role,
      });

      setSuccessMessage('Profile updated successfully');
      setIsEditing(false);
    } catch (err: any) {
      console.error('Failed to update profile:', err);
      setError(err.response?.data?.message || 'Failed to update profile. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateOnboarding = async () => {
    try {
      await updateUserSettings({
        showOnboarder: !user?.profile?.userSettings?.showOnboarder,
      });
      setSuccessMessage('Onboarding settings updated');
    } catch (err: any) {
      console.error('Failed to update onboarding settings:', err);
      setError(err.response?.data?.message || 'Failed to update settings. Please try again.');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Your Profile</h1>

        {error && (
          <div className="bg-red-50 p-4 rounded-md mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {successMessage && (
          <div className="bg-green-50 p-4 rounded-md mb-6">
            <p className="text-green-700">{successMessage}</p>
          </div>
        )}

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="flex items-center mb-6">
            <div className="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
              {user?.profile?.avatar ? (
                <img
                  src={user.profile.avatar}
                  alt={`${user.profile.firstName} ${user.profile.lastName}`}
                  className="h-20 w-20 object-cover"
                />
              ) : (
                <span className="text-gray-500 text-xl font-medium">
                  {user?.profile?.firstName?.[0] || ''}
                  {user?.profile?.lastName?.[0] || ''}
                </span>
              )}
            </div>
            <div className="ml-6">
              <h2 className="text-xl font-semibold">
                {user?.profile?.firstName} {user?.profile?.lastName}
              </h2>
              <p className="text-gray-500">{user?.email}</p>
              {user?.profile?.role && (
                <p className="text-gray-700 mt-1">{user.profile.role}</p>
              )}
            </div>
          </div>

          {!isEditing ? (
            <div className="flex justify-end">
              <button
                onClick={handleEdit}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                Edit Profile
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                    First Name
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                    Last Name
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="role" className="block text-sm font-medium text-gray-700">
                  Role
                </label>
                <input
                  type="text"
                  id="role"
                  value={role}
                  onChange={(e) => setRole(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:bg-indigo-400"
                >
                  {isSaving ? 'Saving...' : 'Save'}
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Settings</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-base font-medium text-gray-900">Onboarding Guide</h3>
                <p className="text-sm text-gray-500">
                  Show the onboarding guide to help you get started
                </p>
              </div>
              <button
                onClick={handleUpdateOnboarding}
                className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                  user?.profile?.userSettings?.showOnboarder ? 'bg-indigo-600' : 'bg-gray-200'
                }`}
                role="switch"
                aria-checked={user?.profile?.userSettings?.showOnboarder}
              >
                <span
                  aria-hidden="true"
                  className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                    user?.profile?.userSettings?.showOnboarder ? 'translate-x-5' : 'translate-x-0'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
