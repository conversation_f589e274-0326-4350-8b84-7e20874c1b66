module.exports = function(api) {
  api.cache(true);

  // Determine if we're targeting web
  const isWeb = process.env.EXPO_TARGET === 'web';

  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'react-native-reanimated/plugin',
      [
        'module-resolver',
        {
          extensions: ['.web.js', '.web.ts', '.web.tsx', '.js', '.ts', '.tsx', '.cjs'],
          alias: {
            // For web platform, use our custom implementation
            'expo-secure-store': isWeb
              ? './src/expo-secure-store.web.ts'
              : 'expo-secure-store',
          },
        },
      ],
    ],
  };
};
