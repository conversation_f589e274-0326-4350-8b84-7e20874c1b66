interface EmailOptions {
    to: string | string[];
    subject: string;
    text?: string;
    html?: string;
}
export declare class EmailService {
    private readonly logger;
    private readonly ses;
    private readonly transporter;
    private readonly fromName;
    private readonly fromAddress;
    private readonly provider;
    constructor();
    sendEmail(options: EmailOptions): Promise<boolean>;
    sendVerificationEmail(email: string, token: string): Promise<boolean>;
    sendPasswordResetEmail(email: string, token: string): Promise<boolean>;
    sendWelcomeEmail(email: string, firstName?: string): Promise<boolean>;
    sendInviteEmail(email: string, companyName: string, inviteCode: string): Promise<boolean>;
}
export {};
