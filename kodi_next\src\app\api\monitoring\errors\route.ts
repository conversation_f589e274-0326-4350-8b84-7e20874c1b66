import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const errorData = await request.json();
    
    // In a real implementation, you would:
    // 1. Validate the error data
    // 2. Store the error in a database or send to an error tracking service
    // 3. Potentially notify developers of critical errors
    
    // For now, we'll just log the error in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error reported from client:', errorData);
    }
    
    // In production, you might send this to a service like:
    // - Sentry
    // - Bugsnag
    // - Rollbar
    // - Custom error tracking backend
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing error report:', error);
    return NextResponse.json({ error: 'Failed to process error report' }, { status: 500 });
  }
}

// Disable body size limit for this route
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '500kb',
    },
  },
};
