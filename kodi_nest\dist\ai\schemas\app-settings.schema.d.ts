import { Document, Schema as MongooseSchema } from 'mongoose';
export type AppSettingsDocument = AppSettings & Document;
export declare class AppSettings {
    type: string;
    name: string;
    settings: Array<{
        key: string;
        value: any;
        type: string;
    }>;
    companyId: MongooseSchema.Types.ObjectId;
    userId: MongooseSchema.Types.ObjectId;
}
export declare const AppSettingsSchema: MongooseSchema<AppSettings, import("mongoose").Model<AppSettings, any, any, any, Document<unknown, any, AppSettings> & AppSettings & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, AppSettings, Document<unknown, {}, import("mongoose").FlatRecord<AppSettings>> & import("mongoose").FlatRecord<AppSettings> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
