import React from 'react';
import { render, screen } from '@testing-library/react';
import SettingsPage from './page';
import { AuthProvider } from '@/contexts/AuthContext';

// Mock the auth context
jest.mock('@/contexts/AuthContext', () => ({
  ...jest.requireActual('@/contexts/AuthContext'),
  useAuth: () => ({
    user: {
      id: 'user-1',
      email: '<EMAIL>',
      roles: ['admin'],
      profile: {
        _id: 'profile-1',
        firstName: 'Test',
        lastName: 'User',
      },
    },
  }),
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

describe('SettingsPage', () => {
  it('renders the settings page with all sections', () => {
    render(
      <AuthProvider>
        <SettingsPage />
      </AuthProvider>
    );
    
    // Check for page title
    expect(screen.getByText('Settings')).toBeInTheDocument();
    
    // Check for all settings sections
    expect(screen.getByText('Account Settings')).toBeInTheDocument();
    expect(screen.getByText('People')).toBeInTheDocument();
    expect(screen.getByText('Teams')).toBeInTheDocument();
    expect(screen.getByText('Tags')).toBeInTheDocument();
    expect(screen.getByText('Post Types')).toBeInTheDocument();
    expect(screen.getByText('Prompts')).toBeInTheDocument();
    expect(screen.getByText('Company')).toBeInTheDocument();
    expect(screen.getByText('Appearance')).toBeInTheDocument();
    
    // Check for descriptions
    expect(screen.getByText('Manage your personal account settings')).toBeInTheDocument();
    expect(screen.getByText('Manage users and permissions')).toBeInTheDocument();
    expect(screen.getByText('Create and manage teams')).toBeInTheDocument();
    expect(screen.getByText('Manage post tags and categories')).toBeInTheDocument();
    expect(screen.getByText('Configure post types and templates')).toBeInTheDocument();
    expect(screen.getByText('Manage AI prompts and templates')).toBeInTheDocument();
    expect(screen.getByText('Manage company information and settings')).toBeInTheDocument();
    expect(screen.getByText('Customize the look and feel of the application')).toBeInTheDocument();
  });

  it('renders links to each settings section', () => {
    render(
      <AuthProvider>
        <SettingsPage />
      </AuthProvider>
    );
    
    // Check for links to each section
    const links = screen.getAllByRole('link');
    
    expect(links[0]).toHaveAttribute('href', '/settings/account');
    expect(links[1]).toHaveAttribute('href', '/settings/people');
    expect(links[2]).toHaveAttribute('href', '/settings/teams');
    expect(links[3]).toHaveAttribute('href', '/settings/tags');
    expect(links[4]).toHaveAttribute('href', '/settings/post-types');
    expect(links[5]).toHaveAttribute('href', '/settings/prompts');
    expect(links[6]).toHaveAttribute('href', '/settings/company');
    expect(links[7]).toHaveAttribute('href', '/settings/appearance');
  });

  it('renders correctly for non-admin users', () => {
    // Mock non-admin user
    jest.spyOn(require('@/contexts/AuthContext'), 'useAuth').mockImplementation(() => ({
      user: {
        id: 'user-2',
        email: '<EMAIL>',
        roles: ['user'],
        profile: {
          _id: 'profile-2',
          firstName: 'Regular',
          lastName: 'User',
        },
      },
    }));
    
    render(
      <AuthProvider>
        <SettingsPage />
      </AuthProvider>
    );
    
    // All sections should still be visible, but some might be disabled or have different behavior
    // This depends on the actual implementation of access control in the component
    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Account Settings')).toBeInTheDocument();
  });
});
