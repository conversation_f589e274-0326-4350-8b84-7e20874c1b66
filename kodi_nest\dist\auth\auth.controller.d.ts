import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { Request as ExpressRequest } from 'express';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<{
        access_token: string;
        refresh_token: string;
        user: {
            id: any;
            email: any;
            profile: any;
            roles: any;
        };
    }>;
    login(req: ExpressRequest): Promise<{
        access_token: string;
        refresh_token: string;
        user: {
            id: any;
            email: any;
            profile: any;
            roles: any;
        };
    }>;
    refreshToken(body: {
        refresh_token: string;
    }): Promise<{
        access_token: string;
        refresh_token: string;
        user: {
            id: any;
            email: any;
            profile: any;
            roles: any;
        };
    }>;
    getProfile(req: ExpressRequest): Express.User | undefined;
}
