import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type AppSettingsDocument = AppSettings & Document;

@Schema({
  collection: 'kd_app_settings',
  timestamps: true,
})
export class AppSettings {
  @Prop({ required: true })
  type: string;

  @Prop({ required: true })
  name: string;

  @Prop({ type: [Object], default: [] })
  settings: Array<{
    key: string;
    value: any;
    type: string;
  }>;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company', required: false, index: true })
  companyId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: MongooseSchema.Types.ObjectId;
}

export const AppSettingsSchema = SchemaFactory.createForClass(AppSettings);
