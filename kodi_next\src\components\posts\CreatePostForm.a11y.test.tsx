import React from 'react';
import { render } from '@testing-library/react';
import { axe, runAxe } from '@/test-utils/axe-setup';
import CreatePostForm from './CreatePostForm';

// Mock the API
jest.mock('@/lib/api', () => ({
  get: jest.fn(() => Promise.resolve({ data: [] })),
  post: jest.fn(),
  endpoints: {
    teams: {
      base: '/teams',
    },
    postTypes: {
      base: '/post-types',
    },
    posts: {
      base: '/posts',
    },
  },
}));

// Mock the AudioRecorder component
jest.mock('./AudioRecorder', () => {
  return function MockAudioRecorder() {
    return <div data-testid="audio-recorder">Audio Recorder</div>;
  };
});

// Mock the AuthContext
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn(() => ({
    user: {
      id: 'user-1',
      profile: {
        companyId: 'company-1',
      },
    },
  })),
}));

describe('CreatePostForm Accessibility', () => {
  it('should not have accessibility violations', async () => {
    const { container } = render(<CreatePostForm onPostCreated={() => {}} />);
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });

  it('should not have accessibility violations with error message', async () => {
    // Force an error to be displayed
    const { container, getByText, getByRole } = render(<CreatePostForm onPostCreated={() => {}} />);
    
    // Submit the form without content to trigger error
    const submitButton = getByRole('button', { name: /Save Draft|Post/i });
    submitButton.click();
    
    // Wait for error message to appear
    await waitFor(() => {
      expect(getByText(/Post content cannot be empty/i)).toBeInTheDocument();
    });
    
    const results = await runAxe(container);
    expect(results).toHaveNoViolations();
  });
});

// Helper function for waitFor
import { waitFor } from '@testing-library/react';
