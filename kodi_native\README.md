# Kodi Native

A React Native mobile app for the Kodi platform.

## Installation

## Running the App

After installation, you can run the app using:

```bash
# Start the app
npm start

# Run on iOS
npm run ios

# Run on Android
npm run android
```

### Windows Users

For Windows users, we've included batch files for easier startup:

- Double-click `start.bat` to run the app
- Double-click `start-android.bat` to run on Android
- Double-click `start-ios.bat` to run on iOS

### Using npx directly

You can also run the app directly with npx:

```bash
# First run the ajv fix script
node fix-ajv.js

# Then start the app
npx expo start
```

> **Note:** This project uses the new local Expo CLI (not the legacy global expo-cli). The legacy expo-cli does not support Node 17+. If you previously had the global expo-cli installed, it has been removed during the migration process.

## Troubleshooting

### Error: "Cannot find module 'ajv/dist/compile/codegen'"

If you encounter this error:

1. Make sure you've run `npm install` which will apply the patch-package patches
2. If the error persists, try reinstalling:
   ```bash
   rm -rf node_modules
   rm -f package-lock.json
   npm install
   ```
3. Verify that the patch was applied by checking if this file exists:
   ```
   node_modules/ajv/dist/compile/codegen/index.js
   ```

### Error: "expo is not recognized as an internal or external command"

If you encounter this error:

1. Use npx to run expo commands:
   ```bash
   npx expo start
   ```

2. Or use the provided batch files:
   - `start.bat` for general startup
   - `start-android.bat` for Android
   - `start-ios.bat` for iOS

3. If the error persists, try reinstalling the dependencies:
   ```bash
   node install.js
   ```

4. You can also try installing expo-cli as a global package (not recommended for Node 17+):
   ```bash
   npm install -g expo-cli@latest
   ```

## Development

This app is built with:
- React Native
- Expo
- TypeScript

The app follows the same structure and functionality as the Next.js app, but adapted for mobile using React Native and Expo.
