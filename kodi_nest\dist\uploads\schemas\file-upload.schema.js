"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileUploadSchema = exports.FileUpload = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let FileUpload = class FileUpload {
    originalName;
    mimeType;
    size;
    key;
    url;
    type;
    userId;
    linkedObjectId;
    objectType;
};
exports.FileUpload = FileUpload;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], FileUpload.prototype, "originalName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], FileUpload.prototype, "mimeType", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], FileUpload.prototype, "size", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], FileUpload.prototype, "key", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], FileUpload.prototype, "url", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: String,
        required: true,
        enum: ['avatar', 'logo', 'audio', 'document', 'other']
    }),
    __metadata("design:type", String)
], FileUpload.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'User', required: true }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], FileUpload.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: mongoose_2.Schema.Types.ObjectId,
        refPath: 'objectType',
        required: false
    }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], FileUpload.prototype, "linkedObjectId", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: String,
        required: false,
        enum: ['Post', 'User', 'Company', 'Insight']
    }),
    __metadata("design:type", String)
], FileUpload.prototype, "objectType", void 0);
exports.FileUpload = FileUpload = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'kd_file_uploads',
        timestamps: true,
    })
], FileUpload);
exports.FileUploadSchema = mongoose_1.SchemaFactory.createForClass(FileUpload);
//# sourceMappingURL=file-upload.schema.js.map