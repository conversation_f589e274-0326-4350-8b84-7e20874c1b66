import type { <PERSON>a, StoryObj } from '@storybook/react';
import CreatePostForm from './CreatePostForm';
import { rest } from 'msw';

// Mock API responses for Storybook
const mockTeams = [
  { _id: 'team-1', name: 'Engineering' },
  { _id: 'team-2', name: 'Marketing' },
  { _id: 'team-3', name: 'Sales' },
];

const mockPostTypes = [
  { 
    _id: 'type-1', 
    name: '🌎 Strategic feedback', 
    recordTips: `
      <ul>
        <li>What's working?</li>
        <li>What's not working?</li>
        <li>What are the opportunities?</li>
      </ul>
    ` 
  },
  { 
    _id: 'type-2', 
    name: '🔦 Product QA',
    recordTips: `
      <ul>
        <li>What have you found to fix?</li>
        <li>What is odd?</li>
        <li>What needs to be improved?</li>
      </ul>
    `
  },
  { 
    _id: 'type-3', 
    name: '📣 Shoutouts',
    recordTips: `
      <ul>
        <li>Who has been crushing it lately?</li>
      </ul>
    `
  },
];

const meta: Meta<typeof CreatePostForm> = {
  title: 'Components/Posts/CreatePostForm',
  component: CreatePostForm,
  parameters: {
    layout: 'centered',
    msw: {
      handlers: [
        rest.get('*/api/teams*', (req, res, ctx) => {
          return res(ctx.json(mockTeams));
        }),
        rest.get('*/api/post-types*', (req, res, ctx) => {
          return res(ctx.json(mockPostTypes));
        }),
        rest.post('*/api/posts', (req, res, ctx) => {
          return res(ctx.json({ _id: 'new-post-id' }));
        }),
      ],
    },
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="max-w-3xl mx-auto">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof CreatePostForm>;

export const Default: Story = {
  args: {
    onPostCreated: () => console.log('Post created'),
  },
};

export const WithError: Story = {
  args: {
    onPostCreated: () => console.log('Post created'),
  },
  play: async ({ canvasElement }) => {
    // This will be executed after the story renders
    // We can use it to simulate user interactions
    const canvas = within(canvasElement);
    const submitButton = canvas.getByRole('button', { name: /Save Draft/i });
    await userEvent.click(submitButton);
  },
};

export const WithAudioRecording: Story = {
  args: {
    onPostCreated: () => console.log('Post created'),
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the form after audio has been recorded and transcribed.',
      },
    },
  },
};

// Helper functions for the play function
import { within } from '@storybook/testing-library';
import { userEvent } from '@storybook/testing-library';
