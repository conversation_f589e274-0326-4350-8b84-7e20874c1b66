import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

interface RadioButtonProps {
  label: string;
  value: string;
  selectedValue: string;
  onSelect: (value: string) => void;
  colors: any;
  count?: number;
}

export default function RadioButton({
  label,
  value,
  selectedValue,
  onSelect,
  colors,
  count,
}: RadioButtonProps) {
  const isSelected = value === selectedValue;

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onSelect(value)}
      accessibilityRole="radio"
      accessibilityState={{ checked: isSelected }}
    >
      <View
        style={[
          styles.radioOuter,
          { borderColor: isSelected ? colors.primary : colors.border },
        ]}
      >
        {isSelected && (
          <View
            style={[styles.radioInner, { backgroundColor: colors.primary }]}
          />
        )}
      </View>
      <View style={styles.labelContainer}>
        <Text style={[styles.label, { color: colors.foreground }]}>
          {label}
        </Text>
        {count !== undefined && (
          <Text style={[styles.count, { color: colors.mutedForeground }]}>
            ({count})
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  label: {
    fontSize: 14,
  },
  count: {
    fontSize: 14,
    marginLeft: 4,
  },
});
