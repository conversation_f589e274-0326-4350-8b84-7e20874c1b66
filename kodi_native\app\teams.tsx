import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Modal,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Button from '@/src/components/ui/Button';
import Card from '@/src/components/ui/Card';
import Input from '@/src/components/ui/Input';
import { Team } from '@/src/types';

export default function TeamsScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [teamName, setTeamName] = useState('');
  const [teamDescription, setTeamDescription] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  
  useEffect(() => {
    fetchTeams();
  }, []);
  
  const fetchTeams = async () => {
    try {
      setLoading(true);
      const { data } = await api.get(endpoints.teams.base);
      setTeams(data);
    } catch (err) {
      console.error('Failed to fetch teams:', err);
      setError('Failed to load teams. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  const handleGoBack = () => {
    router.back();
  };
  
  const openCreateModal = () => {
    setTeamName('');
    setTeamDescription('');
    setModalVisible(true);
  };
  
  const closeModal = () => {
    setModalVisible(false);
  };
  
  const createTeam = async () => {
    if (!teamName.trim()) {
      Alert.alert('Error', 'Team name is required');
      return;
    }
    
    try {
      setIsSaving(true);
      
      const newTeam = {
        name: teamName.trim(),
        description: teamDescription.trim() || undefined,
        companyId: user?.profile?.companyId,
        createdBy: user?.id,
      };
      
      await api.post(endpoints.teams.base, newTeam);
      
      closeModal();
      fetchTeams();
      Alert.alert('Success', 'Team created successfully');
    } catch (err: any) {
      console.error('Failed to create team:', err);
      Alert.alert('Error', err.response?.data?.message || 'Failed to create team. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };
  
  const viewTeamDetails = (teamId: string) => {
    router.push(`/team/${teamId}`);
  };
  
  const renderTeamItem = ({ item }: { item: Team }) => (
    <TouchableOpacity
      style={[styles.teamItem, { backgroundColor: colors.card }]}
      onPress={() => viewTeamDetails(item._id)}
    >
      <View style={styles.teamItemContent}>
        <View style={[styles.teamIcon, { backgroundColor: colors.primary + '20' }]}>
          <Text style={[styles.teamIconText, { color: colors.primary }]}>
            {item.name[0].toUpperCase()}
          </Text>
        </View>
        <View style={styles.teamInfo}>
          <Text style={[styles.teamName, { color: colors.foreground }]}>
            {item.name}
          </Text>
          {item.description ? (
            <Text style={[styles.teamDescription, { color: colors.mutedForeground }]}>
              {item.description}
            </Text>
          ) : null}
          <Text style={[styles.teamMembers, { color: colors.mutedForeground }]}>
            {item.members?.length || 0} members
          </Text>
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color={colors.mutedForeground} />
    </TouchableOpacity>
  );
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Teams</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={openCreateModal}
        >
          <Ionicons name="add" size={24} color={colors.primaryForeground} />
        </TouchableOpacity>
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : error ? (
        <Card style={styles.errorCard}>
          <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
          <Button
            title="Try Again"
            onPress={fetchTeams}
            style={styles.retryButton}
          />
        </Card>
      ) : teams.length === 0 ? (
        <Card style={styles.emptyCard}>
          <Ionicons name="people" size={48} color={colors.mutedForeground} />
          <Text style={[styles.emptyTitle, { color: colors.foreground }]}>
            No Teams Yet
          </Text>
          <Text style={[styles.emptyDescription, { color: colors.mutedForeground }]}>
            Create your first team to start collaborating with your colleagues
          </Text>
          <Button
            title="Create Team"
            onPress={openCreateModal}
            style={styles.createButton}
          />
        </Card>
      ) : (
        <FlatList
          data={teams}
          renderItem={renderTeamItem}
          keyExtractor={(item) => item._id}
          contentContainerStyle={styles.listContent}
        />
      )}
      
      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={closeModal}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.foreground }]}>
                Create New Team
              </Text>
              <TouchableOpacity onPress={closeModal}>
                <Ionicons name="close" size={24} color={colors.mutedForeground} />
              </TouchableOpacity>
            </View>
            
            <Input
              label="Team Name"
              value={teamName}
              onChangeText={setTeamName}
              placeholder="Enter team name"
              leftIcon={<Ionicons name="people-outline" size={20} color={colors.mutedForeground} />}
            />
            
            <Input
              label="Description (Optional)"
              value={teamDescription}
              onChangeText={setTeamDescription}
              placeholder="Enter team description"
              multiline
              numberOfLines={3}
              leftIcon={<Ionicons name="information-circle-outline" size={20} color={colors.mutedForeground} />}
            />
            
            <View style={styles.modalActions}>
              <Button
                title="Cancel"
                variant="outline"
                onPress={closeModal}
                style={styles.cancelButton}
              />
              <Button
                title="Create"
                onPress={createTeam}
                isLoading={isSaving}
                style={styles.submitButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
    marginLeft: 12,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorCard: {
    margin: 16,
    padding: 16,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    minWidth: 120,
  },
  emptyCard: {
    margin: 16,
    padding: 24,
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  createButton: {
    minWidth: 160,
  },
  listContent: {
    padding: 16,
  },
  teamItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  teamItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  teamIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  teamIconText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  teamInfo: {
    flex: 1,
  },
  teamName: {
    fontSize: 16,
    fontWeight: '600',
  },
  teamDescription: {
    fontSize: 14,
    marginTop: 2,
  },
  teamMembers: {
    fontSize: 12,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    borderRadius: 12,
    padding: 20,
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 20,
  },
  cancelButton: {
    marginRight: 12,
  },
  submitButton: {
    minWidth: 100,
  },
});
