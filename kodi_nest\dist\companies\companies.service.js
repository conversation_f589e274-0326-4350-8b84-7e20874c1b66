"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompaniesService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const company_schema_1 = require("./schemas/company.schema");
const crypto = require("crypto");
const core_1 = require("@nestjs/core");
let CompaniesService = class CompaniesService {
    companyModel;
    moduleRef;
    postsService;
    constructor(companyModel, moduleRef) {
        this.companyModel = companyModel;
        this.moduleRef = moduleRef;
    }
    onModuleInit() {
        try {
            const { PostsService } = require('../posts/posts.service');
            this.postsService = this.moduleRef.get(PostsService, { strict: false });
        }
        catch (error) {
            console.warn('Could not load PostsService:', error.message);
        }
    }
    async create(createCompanyDto) {
        if (!createCompanyDto.invite_code) {
            createCompanyDto.invite_code = this.generateInviteCode();
        }
        const newCompany = new this.companyModel(createCompanyDto);
        const company = await newCompany.save();
        if (this.postsService) {
            await this.createDefaultPostTypes(company._id.toString(), createCompanyDto.createdBy);
        }
        return company;
    }
    async createDefaultPostTypes(companyId, userId) {
        const defaultPostTypes = [
            {
                name: "🌎 Strategic feedback",
                description: "<div>Strategic feedback</div>",
                recordTips: "<div>What's working?<br>What's not working?<br>What are the opportunities?</div>",
                archived: false
            },
            {
                name: "🔦 Product QA",
                description: "<div>Async QA on the product</div>",
                recordTips: "<div>What have you found to fix?<br>What is odd?<br>What needs to be improved?</div>",
                archived: false,
            },
            {
                name: "📣 Shoutouts",
                description: "<div>Highlight who has been crushing it lately</div>",
                recordTips: "<div>Who has been crushing it lately?</div>",
                archived: false,
            },
            {
                name: "🗓️ Agenda Generator",
                description: "<div>Turn inputs into an agenda.&nbsp;</div>",
                recordTips: "<div>The purpose here is to have everyone take 1-2 minutes and provide input based on the suggested topics below, which we'll then turn into our agenda.&nbsp;</div><div><br>📰 <strong>News</strong> - Any updates, highlights, or noteworthy FYI announcements?</div><div>🛑 <strong>Problems</strong> - Any problems or issues to be addressed or resolved?</div><div>🚧 <strong>Blockers</strong> - Any hindrance due to an external dependency?</div><div>⚖️ <strong>Decisions</strong> - Any outstanding decisions to be made?</div><div>👥 <strong>Collaboration</strong> - Any review or feedback on anything related to dev or design?<br>🌐 <strong>Misc</strong> - Any additional items to bring up not covered by these topics?</div>",
                archived: false,
            },
            {
                name: "🔄 Discoveries & demos",
                recordTips: "<div>What are the problems/pains?<br>How are they solving today?<br>What is timeline? Budget? Authority?</div>",
                description: "<div>Share insight feedback from discovery calls and demos.</div>",
                archived: false
            },
            {
                name: "🌤️ Weekend Recap",
                description: "<div>Share your weekend</div>",
                recordTips: "<div>How was the weekend?<br>What did you do?<br>Where did you go?<br><br>#weekend #backtowork&nbsp;</div>",
                archived: false
            },
            {
                name: "🗓️ Monthly Review",
                archived: false,
                description: "<div>Reflect back on the prior month</div>",
                recordTips: "<div>What did you achieve?<br>What problems did you overcome?<br>What will you do this next month?</div>"
            },
            {
                name: "🚀 Objective MVP Launch",
                description: "<div>Q1-2024 project</div>",
                recordTips: "<div>What's the latest with this Objective?</div>",
                archived: false
            },
            {
                name: "⏱️ 30 Second Pitch",
                description: "<div>Share a quick pitch</div>",
                recordTips: "<ul><li>What problem are you solving?</li><li>What's the solution?</li><li>How is it different?</li><li>What's the vision?</li><li>Why now?</li></ul>",
                archived: false,
            },
            {
                name: "👥 Daily Standup",
                archived: false,
                description: "<div>Share what's happening today</div>",
                recordTips: "<div>What's your current priority?<br>What did you do yesterday?<br>Any blockers?</div>"
            },
            {
                name: "🎬 Weekly Staff Update",
                archived: false,
                description: "<div>Wrap up the week that was</div>",
                recordTips: "<div>What are the 1-2 highlights you want to share?<br>What are 1-2 lowlights you want to share?<br>What else do you want to share with the team?</div>"
            },
            {
                name: "⭐ Priority Visibility",
                description: "<div>Share your priorities</div>",
                recordTips: "<div>What are the top priorities you're working on?</div>",
                archived: false
            },
            {
                name: "📅 Daily Wrap Up",
                description: "<div>Retrospective on the day that was</div>",
                recordTips: "<div>How was your day?<br>What was accomplished?<br>What's on deck for tomorrow?<br>Any blockers or help needed?</div><div><br></div>",
                archived: false
            },
            {
                name: "💭 Product Opinions",
                description: "<div>Share async internal product feedback</div>",
                recordTips: "<ul><li>Share your opinions on the product.</li><li>Opinions are not requests, but rather informal feedback.</li><li>Provide a mix of likes, dislikes, and ideas.</li><li><br></li></ul><div><br></div>",
                archived: false
            }
        ];
        for (const postType of defaultPostTypes) {
            await this.postsService.createPostType({
                ...postType,
                companyId,
                userId
            });
        }
    }
    async findAll() {
        return this.companyModel.find().exec();
    }
    async findById(id) {
        const company = await this.companyModel.findById(id).exec();
        if (!company) {
            throw new common_1.NotFoundException(`Company with ID ${id} not found`);
        }
        return company;
    }
    async findByInviteCode(inviteCode) {
        return this.companyModel.findOne({ invite_code: inviteCode }).exec();
    }
    async update(id, updateCompanyDto) {
        const updatedCompany = await this.companyModel
            .findByIdAndUpdate(id, updateCompanyDto, { new: true })
            .exec();
        if (!updatedCompany) {
            throw new common_1.NotFoundException(`Company with ID ${id} not found`);
        }
        return updatedCompany;
    }
    async remove(id) {
        const deletedCompany = await this.companyModel.findByIdAndDelete(id).exec();
        if (!deletedCompany) {
            throw new common_1.NotFoundException(`Company with ID ${id} not found`);
        }
        return deletedCompany;
    }
    generateInviteCode() {
        return crypto.randomBytes(4).toString('hex');
    }
};
exports.CompaniesService = CompaniesService;
exports.CompaniesService = CompaniesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(company_schema_1.Company.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        core_1.ModuleRef])
], CompaniesService);
//# sourceMappingURL=companies.service.js.map