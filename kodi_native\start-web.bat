@echo off
echo Kodi Native app for Web
echo.

echo Choose an option:
echo 1. Start development server
echo 2. Build for production
echo 3. Build and serve production build
echo.

set /p option="Enter option (1-3): "

if "%option%"=="1" (
  echo Starting development server...
  npx expo start --web
) else if "%option%"=="2" (
  echo Building for production...
  npx expo export --platform web
  echo.
  echo Build completed. Files are in the web-build directory.
) else if "%option%"=="3" (
  echo Building and serving production build...
  npx expo export --platform web
  echo.
  echo Starting server on http://localhost:3000
  npx serve web-build
) else (
  echo Invalid option selected.
  exit /b 1
)

echo.
if %ERRORLEVEL% NEQ 0 (
  echo Error: Failed to complete the operation. See error messages above.
  pause
  exit /b 1
)
