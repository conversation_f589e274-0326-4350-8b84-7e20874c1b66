# PowerShell script to convert SVG to PNG files for the app icons
# Requires Inkscape to be installed

# Path to the SVG file
$svgPath = "..\..\icon.svg"

# Output paths
$iconPath = "..\icon.png"
$adaptiveIconPath = "..\adaptive-icon.png"
$splashIconPath = "..\splash-icon.png"

# Check if Inkscape is installed
try {
    $inkscapePath = (Get-Command inkscape -ErrorAction Stop).Source
    Write-Host "Found Inkscape at: $inkscapePath"
} catch {
    Write-Host "Inkscape not found. Using alternative method."
    $inkscapePath = $null
}

# Function to convert SVG to PNG using Inkscape
function Convert-SvgToPng {
    param (
        [string]$inputFile,
        [string]$outputFile,
        [int]$width,
        [int]$height
    )
    
    if ($inkscapePath) {
        # Use Inkscape if available
        & $inkscapePath --export-filename="$outputFile" --export-width=$width --export-height=$height "$inputFile"
    } else {
        # Alternative method using System.Drawing
        Add-Type -AssemblyName System.Drawing
        
        # Create a bitmap with the specified dimensions
        $bitmap = New-Object System.Drawing.Bitmap $width, $height
        
        # Create a graphics object from the bitmap
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Set the background color to white
        $graphics.Clear([System.Drawing.Color]::White)
        
        # Save the bitmap to the output file
        $bitmap.Save($outputFile, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # Dispose of the graphics and bitmap objects
        $graphics.Dispose()
        $bitmap.Dispose()
        
        Write-Host "Created blank PNG file at $outputFile (manual editing required)"
    }
}

# Create the icon files
Write-Host "Creating app icon..."
Convert-SvgToPng -inputFile $svgPath -outputFile $iconPath -width 1024 -height 1024

Write-Host "Creating adaptive icon..."
Convert-SvgToPng -inputFile $svgPath -outputFile $adaptiveIconPath -width 1024 -height 1024

Write-Host "Creating splash icon..."
Convert-SvgToPng -inputFile $svgPath -outputFile $splashIconPath -width 1024 -height 1024

Write-Host "Icon conversion complete!"
