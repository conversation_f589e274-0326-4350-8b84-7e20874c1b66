import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { ProfilesService } from '../profiles/profiles.service';
import { CompaniesService } from '../companies/companies.service';
import { RegisterDto } from './dto/register.dto';
export declare class AuthService {
    private usersService;
    private profilesService;
    private companiesService;
    private jwtService;
    constructor(usersService: UsersService, profilesService: ProfilesService, companiesService: CompaniesService, jwtService: JwtService);
    validateUser(email: string, password: string): Promise<any>;
    register(registerDto: RegisterDto): Promise<{
        access_token: string;
        refresh_token: string;
        user: {
            id: any;
            email: any;
            profile: any;
            roles: any;
        };
    }>;
    login(user: any): Promise<{
        access_token: string;
        refresh_token: string;
        user: {
            id: any;
            email: any;
            profile: any;
            roles: any;
        };
    }>;
    refreshToken(refreshToken: string): Promise<{
        access_token: string;
        refresh_token: string;
        user: {
            id: any;
            email: any;
            profile: any;
            roles: any;
        };
    }>;
    private generateTokens;
}
