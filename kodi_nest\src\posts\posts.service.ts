import { Injectable, NotFoundException, BadRequestException, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Post, PostDocument } from './schemas/post.schema';
import { PostType, PostTypeDocument } from './schemas/post-type.schema';
import { Tag, TagDocument } from './schemas/tag.schema';
import { Comment, CommentDocument } from './schemas/comment.schema';
import { CreatePostDto } from './dto/create-post.dto';
import { UpdatePostDto } from './dto/update-post.dto';
import { CreatePostTypeDto } from './dto/create-post-type.dto';
import { CreateTagDto } from './dto/create-tag.dto';
import { CreateCommentDto } from './dto/create-comment.dto';
import { ModuleRef } from '@nestjs/core';

@Injectable()
export class PostsService implements OnModuleInit {
  private companiesService: any;

  constructor(
    @InjectModel(Post.name) private postModel: Model<PostDocument>,
    @InjectModel(PostType.name) private postTypeModel: Model<PostTypeDocument>,
    @InjectModel(Tag.name) private tagModel: Model<TagDocument>,
    @InjectModel(Comment.name) private commentModel: Model<CommentDocument>,
    private moduleRef: ModuleRef
  ) {}

  onModuleInit() {
    // We use moduleRef to avoid circular dependency
    try {
      // Import the CompaniesService class dynamically to avoid circular dependency
      const { CompaniesService } = require('../companies/companies.service');
      this.companiesService = this.moduleRef.get(CompaniesService, { strict: false });
    } catch (error) {
      console.warn('Could not load CompaniesService:', error.message);
    }
  }

  // Post methods
  async createPost(createPostDto: CreatePostDto): Promise<PostDocument> {
    // Extract hashtags from the post body
    const hashtags = this.extractHashtags(createPostDto.body);
    createPostDto.hashtags = hashtags;

    // Create the post
    const newPost = new this.postModel(createPostDto);
    const post = await newPost.save();

    // Update or create tags
    await this.updateTags(hashtags, createPostDto.companyId);

    return post;
  }

  async findAllPosts(
    companyId?: string,
    teamId?: string,
    postTypeId?: string,
    tag?: string,
    isDraft?: boolean,
  ): Promise<PostDocument[]> {
    const query: any = {};

    if (companyId) {
      query.companyId = companyId;
    }

    if (teamId) {
      query.teamId = teamId;
    }

    if (postTypeId) {
      query.postTypeId = postTypeId;
    }

    if (tag) {
      query.hashtags = tag;
    }

    if (isDraft !== undefined) {
      query.isDraft = isDraft;
    }

    return this.postModel
      .find(query)
      .sort({ createdAt: -1 })
      .exec();
  }

  async findPostById(id: string): Promise<PostDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid post ID format: ${id}`);
    }

    const post = await this.postModel.findById(id).exec();
    if (!post) {
      throw new NotFoundException(`Post with ID ${id} not found`);
    }
    return post;
  }

  async updatePost(id: string, updatePostDto: UpdatePostDto): Promise<PostDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid post ID format: ${id}`);
    }

    // If body is updated, extract hashtags
    if (updatePostDto.body) {
      const hashtags = this.extractHashtags(updatePostDto.body);
      updatePostDto.hashtags = hashtags;

      // Get the post to get the companyId
      const post = await this.postModel.findById(id).exec();
      if (post && post.companyId) {
        await this.updateTags(hashtags, post.companyId.toString());
      }
    }

    const updatedPost = await this.postModel
      .findByIdAndUpdate(id, updatePostDto, { new: true })
      .exec();

    if (!updatedPost) {
      throw new NotFoundException(`Post with ID ${id} not found`);
    }

    return updatedPost;
  }

  async removePost(id: string): Promise<PostDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid post ID format: ${id}`);
    }

    // Remove all comments for this post
    await this.commentModel.deleteMany({
      linkedObjectId: id,
      objectType: 'Post',
    }).exec();

    const deletedPost = await this.postModel.findByIdAndDelete(id).exec();

    if (!deletedPost) {
      throw new NotFoundException(`Post with ID ${id} not found`);
    }

    return deletedPost;
  }

  // Post Type methods
  async createPostType(createPostTypeDto: CreatePostTypeDto): Promise<PostTypeDocument> {
    const newPostType = new this.postTypeModel(createPostTypeDto);
    return newPostType.save();
  }

  async findAllPostTypes(companyId?: string): Promise<PostTypeDocument[]> {
    const query = companyId ? { companyId, archived: false } : { archived: false };
    return this.postTypeModel.find(query).exec();
  }

  async findPostTypeById(id: string): Promise<PostTypeDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid post type ID format: ${id}`);
    }

    const postType = await this.postTypeModel.findById(id).exec();
    if (!postType) {
      throw new NotFoundException(`Post type with ID ${id} not found`);
    }
    return postType;
  }

  async updatePostType(id: string, updatePostTypeDto: any): Promise<PostTypeDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid post type ID format: ${id}`);
    }

    const updatedPostType = await this.postTypeModel
      .findByIdAndUpdate(id, updatePostTypeDto, { new: true })
      .exec();

    if (!updatedPostType) {
      throw new NotFoundException(`Post type with ID ${id} not found`);
    }

    return updatedPostType;
  }

  async removePostType(id: string): Promise<PostTypeDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid post type ID format: ${id}`);
    }

    // Instead of deleting, mark as archived
    const archivedPostType = await this.postTypeModel
      .findByIdAndUpdate(id, { archived: true }, { new: true })
      .exec();

    if (!archivedPostType) {
      throw new NotFoundException(`Post type with ID ${id} not found`);
    }

    return archivedPostType;
  }

  // Tag methods
  async createTag(createTagDto: CreateTagDto): Promise<TagDocument> {
    // Check if tag already exists for this company
    const existingTag = await this.tagModel.findOne({
      name: createTagDto.name,
      companyId: createTagDto.companyId,
    }).exec();

    if (existingTag) {
      throw new BadRequestException(`Tag ${createTagDto.name} already exists for this company`);
    }

    const newTag = new this.tagModel(createTagDto);
    return newTag.save();
  }

  async findAllTags(companyId?: string): Promise<TagDocument[]> {
    const query = companyId ? { companyId } : {};
    return this.tagModel.find(query).sort({ count: -1 }).exec();
  }

  async findTagById(id: string): Promise<TagDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid tag ID format: ${id}`);
    }

    const tag = await this.tagModel.findById(id).exec();
    if (!tag) {
      throw new NotFoundException(`Tag with ID ${id} not found`);
    }
    return tag;
  }

  async updateTag(id: string, updateTagDto: any): Promise<TagDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid tag ID format: ${id}`);
    }

    const updatedTag = await this.tagModel
      .findByIdAndUpdate(id, updateTagDto, { new: true })
      .exec();

    if (!updatedTag) {
      throw new NotFoundException(`Tag with ID ${id} not found`);
    }

    return updatedTag;
  }

  async removeTag(id: string): Promise<TagDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid tag ID format: ${id}`);
    }

    const deletedTag = await this.tagModel.findByIdAndDelete(id).exec();

    if (!deletedTag) {
      throw new NotFoundException(`Tag with ID ${id} not found`);
    }

    return deletedTag;
  }

  // Comment methods
  async createComment(createCommentDto: CreateCommentDto): Promise<CommentDocument> {
    const newComment = new this.commentModel(createCommentDto);
    const comment = await newComment.save();

    // Update the referenced object with the comment ID
    if (createCommentDto.objectType === 'Post') {
      await this.postModel.findByIdAndUpdate(
        createCommentDto.linkedObjectId,
        { $push: { comments: comment._id } }
      ).exec();
    }

    return comment;
  }

  async findAllComments(objectType: string, objectId: string): Promise<CommentDocument[]> {
    return this.commentModel
      .find({ objectType, linkedObjectId: objectId })
      .sort({ createdAt: 1 })
      .exec();
  }

  async findCommentById(id: string): Promise<CommentDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid comment ID format: ${id}`);
    }

    const comment = await this.commentModel.findById(id).exec();
    if (!comment) {
      throw new NotFoundException(`Comment with ID ${id} not found`);
    }
    return comment;
  }

  async updateComment(id: string, updateCommentDto: any): Promise<CommentDocument> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid comment ID format: ${id}`);
    }

    const updatedComment = await this.commentModel
      .findByIdAndUpdate(id, updateCommentDto, { new: true })
      .exec();

    if (!updatedComment) {
      throw new NotFoundException(`Comment with ID ${id} not found`);
    }

    return updatedComment;
  }

  async removeComment(id: string): Promise<CommentDocument | null> {
    // Check if the id is a valid ObjectId to prevent errors
    if (!/^[0-9a-fA-F]{24}$/.test(id)) {
      throw new BadRequestException(`Invalid comment ID format: ${id}`);
    }

    const comment = await this.commentModel.findById(id).exec();
    if (!comment) {
      throw new NotFoundException(`Comment with ID ${id} not found`);
    }

    // Remove the comment ID from the referenced object
    if (comment.objectType === 'Post') {
      await this.postModel.findByIdAndUpdate(
        comment.linkedObjectId,
        { $pull: { comments: comment._id } }
      ).exec();
    }

    const deletedComment = await this.commentModel.findByIdAndDelete(id).exec();
    if (!deletedComment) {
      throw new NotFoundException(`Comment with ID ${id} not found`);
    }
    return deletedComment;
  }

  // Helper methods
  private extractHashtags(text: string): string[] {
    const hashtagRegex = /#(\w+)/g;
    const matches = text.match(hashtagRegex);

    if (!matches) {
      return [];
    }

    // Remove the # symbol and return unique hashtags
    return [...new Set(matches.map(tag => tag.substring(1).toLowerCase()))];
  }

  private async updateTags(hashtags: string[], companyId?: string): Promise<void> {
    for (const tag of hashtags) {
      // Check if tag exists
      const existingTag = await this.tagModel.findOne({
        name: tag,
        companyId: companyId || null,
      }).exec();

      if (existingTag) {
        // Increment count
        await this.tagModel.findByIdAndUpdate(
          existingTag._id,
          { $inc: { count: 1 } }
        ).exec();
      } else {
        // Create new tag
        const createTagDto: CreateTagDto = {
          name: tag,
          count: 1,
        };

        if (companyId) {
          createTagDto.companyId = companyId;
        }

        const newTag = new this.tagModel(createTagDto);
        await newTag.save();
      }
    }
  }
}
