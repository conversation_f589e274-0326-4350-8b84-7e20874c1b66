'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import Link from 'next/link';
import AudioRecorder from '@/components/posts/AudioRecorder';

interface PostType {
  _id: string;
  name: string;
  recordTips?: string;
}

interface Team {
  _id: string;
  name: string;
}

export default function CreatePostPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  
  const postTypeId = searchParams.get('postTypeId');
  const teamId = searchParams.get('teamId');
  
  const [postType, setPostType] = useState<PostType | null>(null);
  const [team, setTeam] = useState<Team | null>(null);
  const [body, setBody] = useState('');
  const [audioUrl, setAudioUrl] = useState('');
  const [transcription, setTranscription] = useState('');
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [hashtagInput, setHashtagInput] = useState('');
  const [isDraft, setIsDraft] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!postTypeId) {
      router.push('/post/type-selection');
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch post type details
        const { data: postTypeData } = await api.get(endpoints.postTypes.byId(postTypeId));
        setPostType(postTypeData);
        
        // Fetch team details if teamId is provided
        if (teamId) {
          const { data: teamData } = await api.get(endpoints.teams.byId(teamId));
          setTeam(teamData);
        }
      } catch (err: any) {
        console.error('Failed to fetch data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [postTypeId, teamId, router]);

  const handleAudioRecorded = (url: string, text: string) => {
    setAudioUrl(url);
    setTranscription(text);
    setBody(text);
    
    // Extract hashtags from transcription
    const hashtagRegex = /#(\w+)/g;
    const matches = text.match(hashtagRegex);
    
    if (matches) {
      const extractedTags = matches.map(tag => tag.substring(1));
      setHashtags(extractedTags);
    }
  };

  const handleAddHashtag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && hashtagInput.trim()) {
      e.preventDefault();
      if (!hashtags.includes(hashtagInput.trim())) {
        setHashtags([...hashtags, hashtagInput.trim()]);
      }
      setHashtagInput('');
    }
  };

  const handleRemoveHashtag = (tag: string) => {
    setHashtags(hashtags.filter(t => t !== tag));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!body.trim()) {
      setError('Please enter some content for your post.');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      await api.post(endpoints.posts.base, {
        body,
        teamId: teamId || undefined,
        postTypeId: postTypeId || undefined,
        companyId: user?.profile?.companyId,
        userId: user?.id,
        isDraft,
        audioUrl: audioUrl || undefined,
        hashtags,
      });

      // Redirect to feed after post is created
      router.push('/feed');
    } catch (err: any) {
      console.error('Failed to create post:', err);
      setError(err.response?.data?.message || 'Failed to create post. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Create a Post</h1>
          <div className="flex space-x-4">
            <Link 
              href="/post/pre-record"
              className="text-sm text-indigo-600 hover:text-indigo-800"
            >
              Back
            </Link>
            <Link 
              href="/feed"
              className="text-sm text-gray-600 hover:text-gray-800"
            >
              Cancel
            </Link>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 p-4 rounded-md mb-4">
            <p className="text-red-700">{error}</p>
          </div>
        ) : (
          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="mb-4 flex flex-wrap gap-2">
              {postType && (
                <div className="inline-block px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm">
                  {postType.name}
                </div>
              )}
              {team && (
                <div className="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                  Team: {team.name}
                </div>
              )}
            </div>
            
            <form onSubmit={handleSubmit}>
              <div className="space-y-4">
                {/* Audio Recorder */}
                <AudioRecorder
                  onAudioRecorded={handleAudioRecorded}
                  recordTips={postType?.recordTips}
                />
                
                {/* Post Content */}
                <div>
                  <label htmlFor="body" className="block text-sm font-medium text-gray-700">
                    What's on your mind?
                  </label>
                  <textarea
                    id="body"
                    rows={6}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    placeholder="Enter your post content here..."
                    value={body}
                    onChange={(e) => setBody(e.target.value)}
                  ></textarea>
                </div>
                
                {/* Hashtags */}
                <div>
                  <label htmlFor="hashtags" className="block text-sm font-medium text-gray-700">
                    Hashtags
                  </label>
                  <div className="mt-1 flex flex-wrap gap-2 mb-2">
                    {hashtags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                      >
                        #{tag}
                        <button
                          type="button"
                          onClick={() => handleRemoveHashtag(tag)}
                          className="ml-1 text-indigo-500 hover:text-indigo-700"
                        >
                          &times;
                        </button>
                      </span>
                    ))}
                  </div>
                  <input
                    type="text"
                    id="hashtags"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    placeholder="Add hashtags (press Enter to add)"
                    value={hashtagInput}
                    onChange={(e) => setHashtagInput(e.target.value)}
                    onKeyDown={handleAddHashtag}
                  />
                </div>
                
                {/* Draft/Publish Option */}
                <div className="flex items-center">
                  <input
                    id="isPublished"
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    checked={!isDraft}
                    onChange={(e) => setIsDraft(!e.target.checked)}
                  />
                  <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
                    Publish immediately (otherwise saved as draft)
                  </label>
                </div>
                
                {/* Submit Button */}
                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:bg-indigo-400"
                  >
                    {isSubmitting ? 'Posting...' : isDraft ? 'Save Draft' : 'Post'}
                  </button>
                </div>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
