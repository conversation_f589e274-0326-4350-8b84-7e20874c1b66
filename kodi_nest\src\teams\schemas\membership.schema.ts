import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type MembershipDocument = Membership & Document;

@Schema({
  collection: 'kd_memberships',
  timestamps: true,
})
export class Membership {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Team', required: true, index: true })
  teamId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true, index: true })
  memberId: MongooseSchema.Types.ObjectId;
}

export const MembershipSchema = SchemaFactory.createForClass(Membership);
