import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3011/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Add a request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('access_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // If error is 401 and we haven't retried yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        const response = await axios.post(`${API_URL}/auth/refresh`, {
          refresh_token: refreshToken,
        });

        const { access_token, refresh_token } = response.data;

        // Update tokens in localStorage
        localStorage.setItem('access_token', access_token);
        localStorage.setItem('refresh_token', refresh_token);

        // Update the Authorization header
        originalRequest.headers = {
          ...originalRequest.headers,
          Authorization: `Bearer ${access_token}`,
        };

        // Retry the original request
        return axios(originalRequest);
      } catch (refreshError) {
        // If refresh fails, clear tokens and redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');

        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }

        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export default api;

// API endpoints
export const endpoints = {
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    refresh: '/auth/refresh',
    me: '/auth/me',
  },
  users: {
    base: '/users',
    byId: (id: string) => `/users/${id}`,
  },
  profiles: {
    base: '/profiles',
    byId: (id: string) => `/profiles/${id}`,
    byUserId: (userId: string) => `/profiles/user/${userId}`,
    updateUserSettings: (userId: string) => `/profiles/user-settings/${userId}`,
  },
  companies: {
    base: '/companies',
    byId: (id: string) => `/companies/${id}`,
    byInviteCode: (code: string) => `/companies/invite/${code}`,
  },
  teams: {
    base: '/teams',
    byId: (id: string) => `/teams/${id}`,
    members: (teamId: string) => `/teams/${teamId}/members`,
    addMember: (teamId: string) => `/teams/${teamId}/members`,
    removeMember: (teamId: string, memberId: string) => `/teams/${teamId}/members/${memberId}`,
  },
  posts: {
    base: '/posts',
    byId: (id: string) => `/posts/${id}`,
    feed: '/posts/feed',
    drafts: '/posts/drafts',
    byTag: (tag: string) => `/posts/tag/${tag}`,
    byTeam: (teamId: string) => `/posts/team/${teamId}`,
    byType: (typeId: string) => `/posts/type/${typeId}`,
  },
  postTypes: {
    base: '/posts/types',
    byId: (id: string) => `/posts/types/${id}`,
    byCompany: (companyId: string) => `/posts/types/company/${companyId}`,
  },
  tags: {
    base: '/posts/tags',
    byId: (id: string) => `/posts/tags/${id}`,
    byCompany: (companyId: string) => `/posts/tags/company/${companyId}`,
  },
  comments: {
    base: '/comments',
    byId: (id: string) => `/comments/${id}`,
    byObject: (objectType: string, objectId: string) => `/comments/${objectType}/${objectId}`,
  },
  summaries: {
    base: '/summaries',
    byId: (id: string) => `/summaries/${id}`,
    generate: '/summaries/generate',
  },
  insights: {
    base: '/insights',
    byId: (id: string) => `/insights/${id}`,
    analyze: '/insights/analyze',
  },
  aiChat: {
    base: '/ai-chat',
    byId: (id: string) => `/ai-chat/${id}`,
    messages: (chatId: string) => `/ai-chat/${chatId}/messages`,
  },
  uploads: {
    base: '/uploads',
    avatar: '/uploads/avatar',
    logo: '/uploads/logo',
    audio: '/uploads/audio',
    audioUpload: '/uploads/audio-upload',
    document: '/uploads/document',
  },
  ai: {
    base: '/ai',
    transcribe: '/ai/transcribe',
  },
};
