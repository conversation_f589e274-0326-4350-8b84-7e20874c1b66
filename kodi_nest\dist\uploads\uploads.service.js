"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadsService = void 0;
const common_1 = require("@nestjs/common");
const aws_sdk_1 = require("aws-sdk");
const uuid_1 = require("uuid");
const fs = require("fs");
const path = require("path");
let UploadsService = class UploadsService {
    s3;
    constructor() {
        this.s3 = new aws_sdk_1.S3({
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            region: process.env.AWS_REGION || process.env.AWS_DEFAULT_REGION,
        });
        const uploadsDir = path.join(process.cwd(), 'uploads');
        if (!fs.existsSync(uploadsDir)) {
            fs.mkdirSync(uploadsDir);
        }
        const audioUploadsDir = path.join(uploadsDir, 'audio');
        if (!fs.existsSync(audioUploadsDir)) {
            fs.mkdirSync(audioUploadsDir);
        }
    }
    async generatePresignedUrl(fileType, isPrivate = false) {
        const bucket = isPrivate
            ? process.env.AWS_PRIVATE_BUCKET
            : process.env.AWS_BUCKET;
        const key = `${(0, uuid_1.v4)()}-${Date.now()}`;
        const params = {
            Bucket: bucket,
            Key: key,
            ContentType: fileType,
            Expires: 300,
        };
        const uploadUrl = await this.s3.getSignedUrlPromise('putObject', params);
        return {
            uploadUrl,
            key,
            url: `https://${bucket}.s3.amazonaws.com/${key}`,
        };
    }
    handleFileUpload(file, type) {
        const baseUrl = process.env.API_URL || 'http://localhost:3011';
        return {
            url: `${baseUrl}/uploads/${type}/${file.filename}`,
            filename: file.filename,
            originalname: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
        };
    }
};
exports.UploadsService = UploadsService;
exports.UploadsService = UploadsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], UploadsService);
//# sourceMappingURL=uploads.service.js.map