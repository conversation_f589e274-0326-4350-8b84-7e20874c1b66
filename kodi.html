<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ko<PERSON> - Voice-First Business Collaboration | Seakaytee</title>
    <meta name="description" content="<PERSON><PERSON> revolutionizes business communication with voice-first posting, structured content types, AI-driven analysis, and intelligent summarization for more efficient collaboration.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #fff;
            background: #0f172a;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(59,130,246,0.3);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #ffcc00;
            text-decoration: none;
        }
        
        nav ul {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        nav a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        nav a:hover {
            color: #ffcc00;
        }
        
        .hero {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #3b82f6 100%);
            color: white;
            padding: 6rem 0;
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)" /></svg>');
        }
        
        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
            background: linear-gradient(45deg, #3b82f6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.7;
        }
        
        .cta-group {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .cta-button {
            display: inline-block;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .cta-primary {
            background: linear-gradient(45deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 4px 15px rgba(59,130,246,0.4);
        }
        
        .cta-secondary {
            background: transparent;
            color: white;
            border: 2px solid #3b82f6;
        }
        
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(59,130,246,0.6);
        }
        
        .hero-visual {
            text-align: center;
            font-size: 10rem;
            opacity: 0.8;
            animation: pulse 4s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }
        
        .features {
            padding: 6rem 0;
            background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
        }
        
        .features h2 {
            text-align: center;
            font-size: 3rem;
            color: #3b82f6;
            margin-bottom: 4rem;
            font-weight: 700;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 3rem;
        }
        
        .feature-card {
            background: linear-gradient(145deg, #1e293b, #334155);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            border: 1px solid rgba(59,130,246,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(59,130,246,0.2);
        }
        
        .feature-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            display: block;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            color: #3b82f6;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .feature-card p {
            color: #cbd5e1;
            line-height: 1.6;
        }
        
        .voice-first {
            padding: 6rem 0;
            background: linear-gradient(45deg, #1e293b, #0f172a);
        }
        
        .voice-first h2 {
            text-align: center;
            font-size: 3rem;
            color: #3b82f6;
            margin-bottom: 4rem;
            font-weight: 700;
        }
        
        .voice-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }
        
        .voice-section {
            background: rgba(255,255,255,0.05);
            padding: 3rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59,130,246,0.2);
        }
        
        .voice-section h3 {
            font-size: 2rem;
            color: #3b82f6;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .voice-icon {
            font-size: 2.5rem;
        }
        
        .feature-list {
            list-style: none;
            margin-top: 1.5rem;
        }
        
        .feature-list li {
            padding: 0.8rem 0;
            display: flex;
            align-items: center;
            gap: 1rem;
            color: #cbd5e1;
            font-size: 1.1rem;
        }
        
        .check-icon {
            color: #3b82f6;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .ai-analysis {
            padding: 6rem 0;
            background: #0f172a;
        }
        
        .ai-analysis h2 {
            text-align: center;
            font-size: 3rem;
            color: #3b82f6;
            margin-bottom: 4rem;
            font-weight: 700;
        }
        
        .ai-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }
        
        .ai-card {
            background: linear-gradient(145deg, #1e293b, #334155);
            padding: 2.5rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            border: 1px solid rgba(59,130,246,0.1);
        }
        
        .ai-card:hover {
            transform: translateY(-5px);
            border-color: rgba(59,130,246,0.3);
        }
        
        .ai-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .ai-card h3 {
            color: #3b82f6;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .ai-card p {
            color: #94a3b8;
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        .efficiency {
            padding: 6rem 0;
            background: linear-gradient(135deg, #334155, #1e293b);
        }
        
        .efficiency h2 {
            text-align: center;
            font-size: 3rem;
            color: #3b82f6;
            margin-bottom: 2rem;
            font-weight: 700;
        }
        
        .efficiency-subtitle {
            text-align: center;
            font-size: 1.3rem;
            color: #cbd5e1;
            margin-bottom: 4rem;
            opacity: 0.9;
        }
        
        .efficiency-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
            margin-bottom: 4rem;
        }
        
        .stat-card {
            text-align: center;
            padding: 2rem;
            background: rgba(59,130,246,0.1);
            border-radius: 15px;
            border: 1px solid rgba(59,130,246,0.2);
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #cbd5e1;
            font-size: 1.1rem;
        }
        
        .cta-section {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 6rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .cta-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .cta-section h2 {
            font-size: 3rem;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }
        
        .cta-section p {
            font-size: 1.3rem;
            margin-bottom: 3rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        footer {
            background: #000;
            color: white;
            padding: 4rem 0;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 3rem;
        }
        
        .footer-section h3 {
            color: #3b82f6;
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
        }
        
        .footer-section ul {
            list-style: none;
        }
        
        .footer-section li {
            margin-bottom: 0.5rem;
        }
        
        .footer-section a {
            color: #cbd5e1;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-section a:hover {
            color: #3b82f6;
        }
        
        .footer-bottom {
            border-top: 1px solid #334155;
            margin-top: 3rem;
            padding-top: 2rem;
            text-align: center;
            color: #64748b;
        }
        
        @media (max-width: 768px) {
            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 2rem;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .voice-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .cta-group {
                justify-content: center;
            }
            
            nav ul {
                flex-direction: column;
                gap: 1rem;
            }
            
            .header-content {
                text-align: center;
            }
            
            .hero-visual {
                font-size: 6rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo">Seakaytee</a>
                <nav>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="townscape.html">Townscape</a></li>
                        <li><a href="knownow.html">KnowNow</a></li>
                        <li><a href="stage7.html">Stage7</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div>
                    <h1>Kodi</h1>
                    <p>Revolutionize business communication with voice-first posting, structured content types, and AI-driven analysis. Make collaboration more efficient by leveraging the speed of speech and the clarity of intelligent summarization.</p>
                    <div class="cta-group">
                        <a href="https://kodi.seakaytee.com" class="cta-button cta-primary" target="_blank">Start Collaborating</a>
                        <a href="#features" class="cta-button cta-secondary">Explore Features</a>
                    </div>
                </div>
                <div class="hero-visual">
                    🎙️
                </div>
            </div>
        </div>
    </section>

    <section class="features" id="features">
        <div class="container">
            <h2>Voice-First Communication</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎤</div>
                    <h3>Voice-First Posting</h3>
                    <p>Speak your thoughts naturally and let our platform convert them into structured, organized posts. Speaking is faster than typing, making communication more efficient.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📋</div>
                    <h3>Structured Post Types</h3>
                    <p>Organize information with predefined post structures including updates, discussions, decisions, and action items for clear communication.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🤖</div>
                    <h3>AI-Driven Analysis</h3>
                    <p>Intelligent summarization of posts and conversations provides quick insights and key takeaways without reading everything.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>Interactive Analytics</h3>
                    <p>User-driven analysis across the platform reveals communication patterns, trends, and actionable insights for better collaboration.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>Conversation Streams</h3>
                    <p>Follow real-time conversation flows with automatic threading and context preservation for seamless communication.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <h3>Smart Search</h3>
                    <p>Find information quickly across all posts, conversations, and summaries with intelligent search and filtering capabilities.</p>
                </div>
            </div>
        </div>
    </section>

    <section class="voice-first">
        <div class="container">
            <h2>The Power of Voice</h2>
            <div class="voice-content">
                <div class="voice-section">
                    <h3><span class="voice-icon">🗣️</span>Speak Naturally</h3>
                    <p>Express ideas in your natural speaking voice with advanced speech recognition that understands context and intent.</p>
                    <ul class="feature-list">
                        <li><span class="check-icon">✓</span> Real-time transcription</li>
                        <li><span class="check-icon">✓</span> Multi-language support</li>
                        <li><span class="check-icon">✓</span> Context-aware processing</li>
                        <li><span class="check-icon">✓</span> Noise cancellation</li>
                        <li><span class="check-icon">✓</span> Speaker identification</li>
                    </ul>
                </div>
                
                <div class="voice-section">
                    <h3><span class="voice-icon">📝</span>Structured Output</h3>
                    <p>Transform spoken content into organized, actionable posts with automatic formatting and categorization.</p>
                    <ul class="feature-list">
                        <li><span class="check-icon">✓</span> Auto-formatting</li>
                        <li><span class="check-icon">✓</span> Content categorization</li>
                        <li><span class="check-icon">✓</span> Action item extraction</li>
                        <li><span class="check-icon">✓</span> Priority detection</li>
                        <li><span class="check-icon">✓</span> Tag generation</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <section class="ai-analysis">
        <div class="container">
            <h2>AI-Powered Intelligence</h2>
            <div class="ai-grid">
                <div class="ai-card">
                    <div class="ai-icon">📄</div>
                    <h3>Post Summarization</h3>
                    <p>Automatically generate concise summaries of lengthy posts and discussions</p>
                </div>
                
                <div class="ai-card">
                    <div class="ai-icon">💬</div>
                    <h3>Conversation Analysis</h3>
                    <p>Extract key decisions, action items, and insights from conversation streams</p>
                </div>
                
                <div class="ai-card">
                    <div class="ai-icon">🎯</div>
                    <h3>Sentiment Detection</h3>
                    <p>Understand team mood and communication tone for better collaboration</p>
                </div>
                
                <div class="ai-card">
                    <div class="ai-icon">🔗</div>
                    <h3>Topic Clustering</h3>
                    <p>Automatically group related discussions and identify trending topics</p>
                </div>
                
                <div class="ai-card">
                    <div class="ai-icon">⏰</div>
                    <h3>Smart Prioritization</h3>
                    <p>AI identifies urgent items and important updates that need attention</p>
                </div>
                
                <div class="ai-card">
                    <div class="ai-icon">📈</div>
                    <h3>Engagement Metrics</h3>
                    <p>Track participation patterns and communication effectiveness</p>
                </div>
            </div>
        </div>
    </section>

    <section class="efficiency">
        <div class="container">
            <h2>Communication Efficiency</h2>
            <p class="efficiency-subtitle">Speaking is faster than typing. Reading is faster than listening. AI summarization makes it even better.</p>
            
            <div class="efficiency-stats">
                <div class="stat-card">
                    <div class="stat-number">3x</div>
                    <div class="stat-label">Faster Input</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">5x</div>
                    <div class="stat-label">Quicker Review</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">80%</div>
                    <div class="stat-label">Less Reading</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">90%</div>
                    <div class="stat-label">Better Retention</div>
                </div>
            </div>
        </div>
    </section>

    <section class="cta-section">
        <div class="container">
            <h2>Ready to Transform Your Team Communication?</h2>
            <p>Join forward-thinking businesses that have revolutionized their collaboration with voice-first communication</p>
            <a href="https://kodi.seakaytee.com" class="cta-button cta-primary" target="_blank">Get Started Today</a>
        </div>
    </section>

    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Kodi</h3>
                    <p>Voice-first business collaboration platform for efficient team communication.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Features</h3>
                    <ul>
                        <li><a href="#">Voice Posting</a></li>
                        <li><a href="#">AI Analysis</a></li>
                        <li><a href="#">Smart Summaries</a></li>
                        <li><a href="#">Structured Posts</a></li>
                        <li><a href="#">Team Analytics</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#">Getting Started</a></li>
                        <li><a href="#">Team Setup</a></li>
                        <li><a href="#">Best Practices</a></li>
                        <li><a href="#">FAQ</a></li>
                        <li><a href="#">Contact Support</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="index.html">Seakaytee</a></li>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 Seakaytee. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>