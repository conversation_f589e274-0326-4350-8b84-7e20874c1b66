"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AiController = void 0;
const common_1 = require("@nestjs/common");
const ai_service_1 = require("./ai.service");
const create_summary_dto_1 = require("./dto/create-summary.dto");
const create_insight_dto_1 = require("./dto/create-insight.dto");
const create_ai_prompt_dto_1 = require("./dto/create-ai-prompt.dto");
const create_ai_chat_dto_1 = require("./dto/create-ai-chat.dto");
const create_app_settings_dto_1 = require("./dto/create-app-settings.dto");
const transcribe_audio_dto_1 = require("./dto/transcribe-audio.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
let AiController = class AiController {
    aiService;
    constructor(aiService) {
        this.aiService = aiService;
    }
    createSummary(createSummaryDto) {
        return this.aiService.createSummary(createSummaryDto);
    }
    findAllSummaries(userId, companyId, tag) {
        return this.aiService.findAllSummaries(userId, companyId, tag);
    }
    findSummaryById(id) {
        return this.aiService.findSummaryById(id);
    }
    updateSummary(id, updateSummaryDto) {
        return this.aiService.updateSummary(id, updateSummaryDto);
    }
    removeSummary(id) {
        return this.aiService.removeSummary(id);
    }
    generateSummary(createSummaryDto) {
        return this.aiService.generateSummary(createSummaryDto);
    }
    createInsight(createInsightDto) {
        return this.aiService.createInsight(createInsightDto);
    }
    findAllInsights(userId, companyId, tag) {
        return this.aiService.findAllInsights(userId, companyId, tag);
    }
    findInsightById(id) {
        return this.aiService.findInsightById(id);
    }
    updateInsight(id, updateInsightDto) {
        return this.aiService.updateInsight(id, updateInsightDto);
    }
    removeInsight(id) {
        return this.aiService.removeInsight(id);
    }
    analyzeInsight(createInsightDto) {
        return this.aiService.analyzeInsight(createInsightDto);
    }
    createAiPrompt(createAiPromptDto) {
        return this.aiService.createAiPrompt(createAiPromptDto);
    }
    findAllAiPrompts(companyId, isSystem) {
        return this.aiService.findAllAiPrompts(companyId, isSystem);
    }
    findAiPromptById(id) {
        return this.aiService.findAiPromptById(id);
    }
    updateAiPrompt(id, updateAiPromptDto) {
        return this.aiService.updateAiPrompt(id, updateAiPromptDto);
    }
    removeAiPrompt(id) {
        return this.aiService.removeAiPrompt(id);
    }
    createAiChat(createAiChatDto) {
        return this.aiService.createAiChat(createAiChatDto);
    }
    findAllAiChats(userId) {
        return this.aiService.findAllAiChats(userId);
    }
    findAiChatById(id) {
        return this.aiService.findAiChatById(id);
    }
    updateAiChat(id, updateAiChatDto) {
        return this.aiService.updateAiChat(id, updateAiChatDto);
    }
    removeAiChat(id) {
        return this.aiService.removeAiChat(id);
    }
    sendChatMessage(id, message, userId) {
        return this.aiService.sendChatMessage(id, message, userId);
    }
    createAppSettings(createAppSettingsDto) {
        return this.aiService.createAppSettings(createAppSettingsDto);
    }
    findAllAppSettings(type, companyId) {
        return this.aiService.findAllAppSettings(type, companyId);
    }
    findAppSettingsById(id) {
        return this.aiService.findAppSettingsById(id);
    }
    updateAppSettings(id, updateAppSettingsDto) {
        return this.aiService.updateAppSettings(id, updateAppSettingsDto);
    }
    removeAppSettings(id) {
        return this.aiService.removeAppSettings(id);
    }
    transcribeAudio(transcribeAudioDto) {
        return this.aiService.transcribeAudio(transcribeAudioDto);
    }
};
exports.AiController = AiController;
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('summaries'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_summary_dto_1.CreateSummaryDto]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "createSummary", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('summaries'),
    __param(0, (0, common_1.Query)('userId')),
    __param(1, (0, common_1.Query)('companyId')),
    __param(2, (0, common_1.Query)('tag')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "findAllSummaries", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('summaries/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "findSummaryById", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Patch)('summaries/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "updateSummary", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Delete)('summaries/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "removeSummary", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('summaries/generate'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_summary_dto_1.CreateSummaryDto]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "generateSummary", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('insights'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_insight_dto_1.CreateInsightDto]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "createInsight", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('insights'),
    __param(0, (0, common_1.Query)('userId')),
    __param(1, (0, common_1.Query)('companyId')),
    __param(2, (0, common_1.Query)('tag')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "findAllInsights", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('insights/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "findInsightById", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Patch)('insights/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "updateInsight", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Delete)('insights/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "removeInsight", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('insights/analyze'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_insight_dto_1.CreateInsightDto]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "analyzeInsight", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('prompts'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_ai_prompt_dto_1.CreateAiPromptDto]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "createAiPrompt", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('prompts'),
    __param(0, (0, common_1.Query)('companyId')),
    __param(1, (0, common_1.Query)('isSystem')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "findAllAiPrompts", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('prompts/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "findAiPromptById", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Patch)('prompts/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "updateAiPrompt", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'coAdmin'),
    (0, common_1.Delete)('prompts/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "removeAiPrompt", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('chats'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_ai_chat_dto_1.CreateAiChatDto]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "createAiChat", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('chats'),
    __param(0, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "findAllAiChats", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('chats/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "findAiChatById", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Patch)('chats/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "updateAiChat", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Delete)('chats/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "removeAiChat", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('chats/:id/messages'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('message')),
    __param(2, (0, common_1.Body)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "sendChatMessage", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('settings'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_app_settings_dto_1.CreateAppSettingsDto]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "createAppSettings", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('settings'),
    __param(0, (0, common_1.Query)('type')),
    __param(1, (0, common_1.Query)('companyId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "findAllAppSettings", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('settings/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "findAppSettingsById", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Patch)('settings/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "updateAppSettings", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'coAdmin'),
    (0, common_1.Delete)('settings/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "removeAppSettings", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('transcribe'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [transcribe_audio_dto_1.TranscribeAudioDto]),
    __metadata("design:returntype", void 0)
], AiController.prototype, "transcribeAudio", null);
exports.AiController = AiController = __decorate([
    (0, common_1.Controller)('ai'),
    __metadata("design:paramtypes", [ai_service_1.AiService])
], AiController);
//# sourceMappingURL=ai.controller.js.map