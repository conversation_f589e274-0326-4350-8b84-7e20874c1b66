"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../users/users.service");
const profiles_service_1 = require("../profiles/profiles.service");
const companies_service_1 = require("../companies/companies.service");
const bcrypt = require("bcrypt");
let AuthService = class AuthService {
    usersService;
    profilesService;
    companiesService;
    jwtService;
    constructor(usersService, profilesService, companiesService, jwtService) {
        this.usersService = usersService;
        this.profilesService = profilesService;
        this.companiesService = companiesService;
        this.jwtService = jwtService;
    }
    async validateUser(email, password) {
        console.log('AuthService.ValidateUser:', email, password);
        const user = await this.usersService.findByEmail(email);
        if (user && await bcrypt.compare(password, user.password)) {
            const { password, ...result } = user.toObject();
            console.log('AuthService.ValidateUser:', result);
            return result;
        }
        console.log('AuthService.ValidateUser: null');
        return null;
    }
    async register(registerDto) {
        const { email, password, firstName, lastName, inviteCode } = registerDto;
        console.log('AuthService.Register:', registerDto);
        const existingUser = await this.usersService.findByEmail(email);
        if (existingUser) {
            throw new common_1.ConflictException('Email already exists');
        }
        const hashedPassword = await bcrypt.hash(password, 10);
        let companyId = null;
        if (inviteCode) {
            const company = await this.companiesService.findByInviteCode(inviteCode);
            if (!company) {
                throw new common_1.BadRequestException('Invalid invite code');
            }
            companyId = company._id.toString();
        }
        else {
            const companyName = firstName ? `${firstName}'s Company` : 'My Company';
            const company = await this.companiesService.create({
                name: companyName,
                createdBy: '',
            });
            companyId = company._id.toString();
        }
        const user = await this.usersService.create({
            email,
            password: hashedPassword,
            roles: ['user', 'admin'],
        });
        if (!inviteCode && companyId) {
            await this.companiesService.update(companyId, {
                createdBy: user._id.toString(),
            });
        }
        const profile = await this.profilesService.create({
            userId: user._id.toString(),
            firstName: firstName || '',
            lastName: lastName || '',
            companyId,
            userSettings: {
                showOnboarder: true,
                onboarderStep: 1,
            },
        });
        await this.usersService.update(user._id.toString(), { profile: profile._id });
        return this.generateTokens(user);
    }
    async login(user) {
        console.log('AuthService.Login:', user);
        await this.usersService.update(user._id.toString(), { lastLogin: new Date() });
        return this.generateTokens(user);
    }
    async refreshToken(refreshToken) {
        try {
            const payload = this.jwtService.verify(refreshToken, {
                secret: process.env.JWT_REFRESH_SECRET,
            });
            const user = await this.usersService.findById(payload.sub);
            if (!user) {
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            return this.generateTokens(user);
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
    generateTokens(user) {
        const payload = { email: user.email, sub: user._id, roles: user.roles };
        return {
            access_token: this.jwtService.sign(payload, {
                secret: process.env.JWT_SECRET,
                expiresIn: '1h',
            }),
            refresh_token: this.jwtService.sign(payload, {
                secret: process.env.JWT_REFRESH_SECRET,
                expiresIn: '7d',
            }),
            user: {
                id: user._id,
                email: user.email,
                profile: user.profile,
                roles: user.roles,
            },
        };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        profiles_service_1.ProfilesService,
        companies_service_1.CompaniesService,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map