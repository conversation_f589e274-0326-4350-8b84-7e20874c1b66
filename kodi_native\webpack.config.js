const createExpoWebpackConfigAsync = require('@expo/webpack-config');
const path = require('path');
const webpack = require('webpack');
const fs = require('fs');

module.exports = async function (env, argv) {
  // Make sure EXPO_TARGET is set to 'web' if not already set
  if (!process.env.EXPO_TARGET) {
    process.env.EXPO_TARGET = 'web';
  }

  const config = await createExpoWebpackConfigAsync(env, argv);

  // Add resolve aliases for web-specific implementations
  config.resolve.alias = {
    ...config.resolve.alias,
    'expo-secure-store': path.resolve(__dirname, 'src/expo-secure-store.web.ts'),
    // Add alias for nanoid/non-secure to use our shim
    'nanoid/non-secure': path.resolve(__dirname, 'src/utils/nanoid-non-secure-shim.js'),
  };

  // Add .cjs to extensions
  config.resolve.extensions = [
    '.web.tsx', '.web.ts', '.web.jsx', '.web.js',
    '.tsx', '.ts', '.jsx', '.js', '.cjs',
  ];

  // Add environment variables to the webpack build
  config.plugins.push(
    new webpack.DefinePlugin({
      'process.env.EXPO_TARGET': JSON.stringify('web'),
    })
  );

  return config;
};
