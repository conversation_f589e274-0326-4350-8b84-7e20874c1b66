'use client';

import React, { useState } from 'react';
import AudioInput<PERSON>ield from '@/components/ui/AudioInputField';

interface FilterInputFieldProps {
  label: string;
  id: string;
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
  className?: string;
}

export default function FilterInputField({
  label,
  id,
  value,
  onChange,
  options,
  className = '',
}: FilterInputFieldProps) {
  const [inputValue, setInputValue] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);

  // Filter options based on input value
  const filteredOptions = inputValue
    ? options.filter(option => 
        option.label.toLowerCase().includes(inputValue.toLowerCase())
      )
    : options;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    setShowDropdown(true);
  };

  const handleOptionSelect = (optionValue: string) => {
    onChange(optionValue);
    
    // Find the selected option to display its label in the input
    const selectedOption = options.find(option => option.value === optionValue);
    setInputValue(selectedOption ? selectedOption.label : '');
    
    setShowDropdown(false);
  };

  const handleAudioProcessed = (text: string) => {
    setInputValue(text);
    
    // Try to find a matching option
    const matchingOption = options.find(option => 
      option.label.toLowerCase().includes(text.toLowerCase())
    );
    
    if (matchingOption) {
      onChange(matchingOption.value);
    } else {
      // If no exact match, just update the input value
      setInputValue(text);
      setShowDropdown(true);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <AudioInputField
        label={label}
        id={id}
        value={inputValue}
        onChange={handleInputChange}
        onAudioProcessed={handleAudioProcessed}
        placeholder={`Search ${label.toLowerCase()}...`}
      />
      
      {showDropdown && filteredOptions.length > 0 && (
        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base overflow-auto focus:outline-none sm:text-sm">
          {filteredOptions.map((option) => (
            <div
              key={option.value}
              className={`cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-indigo-50 ${
                value === option.value ? 'bg-indigo-100' : ''
              }`}
              onClick={() => handleOptionSelect(option.value)}
            >
              <span className={`block truncate ${value === option.value ? 'font-medium' : 'font-normal'}`}>
                {option.label}
              </span>
              
              {value === option.value && (
                <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-indigo-600">
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
