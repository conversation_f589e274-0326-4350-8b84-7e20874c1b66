import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type TeamDocument = Team & Document;

@Schema({
  collection: 'kd_teams',
  timestamps: true,
})
export class Team {
  @Prop({ required: true })
  name: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company', required: true, index: true })
  companyId: MongooseSchema.Types.ObjectId;

  @Prop({ required: false })
  description: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ required: false })
  highlightColor: string;
}

export const TeamSchema = SchemaFactory.createForClass(Team);
