"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const post_schema_1 = require("./schemas/post.schema");
const post_type_schema_1 = require("./schemas/post-type.schema");
const tag_schema_1 = require("./schemas/tag.schema");
const comment_schema_1 = require("./schemas/comment.schema");
const core_1 = require("@nestjs/core");
let PostsService = class PostsService {
    postModel;
    postTypeModel;
    tagModel;
    commentModel;
    moduleRef;
    companiesService;
    constructor(postModel, postTypeModel, tagModel, commentModel, moduleRef) {
        this.postModel = postModel;
        this.postTypeModel = postTypeModel;
        this.tagModel = tagModel;
        this.commentModel = commentModel;
        this.moduleRef = moduleRef;
    }
    onModuleInit() {
        try {
            const { CompaniesService } = require('../companies/companies.service');
            this.companiesService = this.moduleRef.get(CompaniesService, { strict: false });
        }
        catch (error) {
            console.warn('Could not load CompaniesService:', error.message);
        }
    }
    async createPost(createPostDto) {
        const hashtags = this.extractHashtags(createPostDto.body);
        createPostDto.hashtags = hashtags;
        const newPost = new this.postModel(createPostDto);
        const post = await newPost.save();
        await this.updateTags(hashtags, createPostDto.companyId);
        return post;
    }
    async findAllPosts(companyId, teamId, postTypeId, tag, isDraft) {
        const query = {};
        if (companyId) {
            query.companyId = companyId;
        }
        if (teamId) {
            query.teamId = teamId;
        }
        if (postTypeId) {
            query.postTypeId = postTypeId;
        }
        if (tag) {
            query.hashtags = tag;
        }
        if (isDraft !== undefined) {
            query.isDraft = isDraft;
        }
        return this.postModel
            .find(query)
            .sort({ createdAt: -1 })
            .exec();
    }
    async findPostById(id) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid post ID format: ${id}`);
        }
        const post = await this.postModel.findById(id).exec();
        if (!post) {
            throw new common_1.NotFoundException(`Post with ID ${id} not found`);
        }
        return post;
    }
    async updatePost(id, updatePostDto) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid post ID format: ${id}`);
        }
        if (updatePostDto.body) {
            const hashtags = this.extractHashtags(updatePostDto.body);
            updatePostDto.hashtags = hashtags;
            const post = await this.postModel.findById(id).exec();
            if (post && post.companyId) {
                await this.updateTags(hashtags, post.companyId.toString());
            }
        }
        const updatedPost = await this.postModel
            .findByIdAndUpdate(id, updatePostDto, { new: true })
            .exec();
        if (!updatedPost) {
            throw new common_1.NotFoundException(`Post with ID ${id} not found`);
        }
        return updatedPost;
    }
    async removePost(id) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid post ID format: ${id}`);
        }
        await this.commentModel.deleteMany({
            linkedObjectId: id,
            objectType: 'Post',
        }).exec();
        const deletedPost = await this.postModel.findByIdAndDelete(id).exec();
        if (!deletedPost) {
            throw new common_1.NotFoundException(`Post with ID ${id} not found`);
        }
        return deletedPost;
    }
    async createPostType(createPostTypeDto) {
        const newPostType = new this.postTypeModel(createPostTypeDto);
        return newPostType.save();
    }
    async findAllPostTypes(companyId) {
        const query = companyId ? { companyId, archived: false } : { archived: false };
        return this.postTypeModel.find(query).exec();
    }
    async findPostTypeById(id) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid post type ID format: ${id}`);
        }
        const postType = await this.postTypeModel.findById(id).exec();
        if (!postType) {
            throw new common_1.NotFoundException(`Post type with ID ${id} not found`);
        }
        return postType;
    }
    async updatePostType(id, updatePostTypeDto) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid post type ID format: ${id}`);
        }
        const updatedPostType = await this.postTypeModel
            .findByIdAndUpdate(id, updatePostTypeDto, { new: true })
            .exec();
        if (!updatedPostType) {
            throw new common_1.NotFoundException(`Post type with ID ${id} not found`);
        }
        return updatedPostType;
    }
    async removePostType(id) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid post type ID format: ${id}`);
        }
        const archivedPostType = await this.postTypeModel
            .findByIdAndUpdate(id, { archived: true }, { new: true })
            .exec();
        if (!archivedPostType) {
            throw new common_1.NotFoundException(`Post type with ID ${id} not found`);
        }
        return archivedPostType;
    }
    async createTag(createTagDto) {
        const existingTag = await this.tagModel.findOne({
            name: createTagDto.name,
            companyId: createTagDto.companyId,
        }).exec();
        if (existingTag) {
            throw new common_1.BadRequestException(`Tag ${createTagDto.name} already exists for this company`);
        }
        const newTag = new this.tagModel(createTagDto);
        return newTag.save();
    }
    async findAllTags(companyId) {
        const query = companyId ? { companyId } : {};
        return this.tagModel.find(query).sort({ count: -1 }).exec();
    }
    async findTagById(id) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid tag ID format: ${id}`);
        }
        const tag = await this.tagModel.findById(id).exec();
        if (!tag) {
            throw new common_1.NotFoundException(`Tag with ID ${id} not found`);
        }
        return tag;
    }
    async updateTag(id, updateTagDto) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid tag ID format: ${id}`);
        }
        const updatedTag = await this.tagModel
            .findByIdAndUpdate(id, updateTagDto, { new: true })
            .exec();
        if (!updatedTag) {
            throw new common_1.NotFoundException(`Tag with ID ${id} not found`);
        }
        return updatedTag;
    }
    async removeTag(id) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid tag ID format: ${id}`);
        }
        const deletedTag = await this.tagModel.findByIdAndDelete(id).exec();
        if (!deletedTag) {
            throw new common_1.NotFoundException(`Tag with ID ${id} not found`);
        }
        return deletedTag;
    }
    async createComment(createCommentDto) {
        const newComment = new this.commentModel(createCommentDto);
        const comment = await newComment.save();
        if (createCommentDto.objectType === 'Post') {
            await this.postModel.findByIdAndUpdate(createCommentDto.linkedObjectId, { $push: { comments: comment._id } }).exec();
        }
        return comment;
    }
    async findAllComments(objectType, objectId) {
        return this.commentModel
            .find({ objectType, linkedObjectId: objectId })
            .sort({ createdAt: 1 })
            .exec();
    }
    async findCommentById(id) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid comment ID format: ${id}`);
        }
        const comment = await this.commentModel.findById(id).exec();
        if (!comment) {
            throw new common_1.NotFoundException(`Comment with ID ${id} not found`);
        }
        return comment;
    }
    async updateComment(id, updateCommentDto) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid comment ID format: ${id}`);
        }
        const updatedComment = await this.commentModel
            .findByIdAndUpdate(id, updateCommentDto, { new: true })
            .exec();
        if (!updatedComment) {
            throw new common_1.NotFoundException(`Comment with ID ${id} not found`);
        }
        return updatedComment;
    }
    async removeComment(id) {
        if (!/^[0-9a-fA-F]{24}$/.test(id)) {
            throw new common_1.BadRequestException(`Invalid comment ID format: ${id}`);
        }
        const comment = await this.commentModel.findById(id).exec();
        if (!comment) {
            throw new common_1.NotFoundException(`Comment with ID ${id} not found`);
        }
        if (comment.objectType === 'Post') {
            await this.postModel.findByIdAndUpdate(comment.linkedObjectId, { $pull: { comments: comment._id } }).exec();
        }
        const deletedComment = await this.commentModel.findByIdAndDelete(id).exec();
        if (!deletedComment) {
            throw new common_1.NotFoundException(`Comment with ID ${id} not found`);
        }
        return deletedComment;
    }
    extractHashtags(text) {
        const hashtagRegex = /#(\w+)/g;
        const matches = text.match(hashtagRegex);
        if (!matches) {
            return [];
        }
        return [...new Set(matches.map(tag => tag.substring(1).toLowerCase()))];
    }
    async updateTags(hashtags, companyId) {
        for (const tag of hashtags) {
            const existingTag = await this.tagModel.findOne({
                name: tag,
                companyId: companyId || null,
            }).exec();
            if (existingTag) {
                await this.tagModel.findByIdAndUpdate(existingTag._id, { $inc: { count: 1 } }).exec();
            }
            else {
                const createTagDto = {
                    name: tag,
                    count: 1,
                };
                if (companyId) {
                    createTagDto.companyId = companyId;
                }
                const newTag = new this.tagModel(createTagDto);
                await newTag.save();
            }
        }
    }
};
exports.PostsService = PostsService;
exports.PostsService = PostsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(post_schema_1.Post.name)),
    __param(1, (0, mongoose_1.InjectModel)(post_type_schema_1.PostType.name)),
    __param(2, (0, mongoose_1.InjectModel)(tag_schema_1.Tag.name)),
    __param(3, (0, mongoose_1.InjectModel)(comment_schema_1.Comment.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        core_1.ModuleRef])
], PostsService);
//# sourceMappingURL=posts.service.js.map