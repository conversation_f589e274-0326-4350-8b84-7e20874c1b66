import { Test, TestingModule } from '@nestjs/testing';
import { CompaniesService } from './companies.service';
import { getModelToken } from '@nestjs/mongoose';
import { Company } from './schemas/company.schema';
import { ModuleRef } from '@nestjs/core';
import { NotFoundException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('CompaniesService', () => {
  let service: CompaniesService;
  let companyModel: Model<Company>;
  let moduleRef: ModuleRef;

  const mockCompanyModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockModuleRef = {
    get: jest.fn(),
  };

  const mockPostsService = {
    createPostType: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CompaniesService,
        {
          provide: getModelToken(Company.name),
          useValue: mockCompanyModel,
        },
        {
          provide: ModuleRef,
          useValue: mockModuleRef,
        },
      ],
    }).compile();

    service = module.get<CompaniesService>(CompaniesService);
    companyModel = module.get<Model<Company>>(getModelToken(Company.name));
    moduleRef = module.get<ModuleRef>(ModuleRef);

    // Mock the moduleRef.get method to return our mock PostsService
    moduleRef.get.mockReturnValue(mockPostsService);

    // Mock the crypto.randomBytes method
    jest.spyOn(service as any, 'generateInviteCode').mockReturnValue('abc12345');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a company with a generated invite code', async () => {
      const createCompanyDto = {
        name: 'Test Company',
        createdBy: 'user1',
      };

      const savedCompany = {
        _id: 'company1',
        ...createCompanyDto,
        invite_code: 'abc12345',
      };

      // Mock the model constructor and save
      mockCompanyModel.new = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedCompany),
      }));

      // Mock the createDefaultPostTypes method
      jest.spyOn(service, 'createDefaultPostTypes').mockResolvedValue(undefined);

      const result = await service.create(createCompanyDto);

      expect(service['generateInviteCode']).toHaveBeenCalled();
      expect(service.createDefaultPostTypes).toHaveBeenCalledWith('company1', 'user1');
      expect(result).toEqual(savedCompany);
    });

    it('should use provided invite code if available', async () => {
      const createCompanyDto = {
        name: 'Test Company',
        createdBy: 'user1',
        invite_code: 'custom123',
      };

      const savedCompany = {
        _id: 'company1',
        ...createCompanyDto,
      };

      // Mock the model constructor and save
      mockCompanyModel.new = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedCompany),
      }));

      // Mock the createDefaultPostTypes method
      jest.spyOn(service, 'createDefaultPostTypes').mockResolvedValue(undefined);

      const result = await service.create(createCompanyDto);

      expect(service['generateInviteCode']).not.toHaveBeenCalled();
      expect(service.createDefaultPostTypes).toHaveBeenCalledWith('company1', 'user1');
      expect(result).toEqual(savedCompany);
    });
  });

  describe('findAll', () => {
    it('should return all companies', async () => {
      const mockCompanies = [
        { _id: 'company1', name: 'Company 1' },
        { _id: 'company2', name: 'Company 2' },
      ];

      mockCompanyModel.find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCompanies),
      });

      const result = await service.findAll();

      expect(mockCompanyModel.find).toHaveBeenCalled();
      expect(result).toEqual(mockCompanies);
    });
  });

  describe('findById', () => {
    it('should return a company when it exists', async () => {
      const mockCompany = { _id: 'company1', name: 'Test Company' };

      mockCompanyModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCompany),
      });

      const result = await service.findById('company1');

      expect(mockCompanyModel.findById).toHaveBeenCalledWith('company1');
      expect(result).toEqual(mockCompany);
    });

    it('should throw NotFoundException when company does not exist', async () => {
      mockCompanyModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.findById('nonexistent')).rejects.toThrow(NotFoundException);
      expect(mockCompanyModel.findById).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('findByInviteCode', () => {
    it('should return a company when invite code exists', async () => {
      const mockCompany = { _id: 'company1', name: 'Test Company', invite_code: 'abc123' };

      mockCompanyModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCompany),
      });

      const result = await service.findByInviteCode('abc123');

      expect(mockCompanyModel.findOne).toHaveBeenCalledWith({ invite_code: 'abc123' });
      expect(result).toEqual(mockCompany);
    });

    it('should return null when invite code does not exist', async () => {
      mockCompanyModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      const result = await service.findByInviteCode('nonexistent');

      expect(mockCompanyModel.findOne).toHaveBeenCalledWith({ invite_code: 'nonexistent' });
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update a company when it exists', async () => {
      const updateCompanyDto = {
        name: 'Updated Company',
      };

      const updatedCompany = {
        _id: 'company1',
        name: 'Updated Company',
        invite_code: 'abc123',
      };

      mockCompanyModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedCompany),
      });

      const result = await service.update('company1', updateCompanyDto);

      expect(mockCompanyModel.findByIdAndUpdate).toHaveBeenCalledWith('company1', updateCompanyDto, { new: true });
      expect(result).toEqual(updatedCompany);
    });

    it('should throw NotFoundException when company does not exist', async () => {
      mockCompanyModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.update('nonexistent', { name: 'Updated' })).rejects.toThrow(NotFoundException);
      expect(mockCompanyModel.findByIdAndUpdate).toHaveBeenCalledWith('nonexistent', { name: 'Updated' }, { new: true });
    });
  });

  describe('remove', () => {
    it('should remove a company when it exists', async () => {
      const deletedCompany = { _id: 'company1', name: 'Test Company' };

      mockCompanyModel.findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(deletedCompany),
      });

      const result = await service.remove('company1');

      expect(mockCompanyModel.findByIdAndDelete).toHaveBeenCalledWith('company1');
      expect(result).toEqual(deletedCompany);
    });

    it('should throw NotFoundException when company does not exist', async () => {
      mockCompanyModel.findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.remove('nonexistent')).rejects.toThrow(NotFoundException);
      expect(mockCompanyModel.findByIdAndDelete).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('createDefaultPostTypes', () => {
    it('should create default post types for a company', async () => {
      await service.createDefaultPostTypes('company1', 'user1');

      // Should call createPostType multiple times (once for each default post type)
      expect(mockPostsService.createPostType).toHaveBeenCalledTimes(5);
      
      // Check that each call includes the company ID and user ID
      const calls = mockPostsService.createPostType.mock.calls;
      calls.forEach(call => {
        expect(call[0]).toHaveProperty('companyId', 'company1');
        expect(call[0]).toHaveProperty('userId', 'user1');
      });
    });
  });
});
