import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AiService } from './ai.service';
import { AiController } from './ai.controller';
import { Summary, SummarySchema } from './schemas/summary.schema';
import { Insight, InsightSchema } from './schemas/insight.schema';
import { AiPrompt, AiPromptSchema } from './schemas/ai-prompt.schema';
import { AiChat, AiChatSchema } from './schemas/ai-chat.schema';
import { AppSettings, AppSettingsSchema } from './schemas/app-settings.schema';
import { PostsModule } from '../posts/posts.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Summary.name, schema: SummarySchema },
      { name: Insight.name, schema: InsightSchema },
      { name: AiPrompt.name, schema: AiPromptSchema },
      { name: AiChat.name, schema: AiChatSchema },
      { name: AppSettings.name, schema: AppSettingsSchema },
    ]),
    PostsModule,
  ],
  controllers: [AiController],
  providers: [AiService],
  exports: [AiService],
})
export class AiModule {}
