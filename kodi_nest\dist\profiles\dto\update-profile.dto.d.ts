import { CreateProfileDto } from './create-profile.dto';
declare const UpdateProfileDto_base: import("@nestjs/mapped-types").MappedType<Partial<CreateProfileDto>>;
export declare class UpdateProfileDto extends UpdateProfileDto_base {
    firstName?: string;
    lastName?: string;
    role?: string;
    companyId?: string;
    avatar?: string;
    transformedAvatar?: {
        small: string;
        medium: string;
        large: string;
    };
    hashtags?: string[];
    userSettings?: {
        showOnboarder: boolean;
        onboarderStep: number;
        [key: string]: any;
    };
}
export {};
