import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import Card from '@/src/components/ui/Card';
import { router } from 'expo-router';

interface SamplePostProps {
  companyName: string;
}

export default function SamplePost({ companyName }: SamplePostProps) {
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;

  return (
    <Card style={[styles.card, { borderColor: colors.primary, borderLeftWidth: 4 }]}>
      <View style={styles.postHeader}>
        <View style={styles.postUser}>
          <View style={[styles.avatar, { backgroundColor: colors.primary + '20' }]}>
            <Text style={[styles.avatarText, { color: colors.primary }]}>
              KD
            </Text>
          </View>
          <View>
            <Text style={[styles.userName, { color: colors.foreground }]}>
              Ko<PERSON> Assistant
            </Text>
            <Text style={[styles.postMeta, { color: colors.mutedForeground }]}>
              {new Date().toLocaleDateString()}
              {' • Type: Welcome'}
            </Text>
          </View>
        </View>
      </View>

      <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
        Welcome to Kodi!
      </Text>

      <Text style={[styles.description, { color: colors.foreground }]}>
        This is a sample post to help you get started with Kodi. Here's what you can do:
      </Text>

      <View style={styles.listContainer}>
        <View style={styles.bulletPoint}>
          <Text style={[styles.bulletText, { color: colors.foreground }]}>•</Text>
        </View>
        <Text style={[styles.listText, { color: colors.foreground }]}>
          Create posts by clicking the POST button in the navigation bar
        </Text>
      </View>

      <View style={styles.listContainer}>
        <View style={styles.bulletPoint}>
          <Text style={[styles.bulletText, { color: colors.foreground }]}>•</Text>
        </View>
        <Text style={[styles.listText, { color: colors.foreground }]}>
          Record audio and have it automatically transcribed
        </Text>
      </View>

      <View style={styles.listContainer}>
        <View style={styles.bulletPoint}>
          <Text style={[styles.bulletText, { color: colors.foreground }]}>•</Text>
        </View>
        <Text style={[styles.listText, { color: colors.foreground }]}>
          Use hashtags to categorize your posts
        </Text>
      </View>

      <View style={styles.listContainer}>
        <View style={styles.bulletPoint}>
          <Text style={[styles.bulletText, { color: colors.foreground }]}>•</Text>
        </View>
        <Text style={[styles.listText, { color: colors.foreground }]}>
          Filter posts by team, type, or tag
        </Text>
      </View>

      <View style={styles.listContainer}>
        <View style={styles.bulletPoint}>
          <Text style={[styles.bulletText, { color: colors.foreground }]}>•</Text>
        </View>
        <Text style={[styles.listText, { color: colors.foreground }]}>
          Generate AI-powered analysis of your content
        </Text>
      </View>

      <Text style={[styles.sectionSubtitle, { color: colors.foreground, marginTop: 16 }]}>
        As an admin of {companyName}, you can also:
      </Text>

      <View style={styles.listContainer}>
        <View style={styles.bulletPoint}>
          <Text style={[styles.bulletText, { color: colors.foreground }]}>•</Text>
        </View>
        <Text style={[styles.listText, { color: colors.foreground }]}>
          Invite team members using your company's invite code
        </Text>
      </View>

      <View style={styles.listContainer}>
        <View style={styles.bulletPoint}>
          <Text style={[styles.bulletText, { color: colors.foreground }]}>•</Text>
        </View>
        <Text style={[styles.listText, { color: colors.foreground }]}>
          Create and manage teams
        </Text>
      </View>

      <View style={styles.listContainer}>
        <View style={styles.bulletPoint}>
          <Text style={[styles.bulletText, { color: colors.foreground }]}>•</Text>
        </View>
        <Text style={[styles.listText, { color: colors.foreground }]}>
          Define custom post types
        </Text>
      </View>

      <Text style={[styles.description, { color: colors.foreground, marginTop: 16 }]}>
        We hope you enjoy using Kodi for your team's voice-based communication!
      </Text>

      <View style={styles.tagsContainer}>
        <View style={[styles.tag, { backgroundColor: colors.primary + '20' }]}>
          <Text style={[styles.tagText, { color: colors.primary }]}>
            #welcome
          </Text>
        </View>
        <View style={[styles.tag, { backgroundColor: colors.primary + '20' }]}>
          <Text style={[styles.tagText, { color: colors.primary }]}>
            #gettingStarted
          </Text>
        </View>
        <View style={[styles.tag, { backgroundColor: colors.primary + '20' }]}>
          <Text style={[styles.tagText, { color: colors.primary }]}>
            #kodi
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.button, { backgroundColor: colors.primary }]}
        onPress={() => router.push('/(tabs)/post')}
        accessibilityRole="button"
        accessibilityLabel="Create your first post"
        accessibilityHint="Navigate to the post creation screen"
      >
        <Text style={[styles.buttonText, { color: colors.primaryForeground }]}>
          Create Your First Post
        </Text>
      </TouchableOpacity>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    padding: 16,
    borderLeftWidth: 4,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  postUser: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 14,
    fontWeight: '600',
  },
  userName: {
    fontWeight: '600',
    fontSize: 16,
  },
  postMeta: {
    fontSize: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    marginBottom: 16,
  },
  listContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bulletPoint: {
    width: 20,
    alignItems: 'center',
    marginRight: 8,
  },
  bulletText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  listText: {
    fontSize: 14,
    flex: 1,
    lineHeight: 20,
  },
  tipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  tipText: {
    fontSize: 14,
    flex: 1,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 16,
    marginBottom: 16,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  button: {
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
