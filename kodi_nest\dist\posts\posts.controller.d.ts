import { Request } from 'express';
import { PostsService } from './posts.service';
import { CreatePostDto } from './dto/create-post.dto';
import { UpdatePostDto } from './dto/update-post.dto';
import { CreatePostTypeDto } from './dto/create-post-type.dto';
import { CreateTagDto } from './dto/create-tag.dto';
import { CreateCommentDto } from './dto/create-comment.dto';
export declare class PostsController {
    private readonly postsService;
    constructor(postsService: PostsService);
    createPost(createPostDto: CreatePostDto): Promise<import("./schemas/post.schema").PostDocument>;
    findAllPosts(companyId?: string, teamId?: string, postTypeId?: string, tag?: string, isDraft?: boolean): Promise<import("./schemas/post.schema").PostDocument[]>;
    getFeed(req: Request, companyId?: string, teamId?: string, postTypeId?: string, tag?: string): Promise<import("./schemas/post.schema").PostDocument[]>;
    getDrafts(req: Request, userId?: string): Promise<import("./schemas/post.schema").PostDocument[]>;
    findPostById(id: string): Promise<import("./schemas/post.schema").PostDocument>;
    updatePost(id: string, updatePostDto: UpdatePostDto): Promise<import("./schemas/post.schema").PostDocument>;
    removePost(id: string): Promise<import("./schemas/post.schema").PostDocument>;
    createPostType(createPostTypeDto: CreatePostTypeDto): Promise<import("./schemas/post-type.schema").PostTypeDocument>;
    findAllPostTypes(req: Request, companyId?: string): Promise<import("./schemas/post-type.schema").PostTypeDocument[]>;
    findPostTypeById(id: string): Promise<import("./schemas/post-type.schema").PostTypeDocument>;
    updatePostType(id: string, updatePostTypeDto: any): Promise<import("./schemas/post-type.schema").PostTypeDocument>;
    removePostType(id: string): Promise<import("./schemas/post-type.schema").PostTypeDocument>;
    createTag(createTagDto: CreateTagDto): Promise<import("./schemas/tag.schema").TagDocument>;
    findAllTags(req: Request, companyId?: string): Promise<import("./schemas/tag.schema").TagDocument[]>;
    findTagById(id: string): Promise<import("./schemas/tag.schema").TagDocument>;
    updateTag(id: string, updateTagDto: any): Promise<import("./schemas/tag.schema").TagDocument>;
    removeTag(id: string): Promise<import("./schemas/tag.schema").TagDocument>;
    createComment(createCommentDto: CreateCommentDto): Promise<import("./schemas/comment.schema").CommentDocument>;
    findAllComments(objectType: string, objectId: string): Promise<import("./schemas/comment.schema").CommentDocument[]>;
    findCommentById(id: string): Promise<import("./schemas/comment.schema").CommentDocument>;
    updateComment(id: string, updateCommentDto: any): Promise<import("./schemas/comment.schema").CommentDocument>;
    removeComment(id: string): Promise<import("./schemas/comment.schema").CommentDocument | null>;
}
