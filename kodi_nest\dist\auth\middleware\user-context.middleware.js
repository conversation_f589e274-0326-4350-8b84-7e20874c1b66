"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserContextMiddleware = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../../users/users.service");
let UserContextMiddleware = class UserContextMiddleware {
    jwtService;
    usersService;
    constructor(jwtService, usersService) {
        this.jwtService = jwtService;
        this.usersService = usersService;
    }
    async use(req, res, next) {
        try {
            const authHeader = req.headers.authorization;
            if (authHeader && authHeader.startsWith('Bearer ')) {
                const token = authHeader.substring(7);
                const payload = this.jwtService.verify(token);
                if (payload && payload.sub) {
                    const user = await this.usersService.findById(payload.sub);
                    if (user && user.profile && user.profile.companyId) {
                        req['userContext'] = {
                            userId: payload.sub,
                            companyId: user.profile.companyId.toString(),
                            roles: payload.roles || [],
                        };
                    }
                }
            }
        }
        catch (error) {
            console.error('Error extracting user context:', error.message);
        }
        next();
    }
};
exports.UserContextMiddleware = UserContextMiddleware;
exports.UserContextMiddleware = UserContextMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        users_service_1.UsersService])
], UserContextMiddleware);
//# sourceMappingURL=user-context.middleware.js.map