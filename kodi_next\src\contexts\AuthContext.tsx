'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import api, { endpoints } from '@/lib/api';

interface User {
  id: string;
  email: string;
  profile: {
    _id: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    transformedAvatar?: {
      small: string;
      medium: string;
      large: string;
    };
    companyId?: string;
    userSettings?: {
      showOnboarder: boolean;
      onboarderStep: number;
      [key: string]: any;
    };
  };
  roles: string[];
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, firstName?: string, lastName?: string, inviteCode?: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  updateUserSettings: (settings: any) => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  login: async () => {},
  register: async () => {},
  logout: () => {},
  isAuthenticated: false,
  updateUserSettings: async () => {},
});

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in on initial load
    const checkAuth = async () => {
      const accessToken = localStorage.getItem('access_token');
      if (accessToken) {
        try {
          const { data } = await api.get(endpoints.auth.me);
          setUser(data);
        } catch (error) {
          console.error('Auth check failed:', error);
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('user');
        }
      }
      setLoading(false);
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const { data } = await api.post(endpoints.auth.login, { email, password });
      localStorage.setItem('access_token', data.access_token);
      localStorage.setItem('refresh_token', data.refresh_token);
      localStorage.setItem('user', JSON.stringify(data.user));
      setUser(data.user);
      router.push('/feed');
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const register = async (email: string, password: string, firstName?: string, lastName?: string, inviteCode?: string) => {
    try {
      const { data } = await api.post(endpoints.auth.register, {
        email,
        password,
        firstName,
        lastName,
        inviteCode
      });
      localStorage.setItem('access_token', data.access_token);
      localStorage.setItem('refresh_token', data.refresh_token);
      localStorage.setItem('user', JSON.stringify(data.user));
      setUser(data.user);
      router.push('/feed');
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    setUser(null);
    router.push('/login');
  };

  const updateUserSettings = async (settings: any) => {
    if (!user) return;

    try {
      const { data } = await api.patch(
        endpoints.profiles.updateUserSettings(user.id),
        settings
      );

      setUser(prev => {
        if (!prev) return null;
        return {
          ...prev,
          profile: {
            ...prev.profile,
            userSettings: {
              ...prev.profile.userSettings,
              ...settings
            }
          }
        };
      });

      return data;
    } catch (error) {
      console.error('Failed to update user settings:', error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      login,
      register,
      logout,
      updateUserSettings,
      isAuthenticated: !!user
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);