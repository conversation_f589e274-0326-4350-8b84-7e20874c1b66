import type { Meta, StoryObj } from '@storybook/react';
import Navbar from './Navbar';
import { AuthProvider } from '@/contexts/AuthContext';
import { ThemeProvider } from '@/contexts/ThemeContext';

// Mock the useAuth hook
jest.mock('@/contexts/AuthContext', () => ({
  ...jest.requireActual('@/contexts/AuthContext'),
  useAuth: () => ({
    user: {
      email: '<EMAIL>',
      profile: {
        firstName: 'Test',
        lastName: 'User',
        avatar: null,
      },
    },
    logout: () => console.log('Logout clicked'),
    isAuthenticated: true,
  }),
}));

// Mock the usePathname hook
jest.mock('next/navigation', () => ({
  usePathname: () => '/feed',
}));

const meta: Meta<typeof Navbar> = {
  title: 'Components/Layout/Navbar',
  component: Navbar,
  parameters: {
    layout: 'fullscreen',
  },
  decorators: [
    (Story) => (
      <ThemeProvider>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ThemeProvider>
    ),
  ],
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof Navbar>;

export const Default: Story = {};

export const LightMode: Story = {
  parameters: {
    backgrounds: { default: 'light' },
    theme: 'light',
  },
};

export const DarkMode: Story = {
  parameters: {
    backgrounds: { default: 'dark' },
    theme: 'dark',
  },
};
