import { Document, Schema as MongooseSchema } from 'mongoose';
export type PostTypeDocument = PostType & Document;
export declare class PostType {
    name: string;
    description: string;
    recordTips: string;
    companyId: MongooseSchema.Types.ObjectId;
    userId: MongooseSchema.Types.ObjectId;
    archived: boolean;
    highlightColor: string;
}
export declare const PostTypeSchema: MongooseSchema<PostType, import("mongoose").Model<PostType, any, any, any, Document<unknown, any, PostType> & PostType & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, PostType, Document<unknown, {}, import("mongoose").FlatRecord<PostType>> & import("mongoose").FlatRecord<PostType> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
