# Use Node.js 20 as the base image
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies and update packages to fix vulnerabilities
RUN apk update && \
    apk upgrade && \
    apk add --no-cache python3 make g++ curl

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Remove development dependencies
RUN npm prune --omit=dev

# Create production image
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Create uploads directory for temporary audio files
# Most files are stored in S3, but audio uploads use local storage temporarily
RUN mkdir -p uploads/audio

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package*.json ./

# Expose the port the app runs on
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Start the application
CMD ["node", "dist/main"]
