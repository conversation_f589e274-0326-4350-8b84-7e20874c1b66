// Apply SecureStore polyfill for web
import '@/src/utils/applySecureStorePolyfill';

import { DarkTheme, DefaultTheme, ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import { useColorScheme } from '@/hooks/useColorScheme';
import { AuthProvider } from '@/src/contexts/AuthContext';
import { ThemeProvider } from '@/src/contexts/ThemeContext';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider>
      <AuthProvider>
        <NavigationThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
          <Stack screenOptions={{ headerShown: false }}>
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen name="login" />
            <Stack.Screen name="register" />
            <Stack.Screen name="profile" />
            <Stack.Screen name="company" />
            <Stack.Screen name="teams" />
            <Stack.Screen name="ai-chat" />
            <Stack.Screen name="+not-found" />
            {/* These routes will be implemented later */}
            {/*
            <Stack.Screen name="team/[id]" />
            <Stack.Screen name="summaries" />
            <Stack.Screen name="insights" />
            <Stack.Screen name="notifications" />
            <Stack.Screen name="security" />
            <Stack.Screen name="help" />
            */}
          </Stack>
          <StatusBar style="auto" />
        </NavigationThemeProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
