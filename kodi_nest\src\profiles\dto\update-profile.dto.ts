import { IsOptional, IsString, IsObject, IsArray } from 'class-validator';
import { PartialType } from '@nestjs/mapped-types';
import { CreateProfileDto } from './create-profile.dto';

export class UpdateProfileDto extends PartialType(CreateProfileDto) {
  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsString()
  role?: string;

  @IsOptional()
  @IsString()
  companyId?: string;

  @IsOptional()
  @IsString()
  avatar?: string;

  @IsOptional()
  @IsObject()
  transformedAvatar?: {
    small: string;
    medium: string;
    large: string;
  };

  @IsOptional()
  @IsArray()
  hashtags?: string[];

  @IsOptional()
  @IsObject()
  userSettings?: {
    showOnboarder: boolean;
    onboarderStep: number;
    [key: string]: any;
  };
}
