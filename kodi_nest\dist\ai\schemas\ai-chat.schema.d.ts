import { Document, Schema as MongooseSchema } from 'mongoose';
export interface ChatMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
}
export type AiChatDocument = AiChat & Document;
export declare class AiChat {
    title: string;
    messages: ChatMessage[];
    userId: MongooseSchema.Types.ObjectId;
    companyId: MongooseSchema.Types.ObjectId;
}
export declare const AiChatSchema: MongooseSchema<AiChat, import("mongoose").Model<AiChat, any, any, any, Document<unknown, any, AiChat> & AiChat & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, AiChat, Document<unknown, {}, import("mongoose").FlatRecord<AiChat>> & import("mongoose").FlatRecord<AiChat> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
