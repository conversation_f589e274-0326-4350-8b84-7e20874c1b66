"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AiModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const ai_service_1 = require("./ai.service");
const ai_controller_1 = require("./ai.controller");
const summary_schema_1 = require("./schemas/summary.schema");
const insight_schema_1 = require("./schemas/insight.schema");
const ai_prompt_schema_1 = require("./schemas/ai-prompt.schema");
const ai_chat_schema_1 = require("./schemas/ai-chat.schema");
const app_settings_schema_1 = require("./schemas/app-settings.schema");
const posts_module_1 = require("../posts/posts.module");
let AiModule = class AiModule {
};
exports.AiModule = AiModule;
exports.AiModule = AiModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: summary_schema_1.Summary.name, schema: summary_schema_1.SummarySchema },
                { name: insight_schema_1.Insight.name, schema: insight_schema_1.InsightSchema },
                { name: ai_prompt_schema_1.AiPrompt.name, schema: ai_prompt_schema_1.AiPromptSchema },
                { name: ai_chat_schema_1.AiChat.name, schema: ai_chat_schema_1.AiChatSchema },
                { name: app_settings_schema_1.AppSettings.name, schema: app_settings_schema_1.AppSettingsSchema },
            ]),
            posts_module_1.PostsModule,
        ],
        controllers: [ai_controller_1.AiController],
        providers: [ai_service_1.AiService],
        exports: [ai_service_1.AiService],
    })
], AiModule);
//# sourceMappingURL=ai.module.js.map