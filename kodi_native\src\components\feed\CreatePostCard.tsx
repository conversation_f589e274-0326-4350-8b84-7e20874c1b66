import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Card from '@/src/components/ui/Card';
import { useRouter } from 'expo-router';

interface CreatePostCardProps {
  colors: any;
}

export default function CreatePostCard({ colors }: CreatePostCardProps) {
  const router = useRouter();

  const navigateToPostScreen = () => {
    router.push('/(tabs)/post');
  };

  return (
    <Card style={styles.container}>
      <Text style={[styles.title, { color: colors.foreground }]}>Create Post</Text>

      <TouchableOpacity
        style={[styles.inputButton, { borderColor: colors.border }]}
        onPress={navigateToPostScreen}
        accessibilityRole="button"
        accessibilityLabel="Create a new post"
        accessibilityHint="Navigate to the post creation screen"
      >
        <Ionicons
          name="create-outline"
          size={20}
          color={colors.mutedForeground}
          style={styles.inputIcon}
        />
        <Text style={[styles.inputText, { color: colors.mutedForeground }]}>
          What's on your mind?
        </Text>
      </TouchableOpacity>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.recordButton, { backgroundColor: colors.primary }]}
          onPress={navigateToPostScreen}
          accessibilityRole="button"
          accessibilityLabel="Record audio"
          accessibilityHint="Navigate to the post creation screen to record audio"
        >
          <Ionicons
            name="mic-outline"
            size={18}
            color={colors.primaryForeground}
            style={styles.recordIcon}
          />
          <Text style={[styles.recordText, { color: colors.primaryForeground }]}>
            Record
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 8,
    padding: 16,
  },
  header: {
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  inputButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 12,
  },
  inputIcon: {
    marginRight: 8,
  },
  inputText: {
    fontSize: 16,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  recordButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  recordIcon: {
    marginRight: 6,
  },
  recordText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
