import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import LoginPage from './page';
import { server } from '@/test-utils/mocks/server';
import { rest } from 'msw';

// Mock the auth context
const mockLogin = jest.fn();
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    login: mockLogin,
  }),
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

describe('LoginPage', () => {
  beforeAll(() => server.listen());
  afterEach(() => {
    server.resetHandlers();
    jest.clearAllMocks();
  });
  afterAll(() => server.close());

  it('renders the login form', () => {
    render(<LoginPage />);
    
    expect(screen.getByText(/Sign in to your account/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Sign in/i })).toBeInTheDocument();
    expect(screen.getByText(/create a new account/i)).toBeInTheDocument();
  });

  it('handles successful login', async () => {
    const user = userEvent.setup();
    
    mockLogin.mockResolvedValue(undefined);
    
    render(<LoginPage />);
    
    // Fill in the form
    await user.type(screen.getByLabelText(/Email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/Password/i), 'password123');
    
    // Submit the form
    await user.click(screen.getByRole('button', { name: /Sign in/i }));
    
    // Check that login was called with correct credentials
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
    });
    
    // No error message should be displayed
    expect(screen.queryByText(/Error/i)).not.toBeInTheDocument();
  });

  it('handles login failure', async () => {
    const user = userEvent.setup();
    
    // Mock login to throw an error
    mockLogin.mockRejectedValue({
      response: {
        data: {
          message: 'Invalid credentials',
        },
      },
    });
    
    render(<LoginPage />);
    
    // Fill in the form
    await user.type(screen.getByLabelText(/Email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/Password/i), 'wrongpassword');
    
    // Submit the form
    await user.click(screen.getByRole('button', { name: /Sign in/i }));
    
    // Check that login was called
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'wrongpassword');
    });
    
    // Error message should be displayed
    await waitFor(() => {
      expect(screen.getByText(/Invalid credentials/i)).toBeInTheDocument();
    });
  });

  it('validates form inputs', async () => {
    const user = userEvent.setup();
    
    render(<LoginPage />);
    
    // Submit without filling the form
    await user.click(screen.getByRole('button', { name: /Sign in/i }));
    
    // Check for validation errors
    await waitFor(() => {
      expect(screen.getByText(/Email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Password is required/i)).toBeInTheDocument();
    });
    
    // Login should not be called
    expect(mockLogin).not.toHaveBeenCalled();
    
    // Fill in email only
    await user.type(screen.getByLabelText(/Email/i), '<EMAIL>');
    await user.click(screen.getByRole('button', { name: /Sign in/i }));
    
    // Should still show password error
    await waitFor(() => {
      expect(screen.queryByText(/Email is required/i)).not.toBeInTheDocument();
      expect(screen.getByText(/Password is required/i)).toBeInTheDocument();
    });
    
    // Login should still not be called
    expect(mockLogin).not.toHaveBeenCalled();
  });

  it('shows loading state during login', async () => {
    const user = userEvent.setup();
    
    // Mock login with a delay to show loading state
    mockLogin.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(<LoginPage />);
    
    // Fill in the form
    await user.type(screen.getByLabelText(/Email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/Password/i), 'password123');
    
    // Submit the form
    await user.click(screen.getByRole('button', { name: /Sign in/i }));
    
    // Check for loading state
    expect(screen.getByRole('button', { name: /Signing in/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Signing in/i })).toBeDisabled();
    
    // Wait for login to complete
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
    });
  });
});
