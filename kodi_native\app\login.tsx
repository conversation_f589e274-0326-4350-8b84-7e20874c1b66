import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, KeyboardAvoidingView, Platform, ScrollView, Alert } from 'react-native';
import { Link } from 'expo-router';
import { useAuth } from '../src/contexts/AuthContext';
import { useTheme } from '../src/contexts/ThemeContext';
import { Colors } from '../src/constants/Theme';
import Input from '../src/components/ui/Input';
import Button from '../src/components/ui/Button';
import { Ionicons } from '@expo/vector-icons';
import ConnectionStatus, { ConnectionStatusRef } from '../src/components/ui/ConnectionStatus';
import { ConnectionStatus as ConnectionStatusType } from '../src/utils/connectionCheck';

export default function LoginScreen() {
  const { login } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  const connectionStatusRef = useRef<ConnectionStatusRef>(null);

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatusType>({
    server: 'unknown',
    message: 'Checking connection...',
    timestamp: Date.now(),
  });

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    // Check if we have a connection to the server
    if (connectionStatus.server === 'error') {
      setError(`Cannot login: ${connectionStatus.message}. Please check your connection and try again.`);

      // Retry connection test using the reference
      if (connectionStatusRef.current) {
        connectionStatusRef.current.testServerConnection();
      }
      return;
    }

    try {
      setLoading(true);
      setError('');
      console.log('Attempting login with email:', email);

      await login({ email, password });
      console.log('Login successful, should be redirecting...');
    } catch (err: any) {
      console.error('Login error details:', err);

      let errorMessage = 'Failed to login. Please check your credentials.';

      if (err.response) {
        // The server responded with an error
        errorMessage = err.response.data?.message || `Server error: ${err.response.status}`;
        console.error('Server response error:', err.response.data);
      } else if (err.request) {
        // The request was made but no response was received
        errorMessage = 'No response from server. Please check your connection.';
        console.error('No response from server:', err.request);
      } else {
        // Something happened in setting up the request
        errorMessage = `Request error: ${err.message}`;
      }

      setError(errorMessage);

      // Show detailed error in development
      if (__DEV__) {
        Alert.alert(
          'Login Error',
          `Error: ${errorMessage}\n\nDetails: ${JSON.stringify(err.response?.data || err.message)}`,
          [{ text: 'OK' }]
        );
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.logoContainer}>
          <Image
            source={require('../assets/images/icon.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={[styles.title, { color: colors.foreground }]}>
            Kodi
          </Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={[styles.subtitle, { color: colors.foreground }]}>
            Log in to your account
          </Text>

          {/* Connection Status Indicator */}
          <ConnectionStatus
            ref={connectionStatusRef}
            onStatusChange={setConnectionStatus}
          />

          {error ? (
            <View style={[styles.errorContainer, { backgroundColor: colors.destructive + '20' }]}>
              <Text style={[styles.errorText, { color: colors.destructive }]}>
                {error}
              </Text>
            </View>
          ) : null}

          <Input
            label="Email"
            placeholder="Enter your email"
            value={email}
            onChangeText={setEmail}
            autoCapitalize="none"
            keyboardType="email-address"
            leftIcon={<Ionicons name="mail-outline" size={20} color={colors.mutedForeground} />}
          />

          <Input
            label="Password"
            placeholder="Enter your password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry={!showPassword}
            leftIcon={<Ionicons name="lock-closed-outline" size={20} color={colors.mutedForeground} />}
            rightIcon={
              <Ionicons
                name={showPassword ? "eye-off-outline" : "eye-outline"}
                size={20}
                color={colors.mutedForeground}
              />
            }
            onRightIconPress={() => setShowPassword(!showPassword)}
          />

          <Button
            title="Log In"
            onPress={handleLogin}
            isLoading={loading}
            fullWidth
            style={styles.loginButton}
          />

          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: colors.mutedForeground }]}>
              Don't have an account?{' '}
            </Text>
            <Link href="/register" asChild>
              <TouchableOpacity>
                <Text style={[styles.link, { color: colors.primary }]}>
                  Sign up
                </Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
  },
  subtitle: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 16,
  },

  // Error styles
  errorContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
  },
  loginButton: {
    marginTop: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 24,
  },
  footerText: {
    fontSize: 14,
  },
  link: {
    fontSize: 14,
    fontWeight: '600',
  },
});
