import { IsNotEmpty, IsString, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON>y, IsDate } from 'class-validator';

export class CreateSummaryDto {
  @IsOptional()
  @IsString()
  tag?: string;

  @IsOptional()
  @IsString()
  teamId?: string;

  @IsOptional()
  @IsString()
  postTypeId?: string;

  @IsOptional()
  @IsString()
  timeRange?: string;

  @IsOptional()
  @IsString()
  userPrompt?: string;

  @IsOptional()
  @IsString()
  promptId?: string;

  @IsOptional()
  @IsString()
  summary?: string;

  @IsOptional()
  @IsArray()
  postIds?: string[];

  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsOptional()
  @IsString()
  companyId?: string;

  @IsOptional()
  @IsDate()
  lastUpdated?: Date;
}
