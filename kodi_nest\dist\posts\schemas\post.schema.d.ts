import { Document, Schema as MongooseSchema } from 'mongoose';
export type PostDocument = Post & Document;
export declare class Post {
    body: string;
    enhancedBody: string;
    companyId: MongooseSchema.Types.ObjectId;
    teamId: MongooseSchema.Types.ObjectId;
    postTypeId: MongooseSchema.Types.ObjectId;
    hashtags: string[];
    userId: MongooseSchema.Types.ObjectId;
    isDraft: boolean;
    comments: MongooseSchema.Types.ObjectId[];
    audioUrl: string;
}
export declare const PostSchema: MongooseSchema<Post, import("mongoose").Model<Post, any, any, any, Document<unknown, any, Post> & Post & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Post, Document<unknown, {}, import("mongoose").FlatRecord<Post>> & import("mongoose").FlatRecord<Post> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
