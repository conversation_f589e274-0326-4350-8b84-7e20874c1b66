"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanySchema = exports.Company = exports.CompanyAccountType = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
var CompanyAccountType;
(function (CompanyAccountType) {
    CompanyAccountType[CompanyAccountType["FREE"] = 0] = "FREE";
    CompanyAccountType[CompanyAccountType["PAID"] = 1] = "PAID";
    CompanyAccountType[CompanyAccountType["COMP"] = 2] = "COMP";
    CompanyAccountType[CompanyAccountType["PREMIUM"] = 3] = "PREMIUM";
})(CompanyAccountType || (exports.CompanyAccountType = CompanyAccountType = {}));
let Company = class Company {
    name;
    invite_code;
    createdBy;
    accountType;
    logo;
    transformedLogo;
    hashtags;
    settings;
};
exports.Company = Company;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Company.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, unique: true, index: true }),
    __metadata("design:type", String)
], Company.prototype, "invite_code", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'User', required: true }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Company.prototype, "createdBy", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: CompanyAccountType.FREE }),
    __metadata("design:type", Number)
], Company.prototype, "accountType", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], Company.prototype, "logo", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, required: false }),
    __metadata("design:type", Object)
], Company.prototype, "transformedLogo", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], Company.prototype, "hashtags", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, default: {} }),
    __metadata("design:type", Object)
], Company.prototype, "settings", void 0);
exports.Company = Company = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'kd_companies',
        timestamps: true,
    })
], Company);
exports.CompanySchema = mongoose_1.SchemaFactory.createForClass(Company);
//# sourceMappingURL=company.schema.js.map