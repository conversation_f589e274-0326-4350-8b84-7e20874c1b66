/**
 * Test script for nanoid/non-secure
 */

// Import nanoid from our shim
const { nanoid, customAlphabet } = require('./nanoid-non-secure-shim');

// Test nanoid
console.log('Generated ID:', nanoid());
console.log('Generated ID with custom size:', nanoid(10));

// Test customAlphabet
const customNanoid = customAlphabet('1234567890', 6);
console.log('Generated custom ID:', customNanoid());

// Export for use in other modules
module.exports = { nanoid, customAlphabet };
