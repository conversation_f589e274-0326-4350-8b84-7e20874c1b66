import api, { endpoints } from './api';
import axios from 'axios';

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    interceptors: {
      request: { use: jest.fn(), eject: jest.fn() },
      response: { use: jest.fn(), eject: jest.fn() },
    },
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  })),
  isAxiosError: jest.fn(),
}));

describe('API Client', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => 'mock-token'),
        setItem: jest.fn(),
        removeItem: jest.fn(),
      },
      writable: true,
    });
  });

  it('should have correct endpoints defined', () => {
    // Test that all expected endpoints are defined
    expect(endpoints.auth.login).toBe('/auth/login');
    expect(endpoints.auth.register).toBe('/auth/register');
    expect(endpoints.users.base).toBe('/users');
    expect(endpoints.companies.byId('123')).toBe('/companies/123');
    expect(endpoints.posts.feed).toBe('/posts/feed');
  });

  it('should make API calls with correct parameters', async () => {
    // Setup
    const mockResponse = { data: { success: true } };
    const mockAxiosInstance = axios.create();
    mockAxiosInstance.get.mockResolvedValueOnce(mockResponse);

    // Execute
    await api.get('/test-endpoint');

    // Assert
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/test-endpoint');
  });
});
