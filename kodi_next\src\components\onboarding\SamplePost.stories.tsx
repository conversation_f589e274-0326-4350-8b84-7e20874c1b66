import type { Meta, StoryObj } from '@storybook/react';
import SamplePost from './SamplePost';

const meta: Meta<typeof SamplePost> = {
  title: 'Components/Onboarding/SamplePost',
  component: SamplePost,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof SamplePost>;

export const Default: Story = {
  args: {
    companyName: 'Acme Inc',
  },
};

export const LongCompanyName: Story = {
  args: {
    companyName: 'Super Long Company Name That Might Wrap Around The Screen Incorporated',
  },
};

export const LightMode: Story = {
  args: {
    companyName: 'Acme Inc',
  },
  parameters: {
    backgrounds: { default: 'light' },
    theme: 'light',
  },
};

export const DarkMode: Story = {
  args: {
    companyName: 'Acme Inc',
  },
  parameters: {
    backgrounds: { default: 'dark' },
    theme: 'dark',
  },
};
