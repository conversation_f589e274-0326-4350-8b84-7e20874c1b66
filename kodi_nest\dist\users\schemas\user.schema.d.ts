import { Document, Schema as MongooseSchema } from 'mongoose';
import { Profile } from '../../profiles/schemas/profile.schema';
export type UserDocument = User & Document;
export declare class User {
    email: string;
    password: string;
    roles: string[];
    profile: Profile;
    lastLogin: Date;
    settings: Record<string, any>;
}
export declare const UserSchema: MongooseSchema<User, import("mongoose").Model<User, any, any, any, Document<unknown, any, User> & User & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, User, Document<unknown, {}, import("mongoose").FlatRecord<User>> & import("mongoose").FlatRecord<User> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
