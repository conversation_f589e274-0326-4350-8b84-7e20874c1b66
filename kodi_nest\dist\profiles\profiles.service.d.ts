import { Model } from 'mongoose';
import { ProfileDocument } from './schemas/profile.schema';
import { CreateProfileDto } from './dto/create-profile.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
export declare class ProfilesService {
    private profileModel;
    constructor(profileModel: Model<ProfileDocument>);
    create(createProfileDto: CreateProfileDto): Promise<ProfileDocument>;
    findAll(): Promise<ProfileDocument[]>;
    findByCompany(companyId: string): Promise<ProfileDocument[]>;
    findById(id: string): Promise<ProfileDocument>;
    findByUserId(userId: string): Promise<ProfileDocument>;
    update(id: string, updateProfileDto: UpdateProfileDto): Promise<ProfileDocument>;
    updateUserSettings(userId: string, userSettings: any): Promise<ProfileDocument>;
    remove(id: string): Promise<ProfileDocument>;
}
