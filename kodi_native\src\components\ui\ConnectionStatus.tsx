import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { Colors } from '../../constants/Theme';
import { ConnectionStatus as ConnectionStatusType, checkServerConnection } from '../../utils/connectionCheck';

interface ConnectionStatusProps {
  onStatusChange?: (status: ConnectionStatusType) => void;
  showRetry?: boolean;
}

export interface ConnectionStatusRef {
  testServerConnection: () => Promise<void>;
}

const ConnectionStatus = forwardRef<ConnectionStatusRef, ConnectionStatusProps>(
  ({ onStatusChange, showRetry = true }, ref) => {
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;

  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatusType>({
    server: 'unknown',
    message: 'Checking connection...',
    timestamp: Date.now(),
  });

  const testServerConnection = async () => {
    setConnectionStatus({
      server: 'unknown',
      message: 'Testing connection to server...',
      timestamp: Date.now(),
    });

    const status = await checkServerConnection();
    setConnectionStatus(status);
  };

  // Expose the testServerConnection function to parent components
  useImperativeHandle(ref, () => ({
    testServerConnection
  }));

  useEffect(() => {
    testServerConnection();
  }, []);

  useEffect(() => {
    if (onStatusChange) {
      onStatusChange(connectionStatus);
    }
  }, [connectionStatus, onStatusChange]);

  return (
    <View style={[
      styles.connectionStatus,
      {
        backgroundColor:
          connectionStatus.server === 'connected' ? colors.success + '20' :
          connectionStatus.server === 'error' ? colors.destructive + '20' :
          colors.muted + '20'
      }
    ]}>
      <Ionicons
        name={
          connectionStatus.server === 'connected' ? 'checkmark-circle' :
          connectionStatus.server === 'error' ? 'alert-circle' :
          'time'
        }
        size={16}
        color={
          connectionStatus.server === 'connected' ? colors.success :
          connectionStatus.server === 'error' ? colors.destructive :
          colors.muted
        }
        style={styles.connectionIcon}
      />
      <Text style={[
        styles.connectionText,
        {
          color:
            connectionStatus.server === 'connected' ? colors.success :
            connectionStatus.server === 'error' ? colors.destructive :
            colors.muted
        }
      ]}>
        {connectionStatus.server === 'connected' ? 'Connected to server' : connectionStatus.message}
      </Text>
      {connectionStatus.server === 'error' && showRetry && (
        <TouchableOpacity onPress={testServerConnection} style={styles.retryButton}>
          <Text style={[styles.retryText, { color: colors.primary }]}>Retry</Text>
        </TouchableOpacity>
      )}
    </View>
  );
});

export default ConnectionStatus;

const styles = StyleSheet.create({
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginBottom: 16,
  },
  connectionIcon: {
    marginRight: 8,
  },
  connectionText: {
    fontSize: 14,
    flex: 1,
  },
  retryButton: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 4,
  },
  retryText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
