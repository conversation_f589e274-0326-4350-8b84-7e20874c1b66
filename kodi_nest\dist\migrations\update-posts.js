"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongodb_1 = require("mongodb");
const dotenv = require("dotenv");
dotenv.config();
async function updatePosts() {
    const uri = process.env.MONGODB_URI || '';
    if (!uri) {
        console.error('MongoDB URI not found in environment variables');
        process.exit(1);
    }
    const client = new mongodb_1.MongoClient(uri);
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        const database = client.db();
        const postsCollection = database.collection('kd_posts');
        const result = await postsCollection.updateMany({ audioUrl: { $exists: false } }, { $set: { audioUrl: null } });
        console.log(`Updated ${result.modifiedCount} posts to include audioUrl field`);
    }
    catch (error) {
        console.error('Error updating posts:', error);
    }
    finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}
updatePosts().catch(console.error);
//# sourceMappingURL=update-posts.js.map