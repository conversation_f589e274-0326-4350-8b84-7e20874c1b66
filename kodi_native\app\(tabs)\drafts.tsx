import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  RefreshControl,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Card from '@/src/components/ui/Card';
import { Post, Team, PostType, Tag } from '@/src/types';
import PostItem from '@/src/components/posts/PostItem';

export default function DraftsScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;

  const [drafts, setDrafts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDrafts();
  }, []);

  const fetchDrafts = async () => {
    try {
      setLoading(true);
      const { data } = await api.get(endpoints.posts.drafts);
      setDrafts(data);
    } catch (err: any) {
      console.error('Failed to fetch drafts:', err);
      setError('Failed to load drafts. Please try again later.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const publishDraft = async (draftId: string) => {
    try {
      await api.patch(`${endpoints.posts.byId(draftId)}`, {
        isDraft: false,
      });
      fetchDrafts();
    } catch (err: any) {
      console.error('Failed to publish draft:', err);
      Alert.alert('Error', 'Failed to publish draft. Please try again later.');
    }
  };

  const deleteDraft = async (draftId: string) => {
    try {
      await api.delete(endpoints.posts.byId(draftId));
      fetchDrafts();
    } catch (err: any) {
      console.error('Failed to delete draft:', err);
      Alert.alert('Error', 'Failed to delete draft. Please try again later.');
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchDrafts();
  };

  const renderEmptyState = () => {
    return (
      <View style={styles.emptyStateContainer}>
        <Card style={styles.emptyStateCard}>
          <Ionicons name="document-text-outline" size={48} color={colors.mutedForeground} style={styles.emptyIcon} />
          <Text style={[styles.emptyStateTitle, { color: colors.foreground }]}>
            No Drafts Yet
          </Text>
          <Text style={[styles.emptyStateText, { color: colors.mutedForeground }]}>
            Your draft posts will appear here. Start creating a post to save it as a draft.
          </Text>
          <TouchableOpacity
            style={[styles.createButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/(tabs)/post')}
          >
            <Text style={[styles.createButtonText, { color: colors.primaryForeground }]}>
              Create a Post
            </Text>
          </TouchableOpacity>
        </Card>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {error && (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={fetchDrafts}
            accessibilityRole="button"
            accessibilityLabel="Retry loading drafts"
          >
            <Text style={[styles.retryButtonText, { color: colors.primaryForeground }]}>Retry</Text>
          </TouchableOpacity>
        </View>
      )}

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.mutedForeground }]}>Loading drafts...</Text>
        </View>
      ) : !error && (
        <FlatList
          data={drafts}
          keyExtractor={(item) => item._id}
          renderItem={({ item }) => (
            <PostItem
              post={item}
              isDraft={true}
              onPublish={publishDraft}
              onDelete={deleteDraft}
            />
          )}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  listContent: {
    padding: 16,
  },
  emptyStateContainer: {
    padding: 16,
  },
  emptyStateCard: {
    padding: 24,
    alignItems: 'center',
    marginTop: 16,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptyStateText: {
    textAlign: 'center',
    fontSize: 16,
    marginBottom: 16,
  },
  createButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  createButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  errorContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: '#FEE2E2', // Light red background for error
    borderRadius: 8,
    marginBottom: 24,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
    color: '#B91C1C', // Red-700 equivalent
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
