{"name": "kodi_next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3010", "dev:turbo": "next dev --turbopack -p 3010", "build": "next build --no-lint", "start": "next start -p 3010", "lint": "echo '<PERSON><PERSON><PERSON> temporarily disabled'", "lint:a11y": "eslint --ext .js,.jsx,.ts,.tsx --config .eslintrc.a11y.js src/", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:a11y": "jest --testMatch='**/*.a11y.test.{ts,tsx}'", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test-storybook": "test-storybook", "chromatic": "npx chromatic --project-token=${CHROMATIC_PROJECT_TOKEN}", "lighthouse": "lhci autorun", "lighthouse:mobile": "lhci autorun --collect.settings.formFactor=mobile", "lighthouse:desktop": "lhci autorun --collect.settings.formFactor=desktop"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "axios": "^1.6.7", "next": "15.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.51.0", "zod": "^3.22.4"}, "devDependencies": {"@axe-core/react": "^4.8.5", "@eslint/eslintrc": "^3", "@lhci/cli": "^0.13.0", "@storybook/addon-a11y": "^7.6.17", "@storybook/addon-coverage": "^0.0.9", "@storybook/addon-essentials": "^7.6.17", "@storybook/addon-interactions": "^7.6.17", "@storybook/addon-links": "^7.6.17", "@storybook/addon-viewport": "^7.6.17", "@storybook/blocks": "^7.6.17", "@storybook/nextjs": "^7.6.17", "@storybook/react": "^7.6.17", "@storybook/test-runner": "^0.16.0", "@storybook/testing-library": "^0.2.2", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "axe-core": "^4.8.4", "chromatic": "^10.9.6", "eslint": "^9", "eslint-config-next": "15.3.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-storybook": "^0.6.15", "jest": "^29.7.0", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.2.3", "react-native-svg-app-icon": "^0.6.1", "storybook": "^7.6.17", "tailwindcss": "^4", "typescript": "^5"}}