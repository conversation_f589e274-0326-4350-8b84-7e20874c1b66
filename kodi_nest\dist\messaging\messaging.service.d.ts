import { Model } from 'mongoose';
import { ConversationDocument } from './schemas/conversation.schema';
import { MessageDocument } from './schemas/message.schema';
import { UsersService } from '../users/users.service';
export declare class MessagingService {
    private conversationModel;
    private messageModel;
    private usersService;
    constructor(conversationModel: Model<ConversationDocument>, messageModel: Model<MessageDocument>, usersService: UsersService);
    createConversation(participants: string[], title?: string, isGroup?: boolean): Promise<ConversationDocument>;
    findConversationById(id: string): Promise<ConversationDocument>;
    findConversationsByUserId(userId: string): Promise<ConversationDocument[]>;
    createMessage(conversationId: string, senderId: string, content: string): Promise<MessageDocument>;
    findMessagesByConversationId(conversationId: string): Promise<MessageDocument[]>;
    markMessageAsRead(messageId: string, userId: string): Promise<MessageDocument>;
}
