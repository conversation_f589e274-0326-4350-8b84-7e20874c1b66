{"version": 3, "file": "ai.service.js", "sourceRoot": "", "sources": ["../../src/ai/ai.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAwG;AACxG,+CAA+C;AAC/C,uCAAiC;AACjC,6DAAoE;AACpE,6DAAoE;AACpE,iEAAwE;AACxE,6DAA+E;AAC/E,uEAAiF;AAOjF,0DAAsD;AAG/C,IAAM,SAAS,GAAf,MAAM,SAAS;IAEiB;IACA;IACC;IACF;IACK;IAC/B;IANV,YACqC,YAAoC,EACpC,YAAoC,EACnC,aAAsC,EACxC,WAAkC,EAC7B,gBAA4C,EAC3E,YAA0B;QALC,iBAAY,GAAZ,YAAY,CAAwB;QACpC,iBAAY,GAAZ,YAAY,CAAwB;QACnC,kBAAa,GAAb,aAAa,CAAyB;QACxC,gBAAW,GAAX,WAAW,CAAuB;QAC7B,qBAAgB,GAAhB,gBAAgB,CAA4B;QAC3E,iBAAY,GAAZ,YAAY,CAAc;IACjC,CAAC;IAGJ,KAAK,CAAC,aAAa,CAAC,gBAAkC;QACpD,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAC3D,OAAO,UAAU,CAAC,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,MAAe,EACf,SAAkB,EAClB,GAAY;QAEZ,MAAM,KAAK,GAAQ,EAAE,CAAC;QAEtB,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9B,CAAC;QAED,IAAI,GAAG,EAAE,CAAC;YACR,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,CAAC,YAAY;aACrB,IAAI,CAAC,KAAK,CAAC;aACX,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;aACvB,IAAI,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAU;QAC9B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU,EAAE,gBAAqB;QACnD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY;aAC3C,iBAAiB,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;aACtD,IAAI,EAAE,CAAC;QAEV,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU;QAC5B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAE5E,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,gBAAkC;QAGtD,MAAM,WAAW,GAAG,qDAAqD,gBAAgB,CAAC,UAAU;;;;;;;iGAOP,CAAC;QAE9F,gBAAgB,CAAC,OAAO,GAAG,WAAW,CAAC;QACvC,gBAAgB,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAE1C,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAC9C,CAAC;IAGD,KAAK,CAAC,aAAa,CAAC,gBAAkC;QACpD,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAC3D,OAAO,UAAU,CAAC,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAAe,EACf,SAAkB,EAClB,GAAY;QAEZ,MAAM,KAAK,GAAQ,EAAE,CAAC;QAEtB,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9B,CAAC;QAED,IAAI,GAAG,EAAE,CAAC;YACR,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,CAAC,YAAY;aACrB,IAAI,CAAC,KAAK,CAAC;aACX,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;aACvB,IAAI,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAU;QAC9B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU,EAAE,gBAAqB;QACnD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY;aAC3C,iBAAiB,CAAC,EAAE,EAAE,gBAAgB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;aACtD,IAAI,EAAE,CAAC;QAEV,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU;QAC5B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAE5E,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,gBAAkC;QAGrD,MAAM,WAAW,GAAG,6CAA6C,gBAAgB,CAAC,KAAK;;;;;;;qGAOU,CAAC;QAElG,gBAAgB,CAAC,OAAO,GAAG,WAAW,CAAC;QAEvC,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAC9C,CAAC;IAGD,KAAK,CAAC,cAAc,CAAC,iBAAoC;QACvD,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QAC9D,OAAO,WAAW,CAAC,IAAI,EAAE,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,SAAkB,EAClB,QAAkB;QAElB,MAAM,KAAK,GAAQ,EAAE,CAAC;QAEtB,IAAI,SAAS,EAAE,CAAC;YACd,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9B,CAAC;QAED,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC5B,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,EAAU;QAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAC9D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,EAAU,EAAE,iBAAsB;QACrD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,aAAa;aAC7C,iBAAiB,CAAC,EAAE,EAAE,iBAAiB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;aACvD,IAAI,EAAE,CAAC;QAEV,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,MAAM,IAAI,0BAAiB,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,EAAU;QAC7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAE9E,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,MAAM,IAAI,0BAAiB,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAGD,KAAK,CAAC,YAAY,CAAC,eAAgC;QACjD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QACxD,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAc;QACjC,OAAO,IAAI,CAAC,WAAW;aACpB,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;aAChB,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;aACvB,IAAI,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,EAAU;QAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAC1D,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,eAAoB;QACjD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW;aACzC,iBAAiB,CAAC,EAAE,EAAE,eAAe,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;aACrD,IAAI,EAAE,CAAC;QAEV,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,EAAU;QAC3B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAE1E,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,OAAe,EAAE,MAAc;QACnE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5D,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,0BAAiB,CAAC,mBAAmB,MAAM,YAAY,CAAC,CAAC;QACrE,CAAC;QAGD,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,CAAC;YACtC,MAAM,IAAI,2BAAkB,CAAC,gDAAgD,CAAC,CAAC;QACjF,CAAC;QAGD,MAAM,WAAW,GAAgB;YAC/B,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAIF,MAAM,UAAU,GAAgB;YAC9B,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,gCAAgC,OAAO;;6HAEuE;YACvH,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAGF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAG5C,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC7E,CAAC;QAED,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;IACrB,CAAC;IAGD,KAAK,CAAC,iBAAiB,CAAC,oBAA0C;QAChE,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;QACvE,OAAO,cAAc,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,IAAa,EACb,SAAkB;QAElB,MAAM,KAAK,GAAQ,EAAE,CAAC;QAEtB,IAAI,IAAI,EAAE,CAAC;YACT,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9B,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,EAAU;QAClC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QACpE,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC;QACtE,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,EAAU,EAAE,oBAAyB;QAC3D,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,gBAAgB;aACnD,iBAAiB,CAAC,EAAE,EAAE,oBAAoB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;aAC1D,IAAI,EAAE,CAAC;QAEV,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxB,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,EAAU;QAChC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAEpF,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxB,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAGD,KAAK,CAAC,eAAe,CAAC,kBAAsC;QAC1D,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,kBAAkB,CAAC;QAKpD,IAAI,aAAa,GAAG,2DAA2D,CAAC;QAGhF,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;gBACtE,IAAI,QAAQ,EAAE,CAAC;oBAGb,aAAa,IAAI,+BAA+B,QAAQ,CAAC,IAAI,GAAG,CAAC;gBACnE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IACjC,CAAC;CACF,CAAA;AA7XY,8BAAS;oBAAT,SAAS;IADrB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,wBAAO,CAAC,IAAI,CAAC,CAAA;IACzB,WAAA,IAAA,sBAAW,EAAC,wBAAO,CAAC,IAAI,CAAC,CAAA;IACzB,WAAA,IAAA,sBAAW,EAAC,2BAAQ,CAAC,IAAI,CAAC,CAAA;IAC1B,WAAA,IAAA,sBAAW,EAAC,uBAAM,CAAC,IAAI,CAAC,CAAA;IACxB,WAAA,IAAA,sBAAW,EAAC,iCAAW,CAAC,IAAI,CAAC,CAAA;qCAJmB,gBAAK;QACL,gBAAK;QACH,gBAAK;QACT,gBAAK;QACK,gBAAK;QACxC,4BAAY;GAPzB,SAAS,CA6XrB"}