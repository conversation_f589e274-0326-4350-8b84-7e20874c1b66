import { <PERSON>, Get, Post, Body, Param, UseGuards, Request } from '@nestjs/common';
import { MessagingService } from './messaging.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('messaging')
@UseGuards(JwtAuthGuard)
export class MessagingController {
  constructor(private readonly messagingService: MessagingService) {}

  @Post('conversations')
  async createConversation(
    @Body() createConversationDto: { participants: string[]; title?: string; isGroup?: boolean },
    @Request() req,
  ) {
    // Add current user to participants if not already included
    if (!createConversationDto.participants.includes(req.user.userId)) {
      createConversationDto.participants.push(req.user.userId);
    }

    return this.messagingService.createConversation(
      createConversationDto.participants,
      createConversationDto.title,
      createConversationDto.isGroup || false,
    );
  }

  @Get('conversations')
  async findUserConversations(@Request() req) {
    return this.messagingService.findConversationsByUserId(req.user.userId);
  }

  @Get('conversations/:id')
  async findConversation(@Param('id') id: string) {
    return this.messagingService.findConversationById(id);
  }

  @Get('conversations/:id/messages')
  async findConversationMessages(@Param('id') id: string) {
    return this.messagingService.findMessagesByConversationId(id);
  }

  @Post('conversations/:id/messages')
  async createMessage(
    @Param('id') id: string,
    @Body() createMessageDto: { content: string },
    @Request() req,
  ) {
    return this.messagingService.createMessage(
      id,
      req.user.userId,
      createMessageDto.content,
    );
  }

  @Post('messages/:id/read')
  async markMessageAsRead(@Param('id') id: string, @Request() req) {
    return this.messagingService.markMessageAsRead(id, req.user.userId);
  }
}
