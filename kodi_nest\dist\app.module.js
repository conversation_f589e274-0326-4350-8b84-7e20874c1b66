"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const platform_express_1 = require("@nestjs/platform-express");
const jwt_1 = require("@nestjs/jwt");
const users_module_1 = require("./users/users.module");
const auth_module_1 = require("./auth/auth.module");
const companies_module_1 = require("./companies/companies.module");
const profiles_module_1 = require("./profiles/profiles.module");
const uploads_module_1 = require("./uploads/uploads.module");
const messaging_module_1 = require("./messaging/messaging.module");
const teams_module_1 = require("./teams/teams.module");
const posts_module_1 = require("./posts/posts.module");
const ai_module_1 = require("./ai/ai.module");
const health_module_1 = require("./health/health.module");
const email_module_1 = require("./email/email.module");
const user_context_middleware_1 = require("./auth/middleware/user-context.middleware");
let AppModule = class AppModule {
    configure(consumer) {
        consumer
            .apply(user_context_middleware_1.UserContextMiddleware)
            .forRoutes('posts/types', 'teams', 'posts/tags');
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            platform_express_1.MulterModule.register({
                dest: './uploads',
            }),
            mongoose_1.MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb+srv://CKT_place3:<EMAIL>/place3?retryWrites=true&w=majority'),
            jwt_1.JwtModule.register({
                secret: process.env.JWT_SECRET,
                signOptions: { expiresIn: '1h' },
            }),
            users_module_1.UsersModule,
            auth_module_1.AuthModule,
            companies_module_1.CompaniesModule,
            profiles_module_1.ProfilesModule,
            uploads_module_1.UploadsModule,
            messaging_module_1.MessagingModule,
            teams_module_1.TeamsModule,
            posts_module_1.PostsModule,
            ai_module_1.AiModule,
            health_module_1.HealthModule,
            email_module_1.EmailModule,
        ],
        providers: [user_context_middleware_1.UserContextMiddleware],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map