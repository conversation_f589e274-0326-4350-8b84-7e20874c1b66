"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagingService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const conversation_schema_1 = require("./schemas/conversation.schema");
const message_schema_1 = require("./schemas/message.schema");
const users_service_1 = require("../users/users.service");
let MessagingService = class MessagingService {
    conversationModel;
    messageModel;
    usersService;
    constructor(conversationModel, messageModel, usersService) {
        this.conversationModel = conversationModel;
        this.messageModel = messageModel;
        this.usersService = usersService;
    }
    async createConversation(participants, title, isGroup = false) {
        for (const userId of participants) {
            await this.usersService.findById(userId);
        }
        const conversation = new this.conversationModel({
            participants,
            title: title || null,
            isGroup,
        });
        return conversation.save();
    }
    async findConversationById(id) {
        const conversation = await this.conversationModel.findById(id).exec();
        if (!conversation) {
            throw new common_1.NotFoundException(`Conversation with ID ${id} not found`);
        }
        return conversation;
    }
    async findConversationsByUserId(userId) {
        return this.conversationModel
            .find({ participants: userId })
            .sort({ lastMessageAt: -1 })
            .exec();
    }
    async createMessage(conversationId, senderId, content) {
        await this.findConversationById(conversationId);
        await this.usersService.findById(senderId);
        const message = new this.messageModel({
            conversationId,
            senderId,
            content,
        });
        await this.conversationModel.findByIdAndUpdate(conversationId, { lastMessageAt: new Date() }).exec();
        return message.save();
    }
    async findMessagesByConversationId(conversationId) {
        return this.messageModel
            .find({ conversationId })
            .sort({ createdAt: 1 })
            .exec();
    }
    async markMessageAsRead(messageId, userId) {
        const message = await this.messageModel.findById(messageId).exec();
        if (!message) {
            throw new common_1.NotFoundException(`Message with ID ${messageId} not found`);
        }
        if (message.senderId.toString() !== userId) {
            message.isRead = true;
            message.readAt = new Date();
            return message.save();
        }
        return message;
    }
};
exports.MessagingService = MessagingService;
exports.MessagingService = MessagingService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(conversation_schema_1.Conversation.name)),
    __param(1, (0, mongoose_1.InjectModel)(message_schema_1.Message.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        users_service_1.UsersService])
], MessagingService);
//# sourceMappingURL=messaging.service.js.map