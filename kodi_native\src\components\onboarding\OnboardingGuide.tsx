import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';

interface OnboardingStep {
  title: string;
  description: string;
  action?: string;
  link?: string;
}

export default function OnboardingGuide() {
  const { user, updateUserSettings } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  const [currentStep, setCurrentStep] = useState(0);
  const [dismissed, setDismissed] = useState(false);
  const [animation] = useState(new Animated.Value(0));

  // Define the onboarding steps
  const steps: OnboardingStep[] = [
    {
      title: 'Welcome to <PERSON><PERSON>!',
      description: '<PERSON><PERSON> helps your team communicate through voice-based posts with AI-powered insights.',
      action: 'Next',
    },
    {
      title: 'Create Your First Post',
      description: 'Tap the POST button in the navigation bar to create your first post. You can record audio or type directly.',
      action: 'Try it',
      link: '/(tabs)/post',
    },
    {
      title: 'Explore Your Feed',
      description: 'Your feed shows posts from your team. Use filters to find specific content.',
      action: 'Next',
    },
    {
      title: 'Invite Your Team',
      description: 'Go to Profile > Company to find your invite link and share it with your team.',
      action: 'Go to Settings',
      link: '/company',
    },
  ];

  // Animate the component when it mounts
  React.useEffect(() => {
    Animated.timing(animation, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleDismiss();
    }
  };

  const handleDismiss = async () => {
    // Animate out
    Animated.timing(animation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setDismissed(true);
    });

    try {
      await updateUserSettings({
        showOnboarder: false,
      });
    } catch (err) {
      console.error('Failed to update onboarding settings:', err);
    }
  };

  if (dismissed || user?.profile?.userSettings?.showOnboarder === false) {
    return null;
  }

  const currentStepData = steps[currentStep];

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: colors.card,
          borderColor: colors.primary,
          shadowColor: colors.foreground,
          transform: [
            { translateY: animation.interpolate({
                inputRange: [0, 1],
                outputRange: [50, 0],
              })
            },
            { scale: animation.interpolate({
                inputRange: [0, 1],
                outputRange: [0.9, 1],
              })
            }
          ],
          opacity: animation,
        },
      ]}
    >
      <TouchableOpacity
        style={styles.closeButton}
        onPress={handleDismiss}
        accessibilityRole="button"
        accessibilityLabel="Close onboarding guide"
      >
        <Ionicons name="close" size={20} color={colors.mutedForeground} />
      </TouchableOpacity>
      
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.foreground }]}>
          {currentStepData.title}
        </Text>
        <Text style={[styles.description, { color: colors.mutedForeground }]}>
          {currentStepData.description}
        </Text>
      </View>
      
      <View style={styles.footer}>
        <View style={styles.indicators}>
          {steps.map((_, index) => (
            <View
              key={index}
              style={[
                styles.indicator,
                {
                  backgroundColor: index === currentStep ? colors.primary : colors.border,
                },
              ]}
            />
          ))}
        </View>
        
        <View style={styles.buttons}>
          {currentStep > 0 && (
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => setCurrentStep(currentStep - 1)}
              accessibilityRole="button"
              accessibilityLabel="Go back to previous step"
            >
              <Text style={[styles.backButtonText, { color: colors.mutedForeground }]}>
                Back
              </Text>
            </TouchableOpacity>
          )}
          
          {currentStepData.link ? (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary }]}
              onPress={() => router.push(currentStepData.link as any)}
              accessibilityRole="button"
              accessibilityLabel={currentStepData.action}
            >
              <Text style={[styles.actionButtonText, { color: colors.primaryForeground }]}>
                {currentStepData.action}
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary }]}
              onPress={handleNext}
              accessibilityRole="button"
              accessibilityLabel={currentStepData.action}
            >
              <Text style={[styles.actionButtonText, { color: colors.primaryForeground }]}>
                {currentStepData.action}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Animated.View>
  );
}

const { width } = Dimensions.get('window');
const isSmallScreen = width < 375;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    left: 20,
    maxWidth: 400,
    alignSelf: 'center',
    borderRadius: 12,
    borderLeftWidth: 4,
    padding: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 1000,
  },
  closeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    padding: 4,
    zIndex: 1,
  },
  content: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  indicators: {
    flexDirection: 'row',
    gap: 4,
  },
  indicator: {
    height: 4,
    width: 24,
    borderRadius: 2,
  },
  buttons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  backButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  actionButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
