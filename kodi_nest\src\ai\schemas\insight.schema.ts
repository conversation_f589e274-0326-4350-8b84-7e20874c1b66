import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type InsightDocument = Insight & Document;

@Schema({
  collection: 'kd_insights',
  timestamps: true,
})
export class Insight {
  @Prop({ required: true })
  title: string;

  @Prop({ required: false })
  tag: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company', required: false, index: true })
  companyId: MongooseSchema.Types.ObjectId;

  @Prop({ required: false })
  touchPointName: string;

  @Prop({ type: Object, required: false })
  touchPointSource: any;

  @Prop({ required: false })
  touchPointText: string;

  @Prop({ required: false })
  summary: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true, index: true })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: 'Comment' }] })
  comments: MongooseSchema.Types.ObjectId[];
}

export const InsightSchema = SchemaFactory.createForClass(Insight);
