/**
 * Web implementation of expo-secure-store
 * This provides a localStorage-based implementation for web platforms
 */

// Define the interface to match the native SecureStore API
interface ExpoSecureStoreInterface {
  getValueWithKeyAsync: (key: string, options?: any) => Promise<string | null>;
  setValueWithKeyAsync: (value: string, key: string, options?: any) => Promise<void>;
  deleteValueWithKeyAsync: (key: string, options?: any) => Promise<void>;
  getValueWithKeySync?: (key: string, options?: any) => string | null;
  setValueWithKeySync?: (value: string, key: string, options?: any) => void;
  canUseBiometricAuthentication?: () => boolean;
  WHEN_UNLOCKED?: string;
  AFTER_FIRST_UNLOCK?: string;
  ALWAYS?: string;
  WHEN_UNLOCKED_THIS_DEVICE_ONLY?: string;
  WHEN_PASSCODE_SET_THIS_DEVICE_ONLY?: string;
  AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY?: string;
  ALWAYS_THIS_DEVICE_ONLY?: string;
}

// Create the web implementation
const ExpoSecureStore: ExpoSecureStoreInterface = {
  // Constants to match native API
  WHEN_UNLOCKED: 'whenUnlocked',
  AFTER_FIRST_UNLOCK: 'afterFirstUnlock',
  ALWAYS: 'always',
  WHEN_UNLOCKED_THIS_DEVICE_ONLY: 'whenUnlockedThisDeviceOnly',
  WHEN_PASSCODE_SET_THIS_DEVICE_ONLY: 'whenPasscodeSetThisDeviceOnly',
  AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY: 'afterFirstUnlockThisDeviceOnly',
  ALWAYS_THIS_DEVICE_ONLY: 'alwaysThisDeviceOnly',

  // Async methods
  async getValueWithKeyAsync(key: string, options?: any): Promise<string | null> {
    try {
      const value = localStorage.getItem(`secure_store_${key}`);
      return value;
    } catch (error) {
      console.error('Error reading from secure store:', error);
      return null;
    }
  },

  async setValueWithKeyAsync(value: string, key: string, options?: any): Promise<void> {
    try {
      localStorage.setItem(`secure_store_${key}`, value);
    } catch (error) {
      console.error('Error writing to secure store:', error);
      throw error;
    }
  },

  async deleteValueWithKeyAsync(key: string, options?: any): Promise<void> {
    try {
      localStorage.removeItem(`secure_store_${key}`);
    } catch (error) {
      console.error('Error deleting from secure store:', error);
      throw error;
    }
  },

  // Sync methods
  getValueWithKeySync(key: string, options?: any): string | null {
    try {
      return localStorage.getItem(`secure_store_${key}`);
    } catch (error) {
      console.error('Error reading from secure store:', error);
      return null;
    }
  },

  setValueWithKeySync(value: string, key: string, options?: any): void {
    try {
      localStorage.setItem(`secure_store_${key}`, value);
    } catch (error) {
      console.error('Error writing to secure store:', error);
      throw error;
    }
  },

  // Biometric authentication is not available on web
  canUseBiometricAuthentication(): boolean {
    return false;
  }
};

export default ExpoSecureStore;
