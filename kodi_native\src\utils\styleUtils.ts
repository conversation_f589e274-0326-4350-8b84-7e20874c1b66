import { Platform } from 'react-native';

/**
 * Creates a cross-platform shadow style
 * Handles the differences between iOS, Android, and Web
 */
export const createShadow = (
  elevation: number = 5,
  shadowColor: string = '#000',
  shadowOpacity: number = 0.2,
  shadowRadius: number = 3.84,
  shadowOffset: { width: number; height: number } = { width: 0, height: 2 }
) => {
  if (Platform.OS === 'android') {
    // Android-specific shadow
    return {
      elevation,
      shadowColor,
    };
  } else if (Platform.OS === 'ios') {
    // iOS-specific shadow
    return {
      shadowColor,
      shadowOffset,
      shadowOpacity,
      shadowRadius,
    };
  } else {
    // Web shadow using boxShadow
    const { width, height } = shadowOffset;
    return {
      boxShadow: `${width}px ${height}px ${shadowRadius}px rgba(0, 0, 0, ${shadowOpacity})`,
    };
  }
};

/**
 * Creates styles for a card component with shadow
 */
export const createCardStyle = (backgroundColor: string = '#fff') => {
  return {
    backgroundColor,
    borderRadius: 8,
    padding: 16,
    margin: 8,
    ...createShadow(),
  };
};
