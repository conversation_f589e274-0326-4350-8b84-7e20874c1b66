import { ProfilesService } from './profiles.service';
import { CreateProfileDto } from './dto/create-profile.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
export declare class ProfilesController {
    private readonly profilesService;
    constructor(profilesService: ProfilesService);
    create(createProfileDto: CreateProfileDto): Promise<import("./schemas/profile.schema").ProfileDocument>;
    findAll(companyId?: string): Promise<import("./schemas/profile.schema").ProfileDocument[]>;
    findByUserId(userId: string): Promise<import("./schemas/profile.schema").ProfileDocument>;
    findOne(id: string): Promise<import("./schemas/profile.schema").ProfileDocument>;
    update(id: string, updateProfileDto: UpdateProfileDto): Promise<import("./schemas/profile.schema").ProfileDocument>;
    updateUserSettings(userId: string, userSettings: any): Promise<import("./schemas/profile.schema").ProfileDocument>;
    remove(id: string): Promise<import("./schemas/profile.schema").ProfileDocument>;
}
