import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ProfilePage from './page';
import { AuthProvider } from '@/contexts/AuthContext';
import api from '@/lib/api';

// Mock the API
jest.mock('@/lib/api', () => ({
  patch: jest.fn(),
  endpoints: {
    profiles: {
      byId: (id: string) => `/profiles/${id}`,
    },
  },
}));

// Mock the auth context
jest.mock('@/contexts/AuthContext', () => ({
  ...jest.requireActual('@/contexts/AuthContext'),
  useAuth: () => ({
    user: {
      id: 'user-1',
      email: '<EMAIL>',
      profile: {
        _id: 'profile-1',
        firstName: 'Test',
        lastName: 'User',
        role: 'Developer',
        userSettings: {
          showOnboarder: true,
        },
      },
    },
    updateUserSettings: jest.fn().mockResolvedValue({}),
  }),
}));

describe('ProfilePage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the profile page with user information', () => {
    render(
      <AuthProvider>
        <ProfilePage />
      </AuthProvider>
    );
    
    // Check that user info is displayed
    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Developer')).toBeInTheDocument();
    
    // Check for profile sections
    expect(screen.getByText('Profile Information')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
    
    // Check for edit button
    expect(screen.getByRole('button', { name: /Edit Profile/i })).toBeInTheDocument();
  });

  it('allows editing profile information', async () => {
    // Mock API response
    (api.patch as jest.Mock).mockResolvedValue({
      data: {
        _id: 'profile-1',
        firstName: 'Updated',
        lastName: 'Name',
        role: 'Senior Developer',
      },
    });
    
    render(
      <AuthProvider>
        <ProfilePage />
      </AuthProvider>
    );
    
    // Click edit button
    const user = userEvent.setup();
    await user.click(screen.getByRole('button', { name: /Edit Profile/i }));
    
    // Check that form fields are displayed
    const firstNameInput = screen.getByLabelText(/First Name/i);
    const lastNameInput = screen.getByLabelText(/Last Name/i);
    const roleInput = screen.getByLabelText(/Role/i);
    
    expect(firstNameInput).toBeInTheDocument();
    expect(lastNameInput).toBeInTheDocument();
    expect(roleInput).toBeInTheDocument();
    
    // Update form fields
    await user.clear(firstNameInput);
    await user.type(firstNameInput, 'Updated');
    
    await user.clear(lastNameInput);
    await user.type(lastNameInput, 'Name');
    
    await user.clear(roleInput);
    await user.type(roleInput, 'Senior Developer');
    
    // Submit form
    await user.click(screen.getByRole('button', { name: /Save/i }));
    
    // Check that API was called with correct data
    await waitFor(() => {
      expect(api.patch).toHaveBeenCalledWith(
        '/profiles/profile-1',
        {
          firstName: 'Updated',
          lastName: 'Name',
          role: 'Senior Developer',
        }
      );
    });
    
    // Check for success message
    await waitFor(() => {
      expect(screen.getByText(/Profile updated successfully/i)).toBeInTheDocument();
    });
  });

  it('handles API errors when updating profile', async () => {
    // Mock API error
    (api.patch as jest.Mock).mockRejectedValue({
      response: {
        data: {
          message: 'Failed to update profile',
        },
      },
    });
    
    render(
      <AuthProvider>
        <ProfilePage />
      </AuthProvider>
    );
    
    // Click edit button
    const user = userEvent.setup();
    await user.click(screen.getByRole('button', { name: /Edit Profile/i }));
    
    // Submit form without changes
    await user.click(screen.getByRole('button', { name: /Save/i }));
    
    // Check for error message
    await waitFor(() => {
      expect(screen.getByText(/Failed to update profile/i)).toBeInTheDocument();
    });
  });

  it('allows canceling profile edit', async () => {
    render(
      <AuthProvider>
        <ProfilePage />
      </AuthProvider>
    );
    
    // Click edit button
    const user = userEvent.setup();
    await user.click(screen.getByRole('button', { name: /Edit Profile/i }));
    
    // Update form field
    const firstNameInput = screen.getByLabelText(/First Name/i);
    await user.clear(firstNameInput);
    await user.type(firstNameInput, 'Changed');
    
    // Click cancel button
    await user.click(screen.getByRole('button', { name: /Cancel/i }));
    
    // Check that we're back to view mode
    expect(screen.queryByLabelText(/First Name/i)).not.toBeInTheDocument();
    expect(screen.getByText('Test User')).toBeInTheDocument();
    
    // API should not have been called
    expect(api.patch).not.toHaveBeenCalled();
  });

  it('toggles onboarding setting', async () => {
    const mockUpdateUserSettings = require('@/contexts/AuthContext').useAuth().updateUserSettings;
    
    render(
      <AuthProvider>
        <ProfilePage />
      </AuthProvider>
    );
    
    // Find and click the toggle button
    const user = userEvent.setup();
    const toggleButton = screen.getByRole('switch');
    await user.click(toggleButton);
    
    // Check that updateUserSettings was called with correct data
    expect(mockUpdateUserSettings).toHaveBeenCalledWith({
      showOnboarder: false,
    });
  });
});
