import { Document, Schema as MongooseSchema } from 'mongoose';
export type SummaryDocument = Summary & Document;
export declare class Summary {
    tag: string;
    teamId: MongooseSchema.Types.ObjectId;
    postTypeId: MongooseSchema.Types.ObjectId;
    timeRange: string;
    userPrompt: string;
    promptId: MongooseSchema.Types.ObjectId;
    summary: string;
    postIds: MongooseSchema.Types.ObjectId[];
    userId: MongooseSchema.Types.ObjectId;
    companyId: MongooseSchema.Types.ObjectId;
    lastUpdated: Date;
    comments: MongooseSchema.Types.ObjectId[];
}
export declare const SummarySchema: MongooseSchema<Summary, import("mongoose").Model<Summary, any, any, any, Document<unknown, any, Summary> & Summary & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Summary, Document<unknown, {}, import("mongoose").FlatRecord<Summary>> & import("mongoose").FlatRecord<Summary> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
