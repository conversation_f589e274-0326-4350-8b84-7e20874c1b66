import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  RefreshControl,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Card from '@/src/components/ui/Card';
import { Post, Team, PostType, Tag } from '@/src/types';
import PostItem from '@/src/components/posts/PostItem';

export default function AnalysisScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;

  const [analyses, setAnalyses] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchAnalyses();
  }, []);

  const fetchAnalyses = async () => {
    try {
      setLoading(true);
      const { data } = await api.get(endpoints.posts.analyses);
      setAnalyses(data);
    } catch (err: any) {
      console.error('Failed to fetch analyses:', err);
      setError('Failed to load analyses. Please try again later.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchAnalyses();
  };

  const renderEmptyState = () => {
    return (
      <View style={styles.emptyStateContainer}>
        <Card style={styles.emptyStateCard}>
          <Ionicons name="analytics-outline" size={48} color={colors.mutedForeground} style={styles.emptyIcon} />
          <Text style={[styles.emptyStateTitle, { color: colors.foreground }]}>
            No Analyses Yet
          </Text>
          <Text style={[styles.emptyStateText, { color: colors.mutedForeground }]}>
            AI-powered analyses of your posts will appear here. Create a post to generate an analysis.
          </Text>
          <TouchableOpacity
            style={[styles.createButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/(tabs)/post')}
          >
            <Text style={[styles.createButtonText, { color: colors.primaryForeground }]}>
              Create a Post
            </Text>
          </TouchableOpacity>
        </Card>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {error && (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={fetchAnalyses}
            accessibilityRole="button"
            accessibilityLabel="Retry loading analyses"
          >
            <Text style={[styles.retryButtonText, { color: colors.primaryForeground }]}>Retry</Text>
          </TouchableOpacity>
        </View>
      )}

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.mutedForeground }]}>Loading analyses...</Text>
        </View>
      ) : !error && (
        <FlatList
          data={analyses}
          keyExtractor={(item) => item._id}
          renderItem={({ item }) => (
            <PostItem post={item} isAnalysis={true} />
          )}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  listContent: {
    padding: 16,
  },
  emptyStateContainer: {
    padding: 16,
  },
  emptyStateCard: {
    padding: 24,
    alignItems: 'center',
    marginTop: 16,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptyStateText: {
    textAlign: 'center',
    fontSize: 16,
    marginBottom: 16,
  },
  createButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  createButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  errorContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: '#FEE2E2', // Light red background for error
    borderRadius: 8,
    marginBottom: 24,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
    color: '#B91C1C', // Red-700 equivalent
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
