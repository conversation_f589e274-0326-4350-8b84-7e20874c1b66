import { Controller, Post, Body, UseGuards, Get, Query, Param, Delete, UseInterceptors, UploadedFile } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { UploadsService } from './uploads.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('uploads')
export class UploadsController {
  constructor(private readonly uploadsService: UploadsService) {}

  @UseGuards(JwtAuthGuard)
  @Post('presigned-url')
  async generatePresignedUrl(
    @Body('fileType') fileType: string,
    @Body('isPrivate') isPrivate: boolean = false,
  ) {
    return this.uploadsService.generatePresignedUrl(fileType, isPrivate);
  }

  @UseGuards(JwtAuthGuard)
  @Post('avatar')
  async generateAvatarUploadUrl(
    @Body('fileType') fileType: string,
  ) {
    return this.uploadsService.generatePresignedUrl(fileType, true);
  }

  @UseGuards(JwtAuthGuard)
  @Post('logo')
  async generateLogoUploadUrl(
    @Body('fileType') fileType: string,
  ) {
    return this.uploadsService.generatePresignedUrl(fileType, true);
  }

  @UseGuards(JwtAuthGuard)
  @Post('audio')
  async generateAudioUploadUrl(
    @Body('fileType') fileType: string,
  ) {
    return this.uploadsService.generatePresignedUrl(fileType, false);
  }

  @UseGuards(JwtAuthGuard)
  @Post('audio-upload')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/audio',
        filename: (req, file, callback) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
          const ext = extname(file.originalname);
          const filename = `${uniqueSuffix}${ext}`;
          callback(null, filename);
        },
      }),
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.(webm|mp3|wav|m4a|ogg)$/)) {
          return callback(new Error('Only audio files are allowed!'), false);
        }
        callback(null, true);
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  )
  uploadAudio(@UploadedFile() file: Express.Multer.File) {
    return this.uploadsService.handleFileUpload(file, 'audio');
  }

  @UseGuards(JwtAuthGuard)
  @Post('document')
  async generateDocumentUploadUrl(
    @Body('fileType') fileType: string,
  ) {
    return this.uploadsService.generatePresignedUrl(fileType, false);
  }
}
