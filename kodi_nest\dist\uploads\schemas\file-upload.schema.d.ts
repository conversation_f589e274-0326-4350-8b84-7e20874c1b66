import { Document, Schema as MongooseSchema } from 'mongoose';
export type FileUploadDocument = FileUpload & Document;
export declare class FileUpload {
    originalName: string;
    mimeType: string;
    size: number;
    key: string;
    url: string;
    type: string;
    userId: MongooseSchema.Types.ObjectId;
    linkedObjectId: MongooseSchema.Types.ObjectId;
    objectType: string;
}
export declare const FileUploadSchema: MongooseSchema<FileUpload, import("mongoose").Model<FileUpload, any, any, any, Document<unknown, any, FileUpload> & FileUpload & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, FileUpload, Document<unknown, {}, import("mongoose").FlatRecord<FileUpload>> & import("mongoose").FlatRecord<FileUpload> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
