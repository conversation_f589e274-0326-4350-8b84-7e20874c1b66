import http from 'k6/http';
import { check, sleep } from 'k6';
import { Counter } from 'k6/metrics';
import { config } from './config.js';

// Custom metrics
const loginSuccessCounter = new Counter('login_success');
const loginFailCounter = new Counter('login_failure');

// Test configuration
export const options = {
  stages: config.stages.auth,
  thresholds: {
    http_req_duration: config.thresholds.http_req_duration,
    'login_success': config.thresholds.login_success,
  },
};

// Test scenario
export default function() {
  // Login request
  const loginPayload = JSON.stringify({
    email: config.testUser.email,
    password: config.testUser.password,
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const loginRes = http.post(`${config.apiBaseUrl}/auth/login`, loginPayload, params);

  // Check if login was successful
  const loginSuccess = check(loginRes, {
    'login status is 201': (r) => r.status === 201,
    'has access token': (r) => JSON.parse(r.body).access_token !== undefined,
  });

  if (loginSuccess) {
    loginSuccessCounter.add(1);

    // Extract the token from the response
    const token = JSON.parse(loginRes.body).access_token;

    // Use the token to make an authenticated request
    const authParams = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    };

    // Get user profile
    const profileRes = http.get(`${config.apiBaseUrl}/auth/me`, authParams);

    check(profileRes, {
      'profile status is 200': (r) => r.status === 200,
      'profile has email': (r) => JSON.parse(r.body).email !== undefined,
    });

    // Get posts feed
    const feedRes = http.get(`${config.apiBaseUrl}/posts/feed`, authParams);

    check(feedRes, {
      'feed status is 200': (r) => r.status === 200,
      'feed is an array': (r) => Array.isArray(JSON.parse(r.body)),
    });
  } else {
    loginFailCounter.add(1);
  }

  // Sleep between iterations
  sleep(1);
}
