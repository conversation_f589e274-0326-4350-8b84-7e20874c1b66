'use client';

import React, { useState } from 'react';
// import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import Link from 'next/link';

export default function AppearanceSettingsPage() {
  // We can use user data here if needed for user-specific settings
  // const { user } = useAuth();
  const { theme: currentTheme, setTheme: setAppTheme } = useTheme();
  const [successMessage, setSuccessMessage] = useState('');

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setAppTheme(newTheme);
    // Save the theme preference to the user's settings
    setSuccessMessage('Theme preference saved');

    setTimeout(() => {
      setSuccessMessage('');
    }, 3000);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Appearance Settings</h1>
          <Link
            href="/settings"
            className="text-sm text-indigo-600 hover:text-indigo-800"
          >
            Back to Settings
          </Link>
        </div>

        {successMessage && (
          <div className="bg-green-50 p-4 rounded-md mb-6">
            <p className="text-green-700">{successMessage}</p>
          </div>
        )}

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Theme</h2>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-900 mb-3">Choose your preferred theme</p>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    id="theme-system"
                    name="theme"
                    type="radio"
                    checked={currentTheme === 'system'}
                    onChange={() => handleThemeChange('system')}
                    className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                  <label htmlFor="theme-system" className="ml-2 block text-sm text-gray-700">
                    System (follow device settings)
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="theme-light"
                    name="theme"
                    type="radio"
                    checked={currentTheme === 'light'}
                    onChange={() => handleThemeChange('light')}
                    className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                  <label htmlFor="theme-light" className="ml-2 block text-sm text-gray-700">
                    Light
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="theme-dark"
                    name="theme"
                    type="radio"
                    checked={currentTheme === 'dark'}
                    onChange={() => handleThemeChange('dark')}
                    className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                  <label htmlFor="theme-dark" className="ml-2 block text-sm text-gray-700">
                    Dark
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
