'use client';

import { useEffect } from 'react';
import { initMonitoring } from '@/lib/monitoring';

// This component doesn't render anything visible
// It just initializes the monitoring when mounted
export default function Monitoring() {
  useEffect(() => {
    // Initialize monitoring on client-side only
    initMonitoring();
  }, []);

  // Return null since this component doesn't render anything
  return null;
}
