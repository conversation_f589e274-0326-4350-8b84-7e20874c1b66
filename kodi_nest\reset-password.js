// Simple script to reset a user's password
const { MongoClient } = require('mongodb');
const bcrypt = require('bcrypt');

async function resetPassword() {
  // Connection URL and Database Name
  const url = 'mongodb+srv://CKT_place3:<EMAIL>';
  const dbName = 'place3'; // Database name from the connection string

  // User ID and new password
  const userId = '67f910cfc0e98226f74231bf';
  const newPassword = 'password1234';

  // Create a new MongoClient
  const client = new MongoClient(url);

  try {
    // Connect to the MongoDB server
    await client.connect();
    console.log('Connected to MongoDB server');

    // Get the database and collection
    const db = client.db(dbName);
    const usersCollection = db.collection('kd_users');

    // Hash the new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

	console.log('hashedPassword: "',hashedPassword,'"');
    // Update the user's password
    const result = await usersCollection.updateOne(
      { _id: userId },
      { $set: { 'services.password.bcrypt': hashedPassword } }
    );

    if (result.matchedCount === 0) {
      console.error(`User with ID ${userId} not found`);
    } else if (result.modifiedCount === 0) {
      console.log('Password was already set to the same value');
    } else {
      console.log(`Password for user ${userId} has been changed successfully`);
    }
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    // Close the connection
    await client.close();
    console.log('Connection closed');
  }
}

// Run the function
resetPassword().catch(console.error);
