import { Is<PERSON>mail, IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ValidateIf } from 'class-validator';

export class RegisterDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ValidateIf(o => !o.lastName)
  @IsString()
  firstName?: string;

  @ValidateIf(o => !o.firstName)
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsString()
  inviteCode?: string;
}
