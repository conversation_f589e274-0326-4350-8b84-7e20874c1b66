import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AudioInputField from '@/src/components/ui/AudioInputField';

interface FilterOption {
  value: string;
  label: string;
}

interface FilterInputFieldProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: FilterOption[];
  colors: any;
  icon?: string;
}

export default function FilterInputField({
  label,
  value,
  onChange,
  options,
  colors,
  icon,
}: FilterInputFieldProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  
  const selectedOption = options.find(option => option.value === value);
  const displayText = selectedOption ? selectedOption.label : '';

  // Filter options based on search text
  const filteredOptions = searchText
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchText.toLowerCase())
      )
    : options;

  const handleAudioProcessed = (text: string) => {
    setSearchText(text);
    
    // Try to find a matching option
    const matchingOption = options.find(option => 
      option.label.toLowerCase().includes(text.toLowerCase())
    );
    
    if (matchingOption) {
      onChange(matchingOption.value);
      setModalVisible(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.label, { color: colors.foreground }]}>{label}</Text>
      
      <TouchableOpacity
        style={[
          styles.pickerButton,
          { 
            borderColor: colors.border,
            backgroundColor: colors.card,
          }
        ]}
        onPress={() => setModalVisible(true)}
        accessibilityRole="button"
        accessibilityLabel={label}
      >
        {icon && (
          <Ionicons 
            name={icon as any} 
            size={18} 
            color={colors.mutedForeground} 
            style={styles.icon} 
          />
        )}
        <Text 
          style={[
            styles.pickerText, 
            { 
              color: selectedOption ? colors.foreground : colors.mutedForeground 
            }
          ]}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {displayText || `Select ${label.toLowerCase()}...`}
        </Text>
        <Ionicons 
          name="chevron-down" 
          size={18} 
          color={colors.mutedForeground} 
        />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.foreground }]}>
              Select {label}
            </Text>
            <TouchableOpacity
              onPress={() => setModalVisible(false)}
              accessibilityRole="button"
              accessibilityLabel="Close"
            >
              <Ionicons name="close" size={24} color={colors.foreground} />
            </TouchableOpacity>
          </View>

          <AudioInputField
            label="Search"
            placeholder={`Search ${label.toLowerCase()}...`}
            value={searchText}
            onChangeText={setSearchText}
            onAudioProcessed={handleAudioProcessed}
            colors={colors}
            style={styles.searchInput}
          />

          <FlatList
            data={filteredOptions}
            keyExtractor={(item) => item.value}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.item,
                  value === item.value && { backgroundColor: colors.secondary }
                ]}
                onPress={() => {
                  onChange(item.value);
                  setModalVisible(false);
                }}
                accessibilityRole="button"
                accessibilityLabel={item.label}
              >
                <Text style={[styles.itemText, { color: colors.foreground }]}>
                  {item.label}
                </Text>
                {value === item.value && (
                  <Ionicons name="checkmark" size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
            )}
          />
        </SafeAreaView>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    height: 48,
  },
  pickerText: {
    flex: 1,
    fontSize: 16,
  },
  icon: {
    marginRight: 8,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  searchInput: {
    margin: 16,
    marginTop: 8,
    marginBottom: 8,
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#ccc',
  },
  itemText: {
    fontSize: 16,
  },
});
