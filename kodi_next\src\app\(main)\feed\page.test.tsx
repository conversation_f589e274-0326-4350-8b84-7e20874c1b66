import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import FeedPage from './page';
import { server } from '@/test-utils/mocks/server';
import { rest } from 'msw';
import { AuthProvider } from '@/contexts/AuthContext';

// Mock the components that are used in the FeedPage
jest.mock('@/components/posts/CreatePostForm', () => {
  return function MockCreatePostForm({ onPostCreated }: { onPostCreated: () => void }) {
    return (
      <div data-testid="create-post-form">
        <button onClick={onPostCreated}>Create Post</button>
      </div>
    );
  };
});

jest.mock('@/components/onboarding/SamplePost', () => {
  return function MockSamplePost({ companyName }: { companyName: string }) {
    return <div data-testid="sample-post">Sample post for {companyName}</div>;
  };
});

// Mock the auth context
jest.mock('@/contexts/AuthContext', () => ({
  ...jest.requireActual('@/contexts/AuthContext'),
  useAuth: () => ({
    user: {
      id: 'user-1',
      email: '<EMAIL>',
      profile: {
        _id: 'profile-1',
        firstName: 'Test',
        lastName: 'User',
        companyId: 'company-1',
      },
    },
    isAuthenticated: true,
    loading: false,
  }),
}));

describe('FeedPage', () => {
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  it('renders the feed page with posts', async () => {
    // Mock API responses
    server.use(
      rest.get('*/api/posts/feed', (req, res, ctx) => {
        return res(
          ctx.json([
            {
              _id: 'post-1',
              body: 'Test post 1',
              userId: 'user-1',
              createdAt: new Date().toISOString(),
              hashtags: ['test', 'first'],
              user: {
                firstName: 'Test',
                lastName: 'User',
              },
            },
            {
              _id: 'post-2',
              body: 'Test post 2',
              userId: 'user-2',
              createdAt: new Date().toISOString(),
              hashtags: ['test', 'second'],
              user: {
                firstName: 'Another',
                lastName: 'User',
              },
            },
          ])
        );
      })
    );

    render(
      <AuthProvider>
        <FeedPage />
      </AuthProvider>
    );

    // Check for loading state
    expect(screen.getByText(/Your Feed/i)).toBeInTheDocument();
    
    // Wait for posts to load
    await waitFor(() => {
      expect(screen.getByText('Test post 1')).toBeInTheDocument();
      expect(screen.getByText('Test post 2')).toBeInTheDocument();
    });

    // Check for user names
    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('Another User')).toBeInTheDocument();

    // Check for hashtags
    expect(screen.getByText('#test')).toBeInTheDocument();
    expect(screen.getByText('#first')).toBeInTheDocument();
    expect(screen.getByText('#second')).toBeInTheDocument();
  });

  it('shows empty state when no posts are available', async () => {
    // Mock empty posts response
    server.use(
      rest.get('*/api/posts/feed', (req, res, ctx) => {
        return res(ctx.json([]));
      })
    );

    render(
      <AuthProvider>
        <FeedPage />
      </AuthProvider>
    );

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    // Check for sample post and empty state message
    expect(screen.getByTestId('sample-post')).toBeInTheDocument();
    expect(screen.getByText(/No posts yet/i)).toBeInTheDocument();
  });

  it('handles filter selection', async () => {
    const user = userEvent.setup();
    
    // Mock posts response
    server.use(
      rest.get('*/api/posts/feed', (req, res, ctx) => {
        // Return different responses based on query parameters
        const url = new URL(req.url);
        const teamId = url.searchParams.get('teamId');
        
        if (teamId === 'team-1') {
          return res(
            ctx.json([
              {
                _id: 'filtered-post',
                body: 'Filtered team post',
                userId: 'user-1',
                createdAt: new Date().toISOString(),
                hashtags: [],
                user: {
                  firstName: 'Test',
                  lastName: 'User',
                },
                teamId: 'team-1',
                team: {
                  name: 'Engineering',
                },
              },
            ])
          );
        }
        
        return res(
          ctx.json([
            {
              _id: 'post-1',
              body: 'Test post 1',
              userId: 'user-1',
              createdAt: new Date().toISOString(),
              hashtags: [],
              user: {
                firstName: 'Test',
                lastName: 'User',
              },
            },
          ])
        );
      }),
      
      rest.get('*/api/teams', (req, res, ctx) => {
        return res(
          ctx.json([
            { _id: 'team-1', name: 'Engineering' },
            { _id: 'team-2', name: 'Marketing' },
          ])
        );
      })
    );

    render(
      <AuthProvider>
        <FeedPage />
      </AuthProvider>
    );

    // Wait for initial posts to load
    await waitFor(() => {
      expect(screen.getByText('Test post 1')).toBeInTheDocument();
    });

    // Open filters
    await user.click(screen.getByText(/Filters/i));
    
    // Select a team filter
    const teamSelect = screen.getByLabelText(/Team/i);
    await user.selectOptions(teamSelect, 'team-1');
    
    // Wait for filtered posts to load
    await waitFor(() => {
      expect(screen.getByText('Filtered team post')).toBeInTheDocument();
      expect(screen.getByText('Team: Engineering')).toBeInTheDocument();
    });
    
    // Clear filters
    await user.click(screen.getByText(/Clear all filters/i));
    
    // Wait for unfiltered posts to load again
    await waitFor(() => {
      expect(screen.getByText('Test post 1')).toBeInTheDocument();
    });
  });

  it('handles error state', async () => {
    // Mock API error
    server.use(
      rest.get('*/api/posts/feed', (req, res, ctx) => {
        return res(ctx.status(500), ctx.json({ message: 'Server error' }));
      })
    );

    render(
      <AuthProvider>
        <FeedPage />
      </AuthProvider>
    );

    // Wait for error message to appear
    await waitFor(() => {
      expect(screen.getByText(/Failed to load feed data/i)).toBeInTheDocument();
    });
  });
});
