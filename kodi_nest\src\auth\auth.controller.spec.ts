import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { UnauthorizedException } from '@nestjs/common';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  const mockAuthService = {
    register: jest.fn(),
    login: jest.fn(),
    refreshToken: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('register', () => {
    it('should call authService.register with registerDto', async () => {
      const registerDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      const expectedResult = {
        access_token: 'access-token',
        refresh_token: 'refresh-token',
        user: {
          id: 'user-id',
          email: '<EMAIL>',
        },
      };

      mockAuthService.register.mockResolvedValue(expectedResult);

      const result = await controller.register(registerDto);

      expect(mockAuthService.register).toHaveBeenCalledWith(registerDto);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('login', () => {
    it('should call authService.login with user from request', async () => {
      const req = {
        user: {
          id: 'user-id',
          email: '<EMAIL>',
        },
      };

      const loginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const expectedResult = {
        access_token: 'access-token',
        refresh_token: 'refresh-token',
        user: {
          id: 'user-id',
          email: '<EMAIL>',
        },
      };

      mockAuthService.login.mockResolvedValue(expectedResult);

      const result = await controller.login(req, loginDto);

      expect(mockAuthService.login).toHaveBeenCalledWith(req.user);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('refreshToken', () => {
    it('should call authService.refreshToken with refresh_token', async () => {
      const body = {
        refresh_token: 'refresh-token',
      };

      const expectedResult = {
        access_token: 'new-access-token',
        refresh_token: 'new-refresh-token',
        user: {
          id: 'user-id',
          email: '<EMAIL>',
        },
      };

      mockAuthService.refreshToken.mockResolvedValue(expectedResult);

      const result = await controller.refreshToken(body);

      expect(mockAuthService.refreshToken).toHaveBeenCalledWith('refresh-token');
      expect(result).toEqual(expectedResult);
    });

    it('should throw UnauthorizedException if refresh_token is not provided', async () => {
      const body = {};

      await expect(controller.refreshToken(body as any)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('getProfile', () => {
    it('should return user from request', () => {
      const req = {
        user: {
          userId: 'user-id',
          email: '<EMAIL>',
          roles: ['user'],
        },
      };

      const result = controller.getProfile(req);

      expect(result).toEqual(req.user);
    });
  });
});
