import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Card from '@/src/components/ui/Card';

interface PostType {
  _id: string;
  name: string;
  description?: string;
  recordTips?: string;
  companyId: string;
}

interface Team {
  _id: string;
  name: string;
  companyId: string;
}

export default function PostTypeSelectionScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;

  const [postTypes, setPostTypes] = useState<PostType[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [selectedTeam, setSelectedTeam] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (user?.profile?.companyId) {
      fetchPostTypes(user.profile.companyId);
      fetchTeams(user.profile.companyId);
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchPostTypes = async (companyId: string) => {
    try {
      // The server will now enforce the company ID restriction based on the user's JWT token
      const { data } = await api.get(endpoints.postTypes.base);
      setPostTypes(data);
    } catch (err: any) {
      console.error('Failed to fetch post types:', err);
      setError('Failed to load post types. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchTeams = async (companyId: string) => {
    try {
      // The server will now enforce the company ID restriction based on the user's JWT token
      const { data } = await api.get(endpoints.teams.base);
      setTeams(data);
    } catch (err: any) {
      console.error('Failed to fetch teams:', err);
      // Don't set error here to avoid blocking the UI if only teams fail to load
    }
  };

  const handlePostTypeSelect = (postTypeId: string) => {
    // Navigate to pre-record page with the selected post type and team
    router.push({
      pathname: '/pre-record',
      params: {
        postTypeId,
        teamId: selectedTeam || undefined,
      }
    });
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.foreground }]}>Create a Post</Text>
        <TouchableOpacity onPress={handleCancel}>
          <Text style={[styles.cancelText, { color: colors.primary }]}>Cancel</Text>
        </TouchableOpacity>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {teams.length > 0 && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
              Select a Team (Optional)
            </Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.teamList}
            >
              {teams.map((team) => (
                <TouchableOpacity
                  key={team._id}
                  style={[
                    styles.teamItem,
                    {
                      backgroundColor: selectedTeam === team._id ? colors.primary : colors.secondary,
                      borderColor: colors.border,
                    }
                  ]}
                  onPress={() => setSelectedTeam(selectedTeam === team._id ? '' : team._id)}
                >
                  <Text
                    style={[
                      styles.teamItemText,
                      {
                        color: selectedTeam === team._id
                          ? colors.primaryForeground
                          : colors.secondaryForeground
                      }
                    ]}
                  >
                    {team.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
            Select a Post Type
          </Text>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : error ? (
            <Card style={styles.errorCard}>
              <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
              <TouchableOpacity
                style={[styles.retryButton, { backgroundColor: colors.destructive }]}
                onPress={() => user?.profile?.companyId && fetchPostTypes(user.profile.companyId)}
              >
                <Text style={[styles.retryButtonText, { color: colors.destructiveForeground }]}>
                  Try Again
                </Text>
              </TouchableOpacity>
            </Card>
          ) : (
            <View style={styles.postTypeList}>
              {postTypes.length > 0 ? (
                postTypes.map((postType) => (
                  <TouchableOpacity
                    key={postType._id}
                    style={[styles.postTypeItem, { borderColor: colors.border }]}
                    onPress={() => handlePostTypeSelect(postType._id)}
                  >
                    <Text style={[styles.postTypeTitle, { color: colors.foreground }]}>
                      {postType.name}
                    </Text>
                    {postType.description && (
                      <Text style={[styles.postTypeDescription, { color: colors.mutedForeground }]}>
                        {postType.description.replace(/<[^>]*>/g, '')}
                      </Text>
                    )}
                    <Ionicons
                      name="chevron-forward"
                      size={20}
                      color={colors.mutedForeground}
                      style={styles.postTypeIcon}
                    />
                  </TouchableOpacity>
                ))
              ) : (
                <Card style={styles.emptyCard}>
                  <Text style={[styles.emptyText, { color: colors.mutedForeground }]}>
                    No post types found. Please contact your administrator.
                  </Text>
                </Card>
              )}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  cancelText: {
    fontSize: 16,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  teamList: {
    paddingVertical: 8,
  },
  teamItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
  },
  teamItemText: {
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorCard: {
    padding: 16,
    alignItems: 'center',
  },
  errorText: {
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    fontWeight: '500',
  },
  postTypeList: {
    marginTop: 8,
  },
  postTypeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 8,
  },
  postTypeTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  postTypeDescription: {
    fontSize: 14,
    flex: 3,
    marginRight: 8,
  },
  postTypeIcon: {
    marginLeft: 8,
  },
  emptyCard: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    textAlign: 'center',
  },
});
