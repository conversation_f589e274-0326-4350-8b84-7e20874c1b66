import { IsNotEmpty, IsString, IsOptional, IsObject } from 'class-validator';

export class CreateInsightDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  tag?: string;

  @IsOptional()
  @IsString()
  companyId?: string;

  @IsOptional()
  @IsString()
  touchPointName?: string;

  @IsOptional()
  @IsObject()
  touchPointSource?: any;

  @IsOptional()
  @IsString()
  touchPointText?: string;

  @IsOptional()
  @IsString()
  summary?: string;

  @IsNotEmpty()
  @IsString()
  userId: string;
}
