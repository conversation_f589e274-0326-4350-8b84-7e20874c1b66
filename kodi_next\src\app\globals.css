@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --card-background: #ffffff;
  --card-foreground: #171717;
  --primary: #4f46e5;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #1f2937;
  --muted: #f3f4f6;
  --muted-foreground: #6b7280;
  --accent: #f3f4f6;
  --accent-foreground: #1f2937;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #4f46e5;

  /* Modern UI variables */
  --card-padding: 1rem;
  --content-spacing: 0.75rem;
  --container-margin: 1rem;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card-background: #171717;
  --card-foreground: #ededed;
  --primary: #6366f1;
  --primary-foreground: #ffffff;
  --secondary: #1f2937;
  --secondary-foreground: #f3f4f6;
  --muted: #1f2937;
  --muted-foreground: #9ca3af;
  --accent: #1f2937;
  --accent-foreground: #f3f4f6;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #374151;
  --input: #374151;
  --ring: #6366f1;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans, Arial, Helvetica, sans-serif);
}

/* Modern UI styles */
.card,
.bg-white.rounded-lg,
.bg-white.rounded-md {
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  padding: var(--card-padding);
  background-color: var(--card-background);
  box-shadow: none !important;
}

/* Adjust container margins */
.container {
  padding-right: var(--container-margin);
}

/* Reduce vertical margins */
.py-8 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-6 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1rem;
}

/* Adjust grid spacing */
.gap-6 {
  gap: var(--content-spacing);
}

/* Dark mode styles for common components */
.dark .bg-white {
  background-color: var(--card-background);
}

.dark .text-gray-900 {
  color: var(--card-foreground);
}

.dark .text-gray-700,
.dark .text-gray-800 {
  color: #d1d5db;
}

.dark .text-gray-500,
.dark .text-gray-600 {
  color: #9ca3af;
}

.dark .border-gray-200,
.dark .border-gray-300 {
  border-color: var(--border);
}

/* Remove shadows in dark mode too */
.dark .shadow {
  box-shadow: none !important;
}

.dark .bg-gray-50 {
  background-color: #111827;
}

.dark .hover\:bg-gray-50:hover {
  background-color: #1f2937;
}

.dark .bg-indigo-50 {
  background-color: rgba(99, 102, 241, 0.1);
}

.dark .bg-red-50 {
  background-color: rgba(239, 68, 68, 0.1);
}

.dark .bg-green-50 {
  background-color: rgba(16, 185, 129, 0.1);
}

.dark .text-indigo-600,
.dark .text-indigo-700 {
  color: #818cf8;
}

.dark .text-red-700,
.dark .text-red-800 {
  color: #f87171;
}

.dark .text-green-700,
.dark .text-green-800 {
  color: #34d399;
}

.dark .hover\:text-indigo-800:hover {
  color: #a5b4fc;
}

.dark .hover\:text-gray-700:hover {
  color: #e5e7eb;
}

.dark .hover\:border-gray-300:hover {
  border-color: #4b5563;
}
