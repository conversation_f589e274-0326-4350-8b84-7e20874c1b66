import { Document, Schema as MongooseSchema } from 'mongoose';
export type InsightDocument = Insight & Document;
export declare class Insight {
    title: string;
    tag: string;
    companyId: MongooseSchema.Types.ObjectId;
    touchPointName: string;
    touchPointSource: any;
    touchPointText: string;
    summary: string;
    userId: MongooseSchema.Types.ObjectId;
    comments: MongooseSchema.Types.ObjectId[];
}
export declare const InsightSchema: MongooseSchema<Insight, import("mongoose").Model<Insight, any, any, any, Document<unknown, any, Insight> & Insight & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Insight, Document<unknown, {}, import("mongoose").FlatRecord<Insight>> & import("mongoose").FlatRecord<Insight> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
