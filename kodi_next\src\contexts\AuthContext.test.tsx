import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider, useAuth } from './AuthContext';
import { server } from '@/test-utils/mocks/server';
import { rest } from 'msw';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

// Test component that uses the auth context
const TestComponent = () => {
  const { user, login, logout, isAuthenticated, loading } = useAuth();
  
  return (
    <div>
      {loading ? (
        <p>Loading...</p>
      ) : (
        <>
          <p data-testid="auth-status">
            {isAuthenticated ? 'Authenticated' : 'Not authenticated'}
          </p>
          {user && (
            <div data-testid="user-info">
              <p>{user.email}</p>
              <p>{user.profile?.firstName || 'No name'}</p>
            </div>
          )}
          <button onClick={() => login('<EMAIL>', 'password')}>
            Login
          </button>
          <button onClick={logout}>Logout</button>
        </>
      )}
    </div>
  );
};

describe('AuthContext', () => {
  beforeAll(() => server.listen());
  afterEach(() => {
    server.resetHandlers();
    localStorage.clear();
    jest.clearAllMocks();
  });
  afterAll(() => server.close());

  it('should provide authentication state and methods', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Initially should be loading
    expect(screen.getByText('Loading...')).toBeInTheDocument();

    // After loading completes
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not authenticated');
    });

    // Test login functionality
    const user = userEvent.setup();
    await user.click(screen.getByText('Login'));

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
      expect(screen.getByTestId('user-info')).toBeInTheDocument();
    });

    // Test logout functionality
    await user.click(screen.getByText('Logout'));
    
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not authenticated');
    });
  });

  it('should handle login errors', async () => {
    // Mock a failed login
    server.use(
      rest.post('*/api/auth/login', (req, res, ctx) => {
        return res(
          ctx.status(401),
          ctx.json({ message: 'Invalid credentials' })
        );
      })
    );

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not authenticated');
    });

    // Attempt login that will fail
    const user = userEvent.setup();
    await user.click(screen.getByText('Login'));

    // Should still be unauthenticated
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not authenticated');
    });
  });
});
