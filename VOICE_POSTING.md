# Voice-Based Posting Feature

This document describes the voice-based posting feature implemented in the Kodi application.

## Overview

The key thought behind this system is that posting is done mostly by speaking. The createPost dialog is configured to record a post and offer typing as a backup. The spoken post audio is channeled through Speech-to-Text (STT) and the post is then sent to the AI engine for analysis. The analysis is defined by the post type.

## Components

### Backend (Nest.js)

1. **Audio Upload Endpoint**
   - Located at `POST /api/uploads/audio-upload`
   - <PERSON>les file uploads using Multer
   - Stores audio files in the `uploads/audio` directory
   - Returns the URL to the uploaded file

2. **Transcription Endpoint**
   - Located at `POST /api/ai/transcribe`
   - Takes an audio URL and optional post type ID
   - Transcribes the audio to text
   - Uses the post type to guide the transcription if provided

3. **Post Schema**
   - Includes an `audioUrl` field to store the URL to the audio recording
   - Allows posts to be created with both text content and audio recordings

4. **Post Types**
   - Include a `recordTips` field with guidance for recording specific types of posts
   - Pre-populated for new organizations with various post types

### Frontend (Next.js)

1. **AudioRecorder Component**
   - Provides UI for recording, playing back, and submitting audio
   - Handles microphone access and recording
   - Uploads audio to the server
   - Requests transcription from the server
   - Passes the transcription to the parent component

2. **CreatePostForm Component**
   - Integrates the AudioRecorder component
   - Shows recording tips based on the selected post type
   - Allows editing of the transcribed text
   - Submits both the text and audio URL to the server

## Default Post Types

New organizations are pre-populated with the following post types, each with specific recording tips:

1. 🌎 Strategic feedback
2. 🔦 Product QA
3. 📣 Shoutouts
4. 🗓️ Agenda Generator
5. 🔄 Discoveries & demos
6. 🌤️ Weekend Recap
7. 🗓️ Monthly Review
8. 🚀 Objective MVP Launch
9. ⏱️ 30 Second Pitch
10. 👥 Daily Standup
11. 🎬 Weekly Staff Update
12. ⭐ Priority Visibility
13. 📅 Daily Wrap Up
14. 💭 Product Opinions

## Usage Flow

1. User selects a post type (optional)
2. User clicks the "Record Audio" button
3. User speaks their post
4. User stops the recording
5. User can play back the recording
6. User clicks "Use Recording" to submit the audio
7. The audio is uploaded to the server
8. The server transcribes the audio
9. The transcription is displayed in the text area
10. User can edit the transcription if needed
11. User submits the post
12. The post is created with both the text and audio URL

## Technical Implementation

- Audio is recorded using the Web Audio API
- Audio is stored as WebM files
- Transcription is currently simulated but can be replaced with a real STT service like OpenAI Whisper
- The system is designed to be extensible for future AI analysis of posts

## Future Enhancements

- Integration with a production-grade STT service
- AI analysis of posts based on post type
- Sentiment analysis of posts
- Automatic tagging of posts based on content
- Voice commands for post creation
