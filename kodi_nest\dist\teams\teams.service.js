"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeamsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const team_schema_1 = require("./schemas/team.schema");
const membership_schema_1 = require("./schemas/membership.schema");
let TeamsService = class TeamsService {
    teamModel;
    membershipModel;
    constructor(teamModel, membershipModel) {
        this.teamModel = teamModel;
        this.membershipModel = membershipModel;
    }
    async create(createTeamDto) {
        const newTeam = new this.teamModel(createTeamDto);
        const team = await newTeam.save();
        await this.addMember({
            teamId: team._id.toString(),
            memberId: createTeamDto.userId,
        });
        return team;
    }
    async findAll() {
        return this.teamModel.find().exec();
    }
    async findByCompany(companyId) {
        return this.teamModel.find({ companyId }).exec();
    }
    async findById(id) {
        const team = await this.teamModel.findById(id).exec();
        if (!team) {
            throw new common_1.NotFoundException(`Team with ID ${id} not found`);
        }
        return team;
    }
    async update(id, updateTeamDto) {
        const updatedTeam = await this.teamModel
            .findByIdAndUpdate(id, updateTeamDto, { new: true })
            .exec();
        if (!updatedTeam) {
            throw new common_1.NotFoundException(`Team with ID ${id} not found`);
        }
        return updatedTeam;
    }
    async remove(id) {
        await this.membershipModel.deleteMany({ teamId: id }).exec();
        const deletedTeam = await this.teamModel.findByIdAndDelete(id).exec();
        if (!deletedTeam) {
            throw new common_1.NotFoundException(`Team with ID ${id} not found`);
        }
        return deletedTeam;
    }
    async getTeamMembers(teamId) {
        return this.membershipModel.find({ teamId }).populate('memberId').exec();
    }
    async getUserTeams(userId, companyId) {
        const memberships = await this.membershipModel
            .find({ memberId: userId })
            .exec();
        const teamIds = memberships.map((membership) => membership.teamId);
        const query = { _id: { $in: teamIds } };
        if (companyId) {
            query.companyId = companyId;
        }
        return this.teamModel.find(query).exec();
    }
    async addMember(createMembershipDto) {
        const team = await this.teamModel
            .findById(createMembershipDto.teamId)
            .exec();
        if (!team) {
            throw new common_1.NotFoundException(`Team with ID ${createMembershipDto.teamId} not found`);
        }
        const existingMembership = await this.membershipModel
            .findOne({
            teamId: createMembershipDto.teamId,
            memberId: createMembershipDto.memberId,
        })
            .exec();
        if (existingMembership) {
            throw new common_1.BadRequestException('User is already a member of this team');
        }
        const newMembership = new this.membershipModel(createMembershipDto);
        return newMembership.save();
    }
    async removeMember(teamId, memberId) {
        const result = await this.membershipModel
            .deleteOne({
            teamId,
            memberId,
        })
            .exec();
        if (result.deletedCount === 0) {
            throw new common_1.NotFoundException(`Membership not found for team ${teamId} and member ${memberId}`);
        }
    }
    async isMember(teamId, userId) {
        const membership = await this.membershipModel
            .findOne({
            teamId,
            memberId: userId,
        })
            .exec();
        return !!membership;
    }
};
exports.TeamsService = TeamsService;
exports.TeamsService = TeamsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(team_schema_1.Team.name)),
    __param(1, (0, mongoose_1.InjectModel)(membership_schema_1.Membership.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], TeamsService);
//# sourceMappingURL=teams.service.js.map