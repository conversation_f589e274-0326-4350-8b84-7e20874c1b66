import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Card from '@/src/components/ui/Card';
import Button from '@/src/components/ui/Button';

interface PostType {
  _id: string;
  name: string;
  description?: string;
  recordTips?: string;
  companyId: string;
}

interface Team {
  _id: string;
  name: string;
  companyId: string;
}

export default function PreRecordScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  const params = useLocalSearchParams<{ postTypeId: string; teamId?: string }>();
  
  const postTypeId = params.postTypeId;
  const teamId = params.teamId;
  
  const [postType, setPostType] = useState<PostType | null>(null);
  const [team, setTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!postTypeId) {
      router.replace('/post-type-selection');
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch post type details
        const { data: postTypeData } = await api.get(endpoints.postTypes.byId(postTypeId));
        setPostType(postTypeData);
        
        // Fetch team details if teamId is provided
        if (teamId) {
          const { data: teamData } = await api.get(endpoints.teams.byId(teamId));
          setTeam(teamData);
        }
      } catch (err: any) {
        console.error('Failed to fetch data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [postTypeId, teamId]);

  const handleStartRecording = () => {
    // Navigate to the post creation screen with the selected post type and team
    router.push({
      pathname: '/(tabs)/post',
      params: {
        postTypeId,
        teamId: teamId || undefined,
        fromPreRecord: 'true'
      }
    });
  };

  const handleBack = () => {
    router.back();
  };

  const handleCancel = () => {
    router.replace('/(tabs)');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.foreground }]}>Ready to Record</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity onPress={handleBack} style={styles.headerButton}>
            <Text style={[styles.headerButtonText, { color: colors.primary }]}>Back</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleCancel} style={styles.headerButton}>
            <Text style={[styles.headerButtonText, { color: colors.mutedForeground }]}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : error ? (
          <Card style={styles.errorCard}>
            <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: colors.destructive }]}
              onPress={handleBack}
            >
              <Text style={[styles.retryButtonText, { color: colors.destructiveForeground }]}>
                Go Back
              </Text>
            </TouchableOpacity>
          </Card>
        ) : (
          <Card style={styles.contentCard}>
            {team && (
              <View style={[styles.teamBadge, { backgroundColor: colors.primary + '20' }]}>
                <Text style={[styles.teamBadgeText, { color: colors.primary }]}>
                  Team: {team.name}
                </Text>
              </View>
            )}
            
            <Text style={[styles.postTypeTitle, { color: colors.foreground }]}>
              {postType?.name}
            </Text>
            
            {postType?.description && (
              <Text style={[styles.description, { color: colors.mutedForeground }]}>
                {postType.description.replace(/<[^>]*>/g, '')}
              </Text>
            )}
            
            {postType?.recordTips && (
              <View style={styles.tipsContainer}>
                <Text style={[styles.tipsTitle, { color: colors.foreground }]}>
                  Recording Tips:
                </Text>
                <View style={[styles.tipsCard, { backgroundColor: colors.secondary }]}>
                  <Text style={[styles.tipsText, { color: colors.secondaryForeground }]}>
                    {postType.recordTips.replace(/<[^>]*>/g, '')}
                  </Text>
                </View>
              </View>
            )}
            
            <View style={styles.buttonContainer}>
              <Button
                title="Start Recording"
                onPress={handleStartRecording}
                leftIcon={<Ionicons name="mic" size={20} color={colors.primaryForeground} />}
                style={styles.startButton}
              />
            </View>
          </Card>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    marginLeft: 16,
  },
  headerButtonText: {
    fontSize: 16,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorCard: {
    padding: 16,
    alignItems: 'center',
  },
  errorText: {
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    fontWeight: '500',
  },
  contentCard: {
    padding: 16,
  },
  teamBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 16,
  },
  teamBadgeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  postTypeTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    marginBottom: 24,
    lineHeight: 22,
  },
  tipsContainer: {
    marginBottom: 24,
  },
  tipsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  tipsCard: {
    padding: 16,
    borderRadius: 8,
  },
  tipsText: {
    fontSize: 16,
    lineHeight: 22,
  },
  buttonContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  startButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
});
