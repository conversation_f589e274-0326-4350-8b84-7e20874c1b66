'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';

interface OnboardingStep {
  title: string;
  description: string;
  action?: string;
  link?: string;
}

export default function OnboardingGuide() {
  const { user, updateUserSettings } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [dismissed, setDismissed] = useState(false);

  const steps: OnboardingStep[] = [
    {
      title: 'Welcome to Kodi!',
      description: '<PERSON><PERSON> helps your team communicate through voice-based posts with AI-powered insights.',
      action: 'Next',
    },
    {
      title: 'Create Your First Post',
      description: 'Click the POST button in the navigation bar to create your first post. You can record audio or type directly.',
      action: 'Try it',
      link: '/post',
    },
    {
      title: 'Explore Your Feed',
      description: 'Your feed shows posts from your team. Use filters to find specific content.',
      action: 'Next',
    },
    {
      title: 'Invite Your Team',
      description: 'Go to Settings > Company to find your invite link and share it with your team.',
      action: 'Go to Settings',
      link: '/settings/company',
    },
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleDismiss();
    }
  };

  const handleDismiss = async () => {
    setDismissed(true);
    try {
      await updateUserSettings({
        showOnboarder: false,
      });
    } catch (err) {
      console.error('Failed to update onboarding settings:', err);
    }
  };

  if (dismissed || user?.profile?.userSettings?.showOnboarder === false) {
    return null;
  }

  const currentStepData = steps[currentStep];

  return (
    <div className="fixed bottom-3 right-3 bg-white rounded-md border border-border p-3 w-80 z-50">
      <button
        onClick={handleDismiss}
        className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
        aria-label="Close"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>

      <div className="mb-3">
        <h3 className="text-lg font-semibold text-gray-900">{currentStepData.title}</h3>
        <p className="text-sm text-gray-600 mt-1">{currentStepData.description}</p>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex space-x-1">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`h-1 w-5 rounded-full ${
                index === currentStep ? 'bg-primary' : 'bg-gray-200'
              }`}
            />
          ))}
        </div>

        <div className="flex space-x-2">
          {currentStep > 0 && (
            <button
              onClick={() => setCurrentStep(currentStep - 1)}
              className="px-2 py-1 text-sm text-gray-600 hover:text-gray-900"
            >
              Back
            </button>
          )}

          {currentStepData.link ? (
            <a
              href={currentStepData.link}
              className="px-3 py-1 bg-primary text-white text-sm rounded hover:bg-opacity-90"
            >
              {currentStepData.action}
            </a>
          ) : (
            <button
              onClick={handleNext}
              className="px-3 py-1 bg-primary text-white text-sm rounded hover:bg-opacity-90"
            >
              {currentStepData.action}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
