import { Injectable, NotFoundException, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ObjectId } from 'mongoose';
import { Company, CompanyDocument } from './schemas/company.schema';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import * as crypto from 'crypto';
import { ModuleRef } from '@nestjs/core';

@Injectable()
export class CompaniesService implements OnModuleInit {
  private postsService: any;

  constructor(
    @InjectModel(Company.name) private companyModel: Model<CompanyDocument>,
    private moduleRef: ModuleRef
  ) {}

  onModuleInit() {
    // We use moduleRef to avoid circular dependency
    try {
      // Import the PostsService class dynamically to avoid circular dependency
      const { PostsService } = require('../posts/posts.service');
      this.postsService = this.moduleRef.get(PostsService, { strict: false });
    } catch (error) {
      console.warn('Could not load PostsService:', error.message);
    }
  }

  async create(createCompanyDto: CreateCompanyDto): Promise<CompanyDocument> {
    // Generate invite code if not provided
    if (!createCompanyDto.invite_code) {
      createCompanyDto.invite_code = this.generateInviteCode();
    }

    const newCompany = new this.companyModel(createCompanyDto);
    const company = await newCompany.save();

    // Create default post types if postsService is available
    if (this.postsService) {
      await this.createDefaultPostTypes((company._id as ObjectId).toString(), createCompanyDto.createdBy);
    }

    return company;
  }

  async createDefaultPostTypes(companyId: string, userId: string): Promise<void> {
    const defaultPostTypes = [
      {
        name: "🌎 Strategic feedback",
        description: "<div>Strategic feedback</div>",
        recordTips: "<div>What's working?<br>What's not working?<br>What are the opportunities?</div>",
        archived: false
      },
      {
        name: "🔦 Product QA",
        description: "<div>Async QA on the product</div>",
        recordTips: "<div>What have you found to fix?<br>What is odd?<br>What needs to be improved?</div>",
        archived: false,
      },
      {
        name: "📣 Shoutouts",
        description: "<div>Highlight who has been crushing it lately</div>",
        recordTips: "<div>Who has been crushing it lately?</div>",
        archived: false,
      },
      {
        name: "🗓️ Agenda Generator",
        description: "<div>Turn inputs into an agenda.&nbsp;</div>",
        recordTips: "<div>The purpose here is to have everyone take 1-2 minutes and provide input based on the suggested topics below, which we'll then turn into our agenda.&nbsp;</div><div><br>📰 <strong>News</strong> - Any updates, highlights, or noteworthy FYI announcements?</div><div>🛑 <strong>Problems</strong> - Any problems or issues to be addressed or resolved?</div><div>🚧 <strong>Blockers</strong> - Any hindrance due to an external dependency?</div><div>⚖️ <strong>Decisions</strong> - Any outstanding decisions to be made?</div><div>👥 <strong>Collaboration</strong> - Any review or feedback on anything related to dev or design?<br>🌐 <strong>Misc</strong> - Any additional items to bring up not covered by these topics?</div>",
        archived: false,
      },
      {
        name: "🔄 Discoveries & demos",
        recordTips: "<div>What are the problems/pains?<br>How are they solving today?<br>What is timeline? Budget? Authority?</div>",
        description: "<div>Share insight feedback from discovery calls and demos.</div>",
        archived: false
      },
      {
        name: "🌤️ Weekend Recap",
        description: "<div>Share your weekend</div>",
        recordTips: "<div>How was the weekend?<br>What did you do?<br>Where did you go?<br><br>#weekend #backtowork&nbsp;</div>",
        archived: false
      },
      {
        name: "🗓️ Monthly Review",
        archived: false,
        description: "<div>Reflect back on the prior month</div>",
        recordTips: "<div>What did you achieve?<br>What problems did you overcome?<br>What will you do this next month?</div>"
      },
      {
        name: "🚀 Objective MVP Launch",
        description: "<div>Q1-2024 project</div>",
        recordTips: "<div>What's the latest with this Objective?</div>",
        archived: false
      },
      {
        name: "⏱️ 30 Second Pitch",
        description: "<div>Share a quick pitch</div>",
        recordTips: "<ul><li>What problem are you solving?</li><li>What's the solution?</li><li>How is it different?</li><li>What's the vision?</li><li>Why now?</li></ul>",
        archived: false,
      },
      {
        name: "👥 Daily Standup",
        archived: false,
        description: "<div>Share what's happening today</div>",
        recordTips: "<div>What's your current priority?<br>What did you do yesterday?<br>Any blockers?</div>"
      },
      {
        name: "🎬 Weekly Staff Update",
        archived: false,
        description: "<div>Wrap up the week that was</div>",
        recordTips: "<div>What are the 1-2 highlights you want to share?<br>What are 1-2 lowlights you want to share?<br>What else do you want to share with the team?</div>"
      },
      {
        name: "⭐ Priority Visibility",
        description: "<div>Share your priorities</div>",
        recordTips: "<div>What are the top priorities you're working on?</div>",
        archived: false
      },
      {
        name: "📅 Daily Wrap Up",
        description: "<div>Retrospective on the day that was</div>",
        recordTips: "<div>How was your day?<br>What was accomplished?<br>What's on deck for tomorrow?<br>Any blockers or help needed?</div><div><br></div>",
        archived: false
      },
      {
        name: "💭 Product Opinions",
        description: "<div>Share async internal product feedback</div>",
        recordTips: "<ul><li>Share your opinions on the product.</li><li>Opinions are not requests, but rather informal feedback.</li><li>Provide a mix of likes, dislikes, and ideas.</li><li><br></li></ul><div><br></div>",
        archived: false
      }
    ];

    // Create each post type
    for (const postType of defaultPostTypes) {
      await this.postsService.createPostType({
        ...postType,
        companyId,
        userId
      });
    }
  }

  async findAll(): Promise<CompanyDocument[]> {
    return this.companyModel.find().exec();
  }

  async findById(id: string): Promise<CompanyDocument> {
    const company = await this.companyModel.findById(id).exec();
    if (!company) {
      throw new NotFoundException(`Company with ID ${id} not found`);
    }
    return company;
  }

  async findByInviteCode(inviteCode: string): Promise<CompanyDocument | null> {
    return this.companyModel.findOne({ invite_code: inviteCode }).exec();
  }

  async update(id: string, updateCompanyDto: UpdateCompanyDto): Promise<CompanyDocument> {
    const updatedCompany = await this.companyModel
      .findByIdAndUpdate(id, updateCompanyDto, { new: true })
      .exec();

    if (!updatedCompany) {
      throw new NotFoundException(`Company with ID ${id} not found`);
    }

    return updatedCompany;
  }

  async remove(id: string): Promise<CompanyDocument> {
    const deletedCompany = await this.companyModel.findByIdAndDelete(id).exec();

    if (!deletedCompany) {
      throw new NotFoundException(`Company with ID ${id} not found`);
    }

    return deletedCompany;
  }

  private generateInviteCode(): string {
    // Generate a random 8-character alphanumeric string
    return crypto.randomBytes(4).toString('hex');
  }
}

