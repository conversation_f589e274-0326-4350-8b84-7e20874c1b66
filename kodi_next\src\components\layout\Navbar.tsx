'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import ThemeToggle from './ThemeToggle';

export default function Navbar() {
  const { user, logout } = useAuth();
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const pathname = usePathname();

  const isActive = (path: string) => {
    return pathname === path;
  };

  return (
    <nav className="bg-white dark:bg-gray-900 shadow dark:shadow-gray-800">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 justify-between">
          <div className="flex">
            <div className="flex flex-shrink-0 items-center">
              <Link href="/feed">
                <img
                  className="h-8 w-auto"
                  src="/logo.svg"
                  alt="Kodi"
                />
              </Link>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              <Link
                href="/feed"
                className={`inline-flex items-center px-1 pt-1 text-sm font-medium ${
                  isActive('/feed')
                    ? 'border-b-2 border-indigo-500 text-gray-900'
                    : 'border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                }`}
              >
                Feed
              </Link>
              <Link
                href="/drafts"
                className={`inline-flex items-center px-1 pt-1 text-sm font-medium ${
                  isActive('/drafts')
                    ? 'border-b-2 border-indigo-500 text-gray-900'
                    : 'border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                }`}
              >
                Drafts
              </Link>
              <Link
                href="/analysis"
                className={`inline-flex items-center px-1 pt-1 text-sm font-medium ${
                  isActive('/analysis')
                    ? 'border-b-2 border-indigo-500 text-gray-900'
                    : 'border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                }`}
              >
                Analysis
              </Link>
              <Link
                href="/chat"
                className={`inline-flex items-center px-1 pt-1 text-sm font-medium ${
                  isActive('/chat')
                    ? 'border-b-2 border-indigo-500 text-gray-900'
                    : 'border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                }`}
              >
                Chat
              </Link>
            </div>

            {/* Post Button */}
            <div className="hidden sm:ml-6 sm:flex">
              <Link
                href="/post/type-selection"
                className="inline-flex items-center my-2 px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-amber-300 hover:bg-amber-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
              >
                <img
                  className="h-6 w-auto mr-2"
                  src="/logo.svg"
                  alt="Kodi"
                />
                POST
              </Link>
            </div>
          </div>
          <div className="hidden sm:ml-6 sm:flex sm:items-center">
            <ThemeToggle />

            {/* Profile dropdown */}
            <div className="relative ml-3">
              <div>
                <button
                  type="button"
                  className="flex rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  id="user-menu-button"
                  aria-expanded={isProfileMenuOpen}
                  aria-haspopup="true"
                  onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                >
                  <span className="sr-only">Open user menu</span>
                  <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                    {user?.profile?.avatar ? (
                      <img
                        src={user.profile.avatar}
                        alt={`${user.profile.firstName} ${user.profile.lastName}`}
                        className="h-8 w-8 object-cover"
                      />
                    ) : (
                      <span className="text-gray-500 text-sm font-medium">
                        {user?.profile?.firstName?.[0] || ''}
                        {user?.profile?.lastName?.[0] || ''}
                      </span>
                    )}
                  </div>
                </button>
              </div>

              {isProfileMenuOpen && (
                <div
                  className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="user-menu-button"
                  tabIndex={-1}
                >
                  <Link
                    href="/profile"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    role="menuitem"
                    tabIndex={-1}
                    id="user-menu-item-0"
                    onClick={() => setIsProfileMenuOpen(false)}
                  >
                    Your Profile
                  </Link>
                  <Link
                    href="/settings"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    role="menuitem"
                    tabIndex={-1}
                    id="user-menu-item-1"
                    onClick={() => setIsProfileMenuOpen(false)}
                  >
                    Settings
                  </Link>
                  <button
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    role="menuitem"
                    tabIndex={-1}
                    id="user-menu-item-2"
                    onClick={() => {
                      setIsProfileMenuOpen(false);
                      logout();
                    }}
                  >
                    Sign out
                  </button>
                </div>
              )}
            </div>
          </div>
          <div className="-mr-2 flex items-center sm:hidden">
            {/* Mobile menu button */}
            <button
              type="button"
              className="inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
              aria-controls="mobile-menu"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              <svg
                className="block h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state. */}
      <div className="sm:hidden" id="mobile-menu">
        <div className="space-y-1 pt-2 pb-3">
          <Link
            href="/post/type-selection"
            className="flex items-center justify-center mt-3 py-2 px-4 mx-3 mb-3 text-base font-medium text-white bg-amber-300 hover:bg-amber-400 rounded-md"
          >
            <img
              className="h-8 w-auto mr-2"
              src="/logo.svg"
              alt="Kodi"
            />
            POST
          </Link>
          <Link
            href="/feed"
            className={`block border-l-4 py-2 pl-3 pr-4 text-base font-medium ${
              isActive('/feed')
                ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                : 'border-transparent text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800'
            }`}
          >
            Feed
          </Link>
          <Link
            href="/drafts"
            className={`block border-l-4 py-2 pl-3 pr-4 text-base font-medium ${
              isActive('/drafts')
                ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                : 'border-transparent text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800'
            }`}
          >
            Drafts
          </Link>
          <Link
            href="/analysis"
            className={`block border-l-4 py-2 pl-3 pr-4 text-base font-medium ${
              isActive('/analysis')
                ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                : 'border-transparent text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800'
            }`}
          >
            Analysis
          </Link>
          <Link
            href="/chat"
            className={`block border-l-4 py-2 pl-3 pr-4 text-base font-medium ${
              isActive('/chat')
                ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                : 'border-transparent text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800'
            }`}
          >
            Chat
          </Link>
        </div>
        <div className="border-t border-gray-200 pt-4 pb-3">
          <div className="flex items-center px-4">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                {user?.profile?.avatar ? (
                  <img
                    src={user.profile.avatar}
                    alt={`${user.profile.firstName} ${user.profile.lastName}`}
                    className="h-10 w-10 object-cover"
                  />
                ) : (
                  <span className="text-gray-500 text-sm font-medium">
                    {user?.profile?.firstName?.[0] || ''}
                    {user?.profile?.lastName?.[0] || ''}
                  </span>
                )}
              </div>
            </div>
            <div className="ml-3">
              <div className="text-base font-medium text-gray-800">
                {user?.profile?.firstName} {user?.profile?.lastName}
              </div>
              <div className="text-sm font-medium text-gray-500">{user?.email}</div>
            </div>
            <div className="ml-auto">
              <ThemeToggle />
            </div>
          </div>
          <div className="mt-3 space-y-1">
            <Link
              href="/profile"
              className="block px-4 py-2 text-base font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-800"
            >
              Your Profile
            </Link>
            <Link
              href="/settings"
              className="block px-4 py-2 text-base font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-800"
            >
              Settings
            </Link>
            <button
              onClick={logout}
              className="block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-800"
            >
              Sign out
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
}
