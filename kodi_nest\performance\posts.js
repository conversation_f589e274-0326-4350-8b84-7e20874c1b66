import http from 'k6/http';
import { check, sleep } from 'k6';
import { Counter } from 'k6/metrics';
import { config } from './config.js';

// Custom metrics
const postCreateCounter = new Counter('post_create_success');
const postReadCounter = new Counter('post_read_success');

// Test configuration
export const options = {
  stages: config.stages.posts,
  thresholds: {
    http_req_duration: config.thresholds.http_req_duration,
    'post_create_success': config.thresholds.post_create_success,
    'post_read_success': config.thresholds.post_read_success,
  },
};

// Shared token and user ID
let token;
let userId;
let companyId;

// Setup function - runs once per VU
export function setup() {
  // Login to get a token
  const loginPayload = JSON.stringify({
    email: config.testUser.email,
    password: config.testUser.password,
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const loginRes = http.post(`${config.apiBaseUrl}/auth/login`, loginPayload, params);

  if (loginRes.status === 201) {
    const loginData = JSON.parse(loginRes.body);
    token = loginData.access_token;
    userId = loginData.user.id;

    // Get user profile to get company ID
    const authParams = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    };

    const profileRes = http.get(`${config.apiBaseUrl}/auth/me`, authParams);

    if (profileRes.status === 200) {
      const profileData = JSON.parse(profileRes.body);
      companyId = profileData.profile.companyId;
    }

    return { token, userId, companyId };
  }

  return null;
}

// Test scenario
export default function(data) {
  if (!data || !data.token) {
    console.log('Setup failed, skipping test');
    return;
  }

  const { token, userId, companyId } = data;

  const authParams = {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  };

  // Create a post
  const postPayload = JSON.stringify({
    body: `Performance test post #${__VU}-${__ITER} created at ${new Date().toISOString()}`,
    userId: userId,
    companyId: companyId,
    isDraft: false,
  });

  const createRes = http.post(`${config.apiBaseUrl}/posts`, postPayload, authParams);

  const createSuccess = check(createRes, {
    'create post status is 201': (r) => r.status === 201,
    'created post has ID': (r) => JSON.parse(r.body)._id !== undefined,
  });

  if (createSuccess) {
    postCreateCounter.add(1);

    // Get the created post ID
    const postId = JSON.parse(createRes.body)._id;

    // Get the post by ID
    const getPostRes = http.get(`${config.apiBaseUrl}/posts/${postId}`, authParams);

    const getSuccess = check(getPostRes, {
      'get post status is 200': (r) => r.status === 200,
      'get post returns correct ID': (r) => JSON.parse(r.body)._id === postId,
    });

    if (getSuccess) {
      postReadCounter.add(1);
    }
  }

  // Get posts feed
  const feedRes = http.get(`${config.apiBaseUrl}/posts/feed`, authParams);

  const feedSuccess = check(feedRes, {
    'feed status is 200': (r) => r.status === 200,
    'feed is an array': (r) => Array.isArray(JSON.parse(r.body)),
  });

  if (feedSuccess) {
    postReadCounter.add(1);
  }

  // Sleep between iterations
  sleep(1);
}
