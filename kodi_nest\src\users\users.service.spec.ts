import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { getModelToken } from '@nestjs/mongoose';
import { User } from './schemas/user.schema';
import { NotFoundException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('UsersService', () => {
  let service: UsersService;
  let userModel: Model<User>;

  const mockUserModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userModel = module.get<Model<User>>(getModelToken(User.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new user', async () => {
      const createUserDto = {
        email: '<EMAIL>',
        password: 'hashedPassword',
        roles: ['user'],
      };

      const savedUser = {
        _id: 'user1',
        ...createUserDto,
      };

      // Mock the model constructor and save
      mockUserModel.new = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedUser),
      }));

      const result = await service.create(createUserDto);

      expect(result).toEqual(savedUser);
    });
  });

  describe('findAll', () => {
    it('should return all users', async () => {
      const mockUsers = [
        { _id: 'user1', email: '<EMAIL>' },
        { _id: 'user2', email: '<EMAIL>' },
      ];

      mockUserModel.find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockUsers),
      });

      const result = await service.findAll();

      expect(mockUserModel.find).toHaveBeenCalled();
      expect(result).toEqual(mockUsers);
    });
  });

  describe('findById', () => {
    it('should return a user when it exists', async () => {
      const mockUser = { 
        _id: 'user1', 
        email: '<EMAIL>',
        profile: {
          firstName: 'Test',
          lastName: 'User',
        }
      };

      mockUserModel.findById.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockUser),
        }),
      });

      const result = await service.findById('user1');

      expect(mockUserModel.findById).toHaveBeenCalledWith('user1');
      expect(result).toEqual(mockUser);
    });

    it('should throw NotFoundException when user does not exist', async () => {
      mockUserModel.findById.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(null),
        }),
      });

      await expect(service.findById('nonexistent')).rejects.toThrow(NotFoundException);
      expect(mockUserModel.findById).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('findByEmail', () => {
    it('should return a user when email exists', async () => {
      const mockUser = { _id: 'user1', email: '<EMAIL>' };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockUser),
      });

      const result = await service.findByEmail('<EMAIL>');

      expect(mockUserModel.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(result).toEqual(mockUser);
    });

    it('should return null when email does not exist', async () => {
      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      const result = await service.findByEmail('<EMAIL>');

      expect(mockUserModel.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update a user when it exists', async () => {
      const updateUserDto = {
        firstName: 'Updated',
        lastName: 'User',
      };

      const updatedUser = {
        _id: 'user1',
        email: '<EMAIL>',
        ...updateUserDto,
      };

      mockUserModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedUser),
      });

      const result = await service.update('user1', updateUserDto);

      expect(mockUserModel.findByIdAndUpdate).toHaveBeenCalledWith('user1', updateUserDto, { new: true });
      expect(result).toEqual(updatedUser);
    });

    it('should throw NotFoundException when user does not exist', async () => {
      mockUserModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.update('nonexistent', { firstName: 'Updated' })).rejects.toThrow(NotFoundException);
      expect(mockUserModel.findByIdAndUpdate).toHaveBeenCalledWith('nonexistent', { firstName: 'Updated' }, { new: true });
    });
  });

  describe('remove', () => {
    it('should remove a user when it exists', async () => {
      const deletedUser = { _id: 'user1', email: '<EMAIL>' };

      mockUserModel.findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(deletedUser),
      });

      const result = await service.remove('user1');

      expect(mockUserModel.findByIdAndDelete).toHaveBeenCalledWith('user1');
      expect(result).toEqual(deletedUser);
    });

    it('should throw NotFoundException when user does not exist', async () => {
      mockUserModel.findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.remove('nonexistent')).rejects.toThrow(NotFoundException);
      expect(mockUserModel.findByIdAndDelete).toHaveBeenCalledWith('nonexistent');
    });
  });
});
