import { StyleSheet } from 'react-native';

export const Colors = {
  light: {
    background: '#ffffff',
    foreground: '#171717',
    card: '#ffffff',
    cardForeground: '#171717',
    primary: '#4f46e5',
    primaryForeground: '#ffffff',
    secondary: '#f3f4f6',
    secondaryForeground: '#1f2937',
    muted: '#f3f4f6',
    mutedForeground: '#6b7280',
    accent: '#f3f4f6',
    accentForeground: '#1f2937',
    destructive: '#ef4444',
    destructiveForeground: '#ffffff',
    success: '#22c55e',
    border: '#e5e7eb',
    input: '#e5e7eb',
    ring: '#4f46e5',
    tint: '#4f46e5',
    amber: '#fcd34d',
  },
  dark: {
    background: '#171717',
    foreground: '#ffffff',
    card: '#262626',
    cardForeground: '#ffffff',
    primary: '#6366f1',
    primaryForeground: '#ffffff',
    secondary: '#1f2937',
    secondaryForeground: '#f3f4f6',
    muted: '#262626',
    mutedForeground: '#a1a1aa',
    accent: '#1f2937',
    accentForeground: '#f3f4f6',
    destructive: '#ef4444',
    destructiveForeground: '#ffffff',
    success: '#22c55e',
    border: '#404040',
    input: '#404040',
    ring: '#6366f1',
    tint: '#6366f1',
    amber: '#fcd34d',
  },
};

export const createThemedStyles = (isDarkMode: boolean) => {
  const colors = isDarkMode ? Colors.dark : Colors.light;

  return StyleSheet.create({
    // Layout
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeArea: {
      flex: 1,
      backgroundColor: colors.background,
    },

    // Text
    text: {
      color: colors.foreground,
      fontSize: 16,
    },
    title: {
      color: colors.foreground,
      fontSize: 24,
      fontWeight: 'bold',
    },
    subtitle: {
      color: colors.foreground,
      fontSize: 18,
      fontWeight: '600',
    },
    label: {
      color: colors.mutedForeground,
      fontSize: 14,
    },

    // Cards
    card: {
      backgroundColor: colors.card,
      borderRadius: 8,
      padding: 16,
      marginVertical: 8,
      borderWidth: 1,
      borderColor: colors.border,
    },

    // Inputs
    input: {
      backgroundColor: colors.input,
      borderRadius: 8,
      padding: 12,
      color: colors.foreground,
      borderWidth: 1,
      borderColor: colors.border,
    },

    // Buttons
    buttonPrimary: {
      backgroundColor: colors.primary,
      borderRadius: 8,
      padding: 12,
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonSecondary: {
      backgroundColor: colors.secondary,
      borderRadius: 8,
      padding: 12,
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonOutline: {
      backgroundColor: 'transparent',
      borderRadius: 8,
      padding: 12,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: colors.border,
    },
    buttonText: {
      color: colors.primaryForeground,
      fontWeight: '600',
    },
    buttonTextSecondary: {
      color: colors.secondaryForeground,
      fontWeight: '600',
    },
    buttonTextOutline: {
      color: colors.foreground,
      fontWeight: '600',
    },

    // Lists
    listItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },

    // Dividers
    divider: {
      height: 1,
      backgroundColor: colors.border,
      marginVertical: 16,
    },

    // Avatars
    avatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.muted,
    },

    // Badges
    badge: {
      backgroundColor: colors.primary,
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 4,
    },
    badgeText: {
      color: colors.primaryForeground,
      fontSize: 12,
      fontWeight: '600',
    },
  });
};
