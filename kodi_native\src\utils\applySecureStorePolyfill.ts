/**
 * Apply the SecureStore polyfill for web platforms
 * This should be imported early in the app's entry point
 */

import { Platform } from 'react-native';
import * as SecureStorePolyfill from './SecureStorePolyfill';

// Apply the polyfill only on web platforms
if (Platform.OS === 'web') {
  // Replace the expo-secure-store module with our polyfill
  try {
    // @ts-ignore - Dynamically replacing the module
    require('expo-secure-store').default = SecureStorePolyfill;
    
    // Also copy all exports to the module
    Object.keys(SecureStorePolyfill).forEach(key => {
      // @ts-ignore - Dynamically replacing the module exports
      require('expo-secure-store')[key] = SecureStorePolyfill[key];
    });
    
    console.log('SecureStore polyfill applied for web');
  } catch (error) {
    console.error('Failed to apply SecureStore polyfill:', error);
  }
}
