'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import Link from 'next/link';

interface Prompt {
  _id: string;
  name: string;
  description?: string;
  prompt: string;
  companyId: string;
  isActive: boolean;
}

export default function PromptsSettingsPage() {
  const { user } = useAuth();
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    if (user?.profile?.companyId) {
      fetchPrompts(user.profile.companyId);
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchPrompts = async (companyId: string) => {
    try {
      setLoading(true);
      // In a real implementation, this would fetch from an AI prompts endpoint
      // For now, we'll simulate with a mock response
      // const { data } = await api.get(`${endpoints.aiPrompts.base}?companyId=${companyId}`);
      
      // Mock data for demonstration
      const mockPrompts: Prompt[] = [
        {
          _id: '1',
          name: 'Post Summary',
          description: 'Generates a concise summary of a post',
          prompt: 'Summarize the following post in 2-3 sentences: {{post}}',
          companyId,
          isActive: true
        },
        {
          _id: '2',
          name: 'Meeting Notes',
          description: 'Extracts key points from meeting notes',
          prompt: 'Extract the key action items, decisions, and discussion points from the following meeting notes: {{post}}',
          companyId,
          isActive: true
        },
        {
          _id: '3',
          name: 'Sentiment Analysis',
          description: 'Analyzes the sentiment of a post',
          prompt: 'Analyze the sentiment of the following post and categorize it as positive, negative, or neutral: {{post}}',
          companyId,
          isActive: false
        }
      ];
      
      setPrompts(mockPrompts);
    } catch (err: any) {
      console.error('Failed to fetch prompts:', err);
      setError('Failed to load prompts. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (prompt: Prompt) => {
    try {
      const updatedPrompt = { ...prompt, isActive: !prompt.isActive };
      
      // In a real implementation, this would update the prompt in the database
      // await api.patch(endpoints.aiPrompts.byId(prompt._id), {
      //   isActive: updatedPrompt.isActive
      // });
      
      // For now, we'll just update the local state
      setPrompts(prompts.map(p => 
        p._id === prompt._id ? updatedPrompt : p
      ));
      
      setSuccessMessage(`Prompt ${updatedPrompt.isActive ? 'activated' : 'deactivated'} successfully`);
      
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (err: any) {
      console.error('Failed to update prompt:', err);
      setError(err.response?.data?.message || 'Failed to update prompt. Please try again.');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-5xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">AI Prompts</h1>
          <div className="flex space-x-4">
            <Link 
              href="/settings"
              className="text-sm text-indigo-600 hover:text-indigo-800"
            >
              Back to Settings
            </Link>
            {user?.roles?.includes('admin') && (
              <Link
                href="/settings/prompts/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Create Prompt
              </Link>
            )}
          </div>
        </div>

        {error && (
          <div className="bg-red-50 p-4 rounded-md mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {successMessage && (
          <div className="bg-green-50 p-4 rounded-md mb-6">
            <p className="text-green-700">{successMessage}</p>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {prompts.length > 0 ? (
                prompts.map((prompt) => (
                  <li key={prompt._id} className={!prompt.isActive ? 'bg-gray-50' : ''}>
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-900">{prompt.name}</span>
                            {!prompt.isActive && (
                              <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                Inactive
                              </span>
                            )}
                          </div>
                          {prompt.description && (
                            <div className="text-sm text-gray-500">{prompt.description}</div>
                          )}
                        </div>
                        {user?.roles?.includes('admin') && (
                          <div className="flex items-center space-x-4">
                            <Link
                              href={`/settings/prompts/${prompt._id}`}
                              className="text-sm text-indigo-600 hover:text-indigo-900"
                            >
                              Edit
                            </Link>
                            <button
                              onClick={() => handleToggleActive(prompt)}
                              className={`text-sm ${prompt.isActive ? 'text-gray-600 hover:text-gray-900' : 'text-green-600 hover:text-green-900'}`}
                            >
                              {prompt.isActive ? 'Deactivate' : 'Activate'}
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </li>
                ))
              ) : (
                <li className="px-4 py-5 sm:px-6">
                  <div className="text-center text-gray-500">
                    No prompts found
                  </div>
                </li>
              )}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
