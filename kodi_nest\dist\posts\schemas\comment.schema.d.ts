import { Document, Schema as MongooseSchema } from 'mongoose';
export type CommentDocument = Comment & Document;
export declare class Comment {
    body: string;
    userId: MongooseSchema.Types.ObjectId;
    linkedObjectId: MongooseSchema.Types.ObjectId;
    objectType: string;
}
export declare const CommentSchema: MongooseSchema<Comment, import("mongoose").Model<Comment, any, any, any, Document<unknown, any, Comment> & Comment & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Comment, Document<unknown, {}, import("mongoose").FlatRecord<Comment>> & import("mongoose").FlatRecord<Comment> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
