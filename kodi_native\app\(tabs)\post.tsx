import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { Audio } from 'expo-av';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import api, { endpoints } from '@/src/api/api';
import Button from '@/src/components/ui/Button';
import Card from '@/src/components/ui/Card';
import { Team, PostType } from '@/src/types';

export default function PostScreen() {
  const { user } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  const params = useLocalSearchParams<{ postTypeId?: string; teamId?: string; fromPreRecord?: string }>();

  const [body, setBody] = useState('');
  const [teams, setTeams] = useState<Team[]>([]);
  const [postTypes, setPostTypes] = useState<PostType[]>([]);
  const [selectedTeam, setSelectedTeam] = useState<string>(params.teamId || '');
  const [selectedPostType, setSelectedPostType] = useState<string>(params.postTypeId || '');
  const [selectedPostTypeRecord, setSelectedPostTypeRecord] = useState<PostType | null>(null);
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [hashtagInput, setHashtagInput] = useState('');
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [audioUri, setAudioUri] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // If not coming from pre-record screen, redirect to post type selection
    if (!params.fromPreRecord && !params.postTypeId) {
      router.replace('/post-type-selection');
      return;
    }

    fetchData();
    // Set up audio recording permissions
    Audio.requestPermissionsAsync().then(({ granted }) => {
      if (!granted) {
        Alert.alert('Permission Required', 'Audio recording permission is required to record posts.');
      }
    });

    return () => {
      // Clean up recording if component unmounts during recording
      if (recording) {
        stopRecording();
      }
    };
  }, []);

  const fetchData = async () => {
    try {
      setLoadingData(true);
      const [teamsRes, postTypesRes] = await Promise.all([
        api.get(endpoints.teams.base),
        api.get(endpoints.postTypes.base),
      ]);
      setTeams(teamsRes.data);
      setPostTypes(postTypesRes.data);

      // If a post type is selected, fetch its details
      if (params.postTypeId) {
        try {
          const { data } = await api.get(endpoints.postTypes.byId(params.postTypeId));
          setSelectedPostTypeRecord(data);

          // Auto-start recording if coming from pre-record screen
          if (params.fromPreRecord === 'true') {
            // Small delay to ensure everything is loaded
            setTimeout(() => {
              startRecording();
            }, 500);
          }
        } catch (err) {
          console.error('Failed to fetch post type details:', err);
        }
      }
    } catch (err: any) {
      console.error('Failed to fetch data:', err);
      setError('Failed to load data. Please try again later.');
    } finally {
      setLoadingData(false);
    }
  };

  const startRecording = async () => {
    try {
      // Prepare the recording session
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      setRecording(recording);
      setIsRecording(true);
    } catch (err) {
      console.error('Failed to start recording', err);
      Alert.alert('Error', 'Failed to start recording. Please try again.');
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    try {
      setIsRecording(false);
      await recording.stopAndUnloadAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
      });

      const uri = recording.getURI();
      setAudioUri(uri);
      setRecording(null);

      // Automatically transcribe the audio after recording
      if (uri) {
        try {
          setLoading(true);
          const audioUrl = await uploadAudio(uri);

          if (audioUrl) {
            // Transcribe the audio
            const transcribeResponse = await api.post(endpoints.ai.transcribe, {
              audioUrl,
              postTypeId: selectedPostType || undefined,
            });

            const transcription = transcribeResponse.data.text;
            setBody(transcription);
          }
        } catch (err) {
          console.error('Failed to transcribe audio:', err);
          Alert.alert('Transcription Error', 'Failed to transcribe audio. You can still create your post manually.');
        } finally {
          setLoading(false);
        }
      }
    } catch (err) {
      console.error('Failed to stop recording', err);
      Alert.alert('Error', 'Failed to stop recording. Please try again.');
    }
  };

  const uploadAudio = async (uri: string = audioUri) => {
    if (!uri) return null;

    try {
      const formData = new FormData();
      formData.append('file', {
        uri: uri,
        type: 'audio/m4a',
        name: 'recording.m4a',
      } as any);

      const response = await api.post(endpoints.uploads.audioUpload, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data.url;
    } catch (err) {
      console.error('Failed to upload audio', err);
      throw new Error('Failed to upload audio');
    }
  };

  const addHashtag = () => {
    if (!hashtagInput.trim()) return;

    const tag = hashtagInput.trim().replace(/^#/, '');
    if (!hashtags.includes(tag)) {
      setHashtags([...hashtags, tag]);
    }
    setHashtagInput('');
  };

  const removeHashtag = (tag: string) => {
    setHashtags(hashtags.filter(t => t !== tag));
  };

  const createPost = async (isDraft: boolean = false) => {
    if (!body.trim() && !audioUri) {
      setError('Please enter post content or record audio');
      return;
    }

    try {
      setLoading(true);
      setError('');

      let audioUrl = null;
      if (audioUri) {
        audioUrl = await uploadAudio(audioUri);
      }

      const postData = {
        body: body.trim(),
        userId: user?.id,
        teamId: selectedTeam || undefined,
        postTypeId: selectedPostType || undefined,
        hashtags,
        audioUrl,
        isDraft,
      };

      await api.post(endpoints.posts.base, postData);

      // Reset form
      setBody('');
      setSelectedTeam('');
      setSelectedPostType('');
      setHashtags([]);
      setAudioUri(null);

      // Navigate back to feed
      if (!isDraft) {
        router.replace('/(tabs)');
      } else {
        Alert.alert('Success', 'Post saved as draft');
      }
    } catch (err: any) {
      console.error('Failed to create post:', err);
      setError(err.response?.data?.message || 'Failed to create post. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loadingData) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: colors.foreground }]}>Create a Post</Text>
          </View>

          {error ? (
            <Card style={[styles.errorCard, { backgroundColor: colors.destructive + '20' }]}>
              <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
            </Card>
          ) : null}

          <Card style={styles.formCard}>
            <TextInput
              style={[
                styles.input,
                { color: colors.foreground, backgroundColor: colors.input }
              ]}
              placeholder="What's on your mind?"
              placeholderTextColor={colors.mutedForeground}
              multiline
              value={body}
              onChangeText={setBody}
            />

            <View style={styles.selectors}>
              <Text style={[styles.sectionTitle, { color: colors.foreground }]}>Team (Optional)</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.selectorScroll}>
                {teams.map((team) => (
                  <TouchableOpacity
                    key={team._id}
                    style={[
                      styles.selectorItem,
                      {
                        backgroundColor: selectedTeam === team._id ? colors.primary : colors.secondary,
                        borderColor: colors.border,
                      }
                    ]}
                    onPress={() => setSelectedTeam(selectedTeam === team._id ? '' : team._id)}
                  >
                    <Text
                      style={[
                        styles.selectorItemText,
                        {
                          color: selectedTeam === team._id
                            ? colors.primaryForeground
                            : colors.secondaryForeground
                        }
                      ]}
                    >
                      {team.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <Text style={[styles.sectionTitle, { color: colors.foreground }]}>Post Type (Optional)</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.selectorScroll}>
                {postTypes.map((type) => (
                  <TouchableOpacity
                    key={type._id}
                    style={[
                      styles.selectorItem,
                      {
                        backgroundColor: selectedPostType === type._id ? colors.primary : colors.secondary,
                        borderColor: colors.border,
                      }
                    ]}
                    onPress={() => setSelectedPostType(selectedPostType === type._id ? '' : type._id)}
                  >
                    <Text
                      style={[
                        styles.selectorItemText,
                        {
                          color: selectedPostType === type._id
                            ? colors.primaryForeground
                            : colors.secondaryForeground
                        }
                      ]}
                    >
                      {type.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <Text style={[styles.sectionTitle, { color: colors.foreground }]}>Hashtags</Text>
              <View style={styles.hashtagInputContainer}>
                <TextInput
                  style={[
                    styles.hashtagInput,
                    { color: colors.foreground, backgroundColor: colors.input }
                  ]}
                  placeholder="Add hashtag"
                  placeholderTextColor={colors.mutedForeground}
                  value={hashtagInput}
                  onChangeText={setHashtagInput}
                  onSubmitEditing={addHashtag}
                />
                <TouchableOpacity
                  style={[styles.addHashtagButton, { backgroundColor: colors.primary }]}
                  onPress={addHashtag}
                >
                  <Ionicons name="add" size={20} color={colors.primaryForeground} />
                </TouchableOpacity>
              </View>

              <View style={styles.hashtagsContainer}>
                {hashtags.map((tag) => (
                  <View
                    key={tag}
                    style={[styles.hashtagItem, { backgroundColor: colors.secondary }]}
                  >
                    <Text style={[styles.hashtagText, { color: colors.secondaryForeground }]}>
                      #{tag}
                    </Text>
                    <TouchableOpacity
                      style={styles.removeHashtagButton}
                      onPress={() => removeHashtag(tag)}
                    >
                      <Ionicons name="close-circle" size={16} color={colors.mutedForeground} />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>

              <Text style={[styles.sectionTitle, { color: colors.foreground }]}>Audio Recording</Text>

              {selectedPostTypeRecord?.recordTips && (
                <View style={[styles.tipsContainer, { backgroundColor: colors.secondary + '40' }]}>
                  <Text style={[styles.tipsTitle, { color: colors.foreground }]}>
                    Recording Tips:
                  </Text>
                  <Text style={[styles.tipsText, { color: colors.mutedForeground }]}>
                    {selectedPostTypeRecord.recordTips.replace(/<[^>]*>/g, '')}
                  </Text>
                </View>
              )}

              <View style={styles.audioControls}>
                {isRecording ? (
                  <Button
                    title="Stop Recording"
                    variant="danger"
                    leftIcon={<Ionicons name="stop" size={20} color="white" />}
                    onPress={stopRecording}
                  />
                ) : (
                  <Button
                    title={audioUri ? "Record Again" : "Record Audio"}
                    variant="primary"
                    leftIcon={<Ionicons name="mic" size={20} color="white" />}
                    onPress={startRecording}
                  />
                )}

                {audioUri && (
                  <View style={[styles.audioPreview, { backgroundColor: colors.secondary }]}>
                    <Ionicons name="musical-note" size={20} color={colors.secondaryForeground} />
                    <Text style={[styles.audioPreviewText, { color: colors.secondaryForeground }]}>
                      Audio recorded
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </Card>

          <View style={styles.buttonContainer}>
            <Button
              title="Save as Draft"
              variant="outline"
              onPress={() => createPost(true)}
              isLoading={loading}
              style={styles.draftButton}
            />
            <Button
              title="Publish Post"
              onPress={() => createPost(false)}
              isLoading={loading}
              style={styles.publishButton}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorCard: {
    padding: 12,
    marginBottom: 16,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 14,
  },
  formCard: {
    marginBottom: 16,
  },
  input: {
    borderRadius: 8,
    padding: 12,
    minHeight: 120,
    textAlignVertical: 'top',
  },
  selectors: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  selectorScroll: {
    flexDirection: 'row',
  },
  selectorItem: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
  },
  selectorItemText: {
    fontSize: 14,
    fontWeight: '500',
  },
  hashtagInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hashtagInput: {
    flex: 1,
    borderRadius: 8,
    padding: 12,
    marginRight: 8,
  },
  addHashtagButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  hashtagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  hashtagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  hashtagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  removeHashtagButton: {
    marginLeft: 4,
  },
  audioControls: {
    marginTop: 8,
  },
  audioPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
  },
  audioPreviewText: {
    marginLeft: 8,
    fontSize: 14,
  },
  tipsContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  tipsText: {
    fontSize: 14,
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  draftButton: {
    flex: 1,
    marginRight: 8,
  },
  publishButton: {
    flex: 1,
    marginLeft: 8,
  },
});
