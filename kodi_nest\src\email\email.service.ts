import { Injectable, Logger } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import * as nodemailer from 'nodemailer';

interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
}

type EmailProvider = 'smtp' | 'ses';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly ses: AWS.SES;
  private readonly transporter: nodemailer.Transporter;
  private readonly fromName: string;
  private readonly fromAddress: string;
  private readonly provider: EmailProvider;

  constructor() {
    // Get email sender details from environment variables
    this.fromName = process.env.MAIL_FROM_NAME || 'Kodi Support';
    this.fromAddress = process.env.MAIL_FROM_ADDRESS || '<EMAIL>';
    this.provider = (process.env.MAIL_PROVIDER || 'ses') as EmailProvider;

    // Initialize email provider based on configuration
    if (this.provider === 'ses') {
      // Initialize AWS SES with credentials from environment variables
      this.ses = new AWS.SES({
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        region: process.env.AWS_REGION,
      });
    } else {
      // Initialize SMTP transporter
      this.transporter = nodemailer.createTransport({
        host: process.env.MAIL_HOST,
        port: parseInt(process.env.MAIL_PORT || '587', 10),
        secure: process.env.MAIL_SECURE === 'true',
        auth: {
          user: process.env.MAIL_USERNAME,
          pass: process.env.MAIL_PASSWORD,
        },
      });
    }
  }

  /**
   * Send an email using the configured provider (AWS SES or SMTP)
   */
  async sendEmail(options: EmailOptions): Promise<boolean> {
    const { to, subject, text, html } = options;

    try {
      if (this.provider === 'ses') {
        // Send email using AWS SES
        const params: AWS.SES.SendEmailRequest = {
          Source: `"${this.fromName}" <${this.fromAddress}>`,
          Destination: {
            ToAddresses: Array.isArray(to) ? to : [to],
          },
          Message: {
            Subject: {
              Data: subject,
            },
            Body: {
              ...(text && {
                Text: {
                  Data: text,
                },
              }),
              ...(html && {
                Html: {
                  Data: html,
                },
              }),
            },
          },
        };

        const result = await this.ses.sendEmail(params).promise();
        this.logger.log(`Email sent successfully via SES: ${result.MessageId}`);
        return true;
      } else {
        // Send email using SMTP
        const mailOptions: nodemailer.SendMailOptions = {
          from: `"${this.fromName}" <${this.fromAddress}>`,
          to: Array.isArray(to) ? to.join(',') : to,
          subject,
          ...(text && { text }),
          ...(html && { html }),
        };

        const info = await this.transporter.sendMail(mailOptions);
        this.logger.log(`Email sent successfully via SMTP: ${info.messageId}`);
        return true;
      }
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Send a verification email to a new user
   */
  async sendVerificationEmail(email: string, token: string): Promise<boolean> {
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3010';
    const verificationLink = `${frontendUrl}/verify-email?token=${token}`;

    return this.sendEmail({
      to: email,
      subject: 'Verify Your Email Address',
      html: `
        <h1>Welcome to Kodi!</h1>
        <p>Thank you for signing up. Please verify your email address by clicking the link below:</p>
        <p><a href="${verificationLink}">Verify Email Address</a></p>
        <p>If you did not create an account, please ignore this email.</p>
        <p>This link will expire in 24 hours.</p>
      `,
    });
  }

  /**
   * Send a password reset email
   */
  async sendPasswordResetEmail(email: string, token: string): Promise<boolean> {
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3010';
    const resetLink = `${frontendUrl}/reset-password?token=${token}`;

    return this.sendEmail({
      to: email,
      subject: 'Reset Your Password',
      html: `
        <h1>Reset Your Password</h1>
        <p>You requested to reset your password. Please click the link below to set a new password:</p>
        <p><a href="${resetLink}">Reset Password</a></p>
        <p>If you did not request a password reset, please ignore this email.</p>
        <p>This link will expire in 1 hour.</p>
      `,
    });
  }

  /**
   * Send a welcome email to a new user
   */
  async sendWelcomeEmail(email: string, firstName?: string): Promise<boolean> {
    const name = firstName || 'there';

    return this.sendEmail({
      to: email,
      subject: 'Welcome to Kodi!',
      html: `
        <h1>Welcome to Kodi, ${name}!</h1>
        <p>Thank you for joining Kodi. We're excited to have you on board!</p>
        <p>With Kodi, you can:</p>
        <ul>
          <li>Create voice-based posts with AI-powered insights</li>
          <li>Collaborate with your team</li>
          <li>Get valuable feedback and analysis</li>
        </ul>
        <p>If you have any questions, feel free to reach out to our support team.</p>
      `,
    });
  }

  /**
   * Send an invite email to join a company
   */
  async sendInviteEmail(email: string, companyName: string, inviteCode: string): Promise<boolean> {
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3010';
    const inviteLink = `${frontendUrl}/register?inviteCode=${inviteCode}`;

    return this.sendEmail({
      to: email,
      subject: `You've been invited to join ${companyName} on Kodi`,
      html: `
        <h1>You've Been Invited!</h1>
        <p>You've been invited to join <strong>${companyName}</strong> on Kodi.</p>
        <p>Click the link below to accept the invitation and create your account:</p>
        <p><a href="${inviteLink}">Accept Invitation</a></p>
        <p>If you have any questions, please contact the person who invited you.</p>
      `,
    });
  }
}
