import { configureAxe } from 'jest-axe';

// Configure axe for our specific needs
export const axe = configureAxe({
  rules: {
    // Add specific rule configurations here
    'color-contrast': { enabled: true },
    'frame-title': { enabled: false }, // Disable frame-title rule if not applicable
  },
  // Add global configuration here
  reporter: 'v2',
});

// Export a helper function to run axe on a container
export const runAxe = async (container: HTMLElement) => {
  const results = await axe(container);
  return results;
};
