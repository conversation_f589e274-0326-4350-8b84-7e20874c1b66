'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';

interface Summary {
  _id: string;
  tag?: string;
  teamId?: string;
  postTypeId?: string;
  timeRange?: string;
  userPrompt?: string;
  summary?: string;
  createdAt: string;
  userId: string;
}

interface Tag {
  _id: string;
  name: string;
  count: number;
  highlightColor?: string;
}

export default function AnalysisPage() {
  const { user } = useAuth();
  const [summaries, setSummaries] = useState<Summary[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [prompt, setPrompt] = useState('');
  const [generatingSummary, setGeneratingSummary] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [summariesRes, tagsRes] = await Promise.all([
        api.get(endpoints.summaries.base),
        api.get(endpoints.tags.base),
      ]);
      setSummaries(summariesRes.data);
      setTags(tagsRes.data);
    } catch (err: any) {
      console.error('Failed to fetch data:', err);
      setError('Failed to load analysis data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateSummary = async () => {
    if (!prompt) return;

    try {
      setGeneratingSummary(true);
      const { data } = await api.post(endpoints.summaries.generate, {
        userPrompt: prompt,
        tag: selectedTag,
      });
      setSummaries([data, ...summaries]);
      setPrompt('');
    } catch (err: any) {
      console.error('Failed to generate summary:', err);
      setError('Failed to generate summary. Please try again later.');
    } finally {
      setGeneratingSummary(false);
    }
  };

  const filteredSummaries = selectedTag
    ? summaries.filter((summary) => summary.tag === selectedTag)
    : summaries;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Analysis</h1>

      {error && (
        <div className="bg-red-50 p-4 rounded-md mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">Tags</h2>
            <div className="space-y-2">
              <button
                onClick={() => setSelectedTag(null)}
                className={`block w-full text-left px-3 py-2 rounded-md ${
                  selectedTag === null
                    ? 'bg-indigo-100 text-indigo-800'
                    : 'hover:bg-gray-100'
                }`}
              >
                All Tags
              </button>
              {tags.map((tag) => (
                <button
                  key={tag._id}
                  onClick={() => setSelectedTag(tag.name)}
                  className={`block w-full text-left px-3 py-2 rounded-md ${
                    selectedTag === tag.name
                      ? 'bg-indigo-100 text-indigo-800'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span>#{tag.name}</span>
                    <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
                      {tag.count}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">Generate New Analysis</h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="prompt" className="block text-sm font-medium text-gray-700">
                  What would you like to analyze?
                </label>
                <textarea
                  id="prompt"
                  rows={3}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="E.g., Summarize the key themes in posts about customer service"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                ></textarea>
              </div>
              <div className="flex justify-end">
                <button
                  onClick={handleGenerateSummary}
                  disabled={!prompt || generatingSummary}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:bg-indigo-400"
                >
                  {generatingSummary ? 'Generating...' : 'Generate Analysis'}
                </button>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
            </div>
          ) : filteredSummaries.length === 0 ? (
            <div className="bg-white rounded-lg shadow p-6 text-center">
              <p className="text-gray-500">No analysis found. Generate your first analysis above!</p>
            </div>
          ) : (
            <div className="space-y-6">
              {filteredSummaries.map((summary) => (
                <div key={summary._id} className="bg-white rounded-lg shadow p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-medium">
                        {summary.userPrompt || 'Analysis'}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {new Date(summary.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    {summary.tag && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                        #{summary.tag}
                      </span>
                    )}
                  </div>
                  <div className="prose max-w-none">
                    {summary.summary ? (
                      <div dangerouslySetInnerHTML={{ __html: summary.summary }} />
                    ) : (
                      <p className="text-gray-500 italic">No summary available</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
