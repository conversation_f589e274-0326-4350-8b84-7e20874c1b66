'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import CreatePostForm from '@/components/posts/CreatePostForm';
import SamplePost from '@/components/onboarding/SamplePost';
import PostItem from '@/components/posts/PostItem';
import FilterInputField from '@/components/feed/FilterInputField';

interface Post {
  _id: string;
  body: string;
  enhancedBody?: string;
  userId: string;
  createdAt: string;
  hashtags: string[];
  user?: {
    firstName?: string;
    lastName?: string;
    avatar?: string;
  };
  teamId?: string;
  team?: {
    name: string;
  };
  postTypeId?: string;
  postType?: {
    name: string;
  };
  audioUrl?: string;
}

interface Team {
  _id: string;
  name: string;
}

interface PostType {
  _id: string;
  name: string;
}

interface Tag {
  _id: string;
  name: string;
  count: number;
}

export default function FeedPage() {
  const { user } = useAuth();
  const [posts, setPosts] = useState<Post[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [postTypes, setPostTypes] = useState<PostType[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [company, setCompany] = useState<{name: string} | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Filter states
  const [selectedTeam, setSelectedTeam] = useState<string>('');
  const [selectedPostType, setSelectedPostType] = useState<string>('');
  const [selectedTag, setSelectedTag] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);
  const [showDesktopFilters, setShowDesktopFilters] = useState(true);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    fetchPosts();
  }, [selectedTeam, selectedPostType, selectedTag]);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Make API calls one by one to isolate any issues
      try {
        const postsRes = await api.get(endpoints.posts.feed);
        setPosts(postsRes.data);
      } catch (err) {
        console.error('Failed to fetch posts:', err);
        // Continue with other requests even if this one fails
      }

      try {
        // The server will now enforce the company ID restriction based on the user's JWT token
        const teamsRes = await api.get(endpoints.teams.base);
        setTeams(teamsRes.data);
      } catch (err) {
        console.error('Failed to fetch teams:', err);
        // Continue with other requests even if this one fails
      }

      try {
        // The server will now enforce the company ID restriction based on the user's JWT token
        const postTypesRes = await api.get(endpoints.postTypes.base);
        setPostTypes(postTypesRes.data);
      } catch (err) {
        console.error('Failed to fetch post types:', err);
        // Continue with other requests even if this one fails
      }

      try {
        // The server will now enforce the company ID restriction based on the user's JWT token
        const tagsRes = await api.get(endpoints.tags.base);
        setTags(tagsRes.data);
      } catch (err) {
        console.error('Failed to fetch tags:', err);
        // Continue with other requests even if this one fails
      }

      try {
        if (user?.profile?.companyId) {
          const companyRes = await api.get(endpoints.companies.byId(user.profile.companyId));
          setCompany(companyRes.data);
        }
      } catch (err) {
        console.error('Failed to fetch company:', err);
        // Continue with other requests even if this one fails
      }

      setError(''); // Clear any previous errors
    } catch (err: any) {
      console.error('Failed to fetch data:', err);
      setError('Failed to load feed data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchPosts = async () => {
    try {
      setLoading(true);
      let url = endpoints.posts.feed;
      const params = new URLSearchParams();

      if (selectedTeam) params.append('teamId', selectedTeam);
      if (selectedPostType) params.append('postTypeId', selectedPostType);
      if (selectedTag) params.append('tag', selectedTag);

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const { data } = await api.get(url);
      setPosts(data);
    } catch (err: any) {
      console.error('Failed to fetch posts:', err);
      setError('Failed to load posts. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const clearFilters = () => {
    setSelectedTeam('');
    setSelectedPostType('');
    setSelectedTag('');
  };

  const hasActiveFilters = selectedTeam || selectedPostType || selectedTag;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Mobile filter button - only visible on small screens */}
      <div className="flex justify-between items-center mb-6 md:hidden">
        <h1 className="text-2xl font-bold">Feed</h1>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
          </svg>
          Filters {hasActiveFilters && '(Active)'}
        </button>
      </div>

      {error && (
        <div className="bg-red-50 p-4 rounded-md mb-6">
          <p className="text-red-700">{error}</p>
          <div className="flex justify-center mt-4 space-x-4">
            <button
              onClick={fetchData}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              Retry
            </button>
            <button
              onClick={async () => {
                try {
                  // Test API call using the Axios instance
                  console.log('Testing API call to /posts/feed');
                  const response = await api.get(endpoints.posts.feed);
                  console.log('API response status:', response.status);
                  console.log('API response data:', response.data);
                  alert('API Test Result: ' + JSON.stringify(response.data, null, 2));
                } catch (err: any) {
                  console.error('API call error:', err);
                  alert('API Test Error: ' + err.message);
                }
              }}
              className="px-4 py-2 bg-amber-400 text-gray-900 rounded-md hover:bg-amber-500 focus:outline-none focus:ring-2 focus:ring-amber-300"
            >
              Test API
            </button>
          </div>
        </div>
      )}

      {/* Mobile filters - only visible on small screens when toggled */}
      {showFilters && (
        <div className="bg-white rounded-lg shadow p-6 mb-6 md:hidden">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Filter Posts</h2>
            <div className="flex space-x-4 items-center">
              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="text-sm text-indigo-600 hover:text-indigo-800"
                >
                  Clear all filters
                </button>
              )}
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-500 hover:text-gray-700"
                aria-label="Close filters"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-4">
            <FilterInputField
              label="Team"
              id="team-filter-mobile"
              value={selectedTeam}
              onChange={setSelectedTeam}
              options={[
                { value: '', label: 'All Teams' },
                ...teams.map(team => ({ value: team._id, label: team.name }))
              ]}
            />

            <FilterInputField
              label="Post Type"
              id="post-type-filter-mobile"
              value={selectedPostType}
              onChange={setSelectedPostType}
              options={[
                { value: '', label: 'All Post Types' },
                ...postTypes.map(type => ({ value: type._id, label: type.name }))
              ]}
            />

            <FilterInputField
              label="Tag"
              id="tag-filter-mobile"
              value={selectedTag}
              onChange={setSelectedTag}
              options={[
                { value: '', label: 'All Tags' },
                ...tags.map(tag => ({ value: tag.name, label: `#${tag.name} (${tag.count})` }))
              ]}
            />
          </div>
        </div>
      )}

      {/* Desktop layout with sidebar */}
      <div className="flex flex-col md:flex-row md:space-x-6">
        {/* Main content area */}
        <div className="md:flex-grow">
          {/* Desktop title - only visible on medium screens and up */}
          <h1 className="text-2xl font-bold mb-6 hidden md:block">Feed</h1>

          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
            </div>
          ) : posts.length === 0 ? (
            <div className="space-y-6">
              {hasActiveFilters ? (
                <div className="bg-white rounded-lg shadow p-6 text-center">
                  <p className="text-gray-500">
                    No posts match your current filters. Try adjusting your filters or clear them to see all posts.
                  </p>
                </div>
              ) : (
                <>
                  <SamplePost companyName={company?.name || 'your company'} />
                  <div className="bg-white rounded-lg shadow p-6 text-center">
                    <p className="text-gray-500">No posts yet. Be the first to post!</p>
                  </div>
                </>
              )}
            </div>
          ) : (
            <div className="space-y-6">
              {posts.map((post) => (
                <div key={post._id}>
                  <PostItem
                    post={post}
                    onTagClick={(tag) => setSelectedTag(tag)}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Desktop sidebar with filters - only visible on medium screens and up */}
        <div className="hidden md:block md:w-80">
          {!showDesktopFilters ? (
            <button
              onClick={() => setShowDesktopFilters(true)}
              className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 mb-4"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
              </svg>
              Show Filters {hasActiveFilters && '(Active)'}
            </button>
          ) : (
            <>
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-semibold text-lg">Filters</h3>
                <button
                  onClick={() => setShowDesktopFilters(false)}
                  className="text-gray-500 hover:text-gray-700"
                  aria-label="Close filters"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>

              {/* Teams Filter */}
              <div className="bg-white rounded-lg shadow p-4 mb-4">
                <div className="flex items-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                  </svg>
                  <h3 className="font-semibold">Teams</h3>
                </div>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="team-all"
                      name="team-filter"
                      value=""
                      checked={selectedTeam === ''}
                      onChange={() => {
                        setSelectedTeam('');
                        fetchPosts();
                      }}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <label htmlFor="team-all" className="ml-2 block text-sm text-gray-700">
                      All Teams
                    </label>
                  </div>
                  {teams.map((team) => (
                    <div key={team._id} className="flex items-center">
                      <input
                        type="radio"
                        id={`team-${team._id}`}
                        name="team-filter"
                        value={team._id}
                        checked={selectedTeam === team._id}
                        onChange={() => {
                          setSelectedTeam(team._id);
                          fetchPosts();
                        }}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                      />
                      <label htmlFor={`team-${team._id}`} className="ml-2 block text-sm text-gray-700">
                        {team.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Post Types Filter */}
              <div className="bg-white rounded-lg shadow p-4 mb-4">
                <div className="flex items-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                  </svg>
                  <h3 className="font-semibold">Post Types</h3>
                </div>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="post-type-all"
                      name="post-type-filter"
                      value=""
                      checked={selectedPostType === ''}
                      onChange={() => {
                        setSelectedPostType('');
                        fetchPosts();
                      }}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <label htmlFor="post-type-all" className="ml-2 block text-sm text-gray-700">
                      All Post Types
                    </label>
                  </div>
                  {postTypes.map((type) => (
                    <div key={type._id} className="flex items-center">
                      <input
                        type="radio"
                        id={`post-type-${type._id}`}
                        name="post-type-filter"
                        value={type._id}
                        checked={selectedPostType === type._id}
                        onChange={() => {
                          setSelectedPostType(type._id);
                          fetchPosts();
                        }}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                      />
                      <label htmlFor={`post-type-${type._id}`} className="ml-2 block text-sm text-gray-700">
                        {type.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Tags Filter */}
              <div className="bg-white rounded-lg shadow p-4 mb-4">
                <div className="flex items-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                  </svg>
                  <h3 className="font-semibold">Tags</h3>
                </div>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="tag-all"
                      name="tag-filter"
                      value=""
                      checked={selectedTag === ''}
                      onChange={() => {
                        setSelectedTag('');
                        fetchPosts();
                      }}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <label htmlFor="tag-all" className="ml-2 block text-sm text-gray-700">
                      All Tags
                    </label>
                  </div>
                  {tags.map((tag) => (
                    <div key={tag._id} className="flex items-center">
                      <input
                        type="radio"
                        id={`tag-${tag._id}`}
                        name="tag-filter"
                        value={tag.name}
                        checked={selectedTag === tag.name}
                        onChange={() => {
                          setSelectedTag(tag.name);
                          fetchPosts();
                        }}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                      />
                      <label htmlFor={`tag-${tag._id}`} className="ml-2 block text-sm text-gray-700">
                        #{tag.name} <span className="text-gray-500">({tag.count})</span>
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Clear Filters Button */}
              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="w-full py-2 px-4 border border-indigo-600 rounded-md text-indigo-600 hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm font-medium"
                >
                  Clear all filters
                </button>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
