import { Request } from 'express';
import { TeamsService } from './teams.service';
import { CreateTeamDto } from './dto/create-team.dto';
import { UpdateTeamDto } from './dto/update-team.dto';
export declare class TeamsController {
    private readonly teamsService;
    constructor(teamsService: TeamsService);
    create(createTeamDto: CreateTeamDto): Promise<import("./schemas/team.schema").TeamDocument>;
    findAll(req: Request, companyId?: string, userId?: string): never[] | Promise<import("./schemas/team.schema").TeamDocument[]>;
    findOne(id: string): Promise<import("./schemas/team.schema").TeamDocument>;
    update(id: string, updateTeamDto: UpdateTeamDto, userId: string): Promise<import("./schemas/team.schema").TeamDocument>;
    remove(id: string): Promise<import("./schemas/team.schema").TeamDocument>;
    getTeamMembers(id: string): Promise<import("./schemas/membership.schema").MembershipDocument[]>;
    addMember(teamId: string, memberId: string): Promise<import("./schemas/membership.schema").MembershipDocument>;
    removeMember(teamId: string, memberId: string, userId: string): Promise<void>;
}
