import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { MongooseModule } from '@nestjs/mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
import * as bcrypt from 'bcrypt';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User } from '../src/users/schemas/user.schema';
import { Company } from '../src/companies/schemas/company.schema';

describe('Posts Flow (e2e)', () => {
  let app: INestApplication;
  let mongoMemoryServer: MongoMemoryServer;
  let userModel: Model<User>;
  let companyModel: Model<Company>;
  let accessToken: string;
  let userId: string;
  let companyId: string;
  let postId: string;

  beforeAll(async () => {
    // Create an in-memory MongoDB instance
    mongoMemoryServer = await MongoMemoryServer.create();
    const mongoUri = mongoMemoryServer.getUri();

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(mongoUri),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }));
    app.setGlobalPrefix('api');
    
    await app.init();

    // Get the models
    userModel = app.get<Model<User>>(getModelToken(User.name));
    companyModel = app.get<Model<Company>>(getModelToken(Company.name));

    // Create a test user
    const hashedPassword = await bcrypt.hash('Password123!', 10);
    const user = await userModel.create({
      email: '<EMAIL>',
      password: hashedPassword,
      roles: ['user'],
    });
    userId = user._id.toString();

    // Create a test company
    const company = await companyModel.create({
      name: 'Test Company',
      invite_code: 'TEST123',
      createdBy: user._id,
    });
    companyId = company._id.toString();

    // Update user with company reference
    await userModel.findByIdAndUpdate(userId, {
      profile: {
        firstName: 'Test',
        lastName: 'User',
        companyId: company._id,
      },
    });

    // Login to get access token
    const loginResponse = await request(app.getHttpServer())
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'Password123!',
      });

    accessToken = loginResponse.body.access_token;
  });

  afterAll(async () => {
    await app.close();
    await mongoMemoryServer.stop();
  });

  it('should create a post', async () => {
    const createPostDto = {
      body: 'Test post with #hashtag',
      userId,
      companyId,
      isDraft: false,
    };

    const response = await request(app.getHttpServer())
      .post('/api/posts')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(createPostDto)
      .expect(201);

    expect(response.body).toHaveProperty('_id');
    expect(response.body.body).toBe('Test post with #hashtag');
    expect(response.body.userId.toString()).toBe(userId);
    expect(response.body.companyId.toString()).toBe(companyId);
    expect(response.body.isDraft).toBe(false);
    expect(response.body.hashtags).toContain('hashtag');

    // Save post ID for later tests
    postId = response.body._id;
  });

  it('should get all posts', async () => {
    const response = await request(app.getHttpServer())
      .get('/api/posts')
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(response.body).toBeInstanceOf(Array);
    expect(response.body.length).toBeGreaterThan(0);
    expect(response.body[0]).toHaveProperty('_id');
    expect(response.body[0].body).toBe('Test post with #hashtag');
  });

  it('should get posts by company ID', async () => {
    const response = await request(app.getHttpServer())
      .get(`/api/posts?companyId=${companyId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(response.body).toBeInstanceOf(Array);
    expect(response.body.length).toBeGreaterThan(0);
    expect(response.body[0].companyId.toString()).toBe(companyId);
  });

  it('should get posts by tag', async () => {
    const response = await request(app.getHttpServer())
      .get('/api/posts?tag=hashtag')
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(response.body).toBeInstanceOf(Array);
    expect(response.body.length).toBeGreaterThan(0);
    expect(response.body[0].hashtags).toContain('hashtag');
  });

  it('should get a post by ID', async () => {
    const response = await request(app.getHttpServer())
      .get(`/api/posts/${postId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(response.body).toHaveProperty('_id', postId);
    expect(response.body.body).toBe('Test post with #hashtag');
  });

  it('should update a post', async () => {
    const updatePostDto = {
      body: 'Updated post with #newtag',
    };

    const response = await request(app.getHttpServer())
      .patch(`/api/posts/${postId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(updatePostDto)
      .expect(200);

    expect(response.body).toHaveProperty('_id', postId);
    expect(response.body.body).toBe('Updated post with #newtag');
    expect(response.body.hashtags).toContain('newtag');
    expect(response.body.hashtags).not.toContain('hashtag');
  });

  it('should create a comment on a post', async () => {
    const createCommentDto = {
      body: 'Test comment',
      userId,
      linkedObjectId: postId,
      objectType: 'Post',
    };

    const response = await request(app.getHttpServer())
      .post('/api/posts/comments')
      .set('Authorization', `Bearer ${accessToken}`)
      .send(createCommentDto)
      .expect(201);

    expect(response.body).toHaveProperty('_id');
    expect(response.body.body).toBe('Test comment');
    expect(response.body.userId.toString()).toBe(userId);
    expect(response.body.linkedObjectId.toString()).toBe(postId);
    expect(response.body.objectType).toBe('Post');
  });

  it('should get comments for a post', async () => {
    const response = await request(app.getHttpServer())
      .get(`/api/posts/comments/Post/${postId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(response.body).toBeInstanceOf(Array);
    expect(response.body.length).toBe(1);
    expect(response.body[0].body).toBe('Test comment');
    expect(response.body[0].linkedObjectId.toString()).toBe(postId);
  });

  it('should delete a post', async () => {
    await request(app.getHttpServer())
      .delete(`/api/posts/${postId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    // Verify post is deleted
    await request(app.getHttpServer())
      .get(`/api/posts/${postId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(404);

    // Verify comments are deleted
    const commentsResponse = await request(app.getHttpServer())
      .get(`/api/posts/comments/Post/${postId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(commentsResponse.body).toBeInstanceOf(Array);
    expect(commentsResponse.body.length).toBe(0);
  });
});
