import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  useWindowDimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/src/constants/Theme';
import Card from '@/src/components/ui/Card';
import { Team, PostType, Tag } from '@/src/types';
import Picker from '@/src/components/ui/Picker';
import RadioButton from '@/src/components/ui/RadioButton';
import AudioInputField from '@/src/components/ui/AudioInputField';
import FilterInputField from '@/src/components/feed/FilterInputField';

interface FeedFiltersProps {
  teams: Team[];
  postTypes: PostType[];
  tags: Tag[];
  selectedTeam: string;
  selectedPostType: string;
  selectedTag: string;
  onTeamChange: (teamId: string) => void;
  onPostTypeChange: (postTypeId: string) => void;
  onTagChange: (tagId: string) => void;
  onClearFilters: () => void;
  onApplyFilters: () => void;
  onClose?: () => void;
  colors: any;
  isDesktopLayout?: boolean;
}

export default function FeedFilters({
  teams,
  postTypes,
  tags,
  selectedTeam,
  selectedPostType,
  selectedTag,
  onTeamChange,
  onPostTypeChange,
  onTagChange,
  onClearFilters,
  onApplyFilters,
  onClose,
  colors,
  isDesktopLayout = false,
}: FeedFiltersProps) {
  const hasActiveFilters = selectedTeam || selectedPostType || selectedTag;
  const { width } = useWindowDimensions();

  // Determine if we should use desktop layout based on screen width
  const useDesktopLayout = isDesktopLayout || width >= 768;

  // Mobile layout with dropdowns
  const renderMobileLayout = () => (
    <Card style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.headerText, { color: colors.foreground }]}>Filter Posts</Text>
        <View style={styles.headerButtons}>
          {hasActiveFilters && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={onClearFilters}
              accessibilityRole="button"
              accessibilityLabel="Clear all filters"
            >
              <Text style={[styles.clearButtonText, { color: colors.primary }]}>
                Clear all filters
              </Text>
            </TouchableOpacity>
          )}
          {onClose && (
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              accessibilityRole="button"
              accessibilityLabel="Close filters"
            >
              <Ionicons name="close" size={20} color={colors.foreground} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <ScrollView style={styles.filtersContainer}>
        <FilterInputField
          label="Team"
          value={selectedTeam}
          onChange={onTeamChange}
          options={[
            { value: '', label: 'All Teams' },
            ...teams.map(team => ({ value: team._id, label: team.name }))
          ]}
          colors={colors}
          icon="people-outline"
        />

        <FilterInputField
          label="Post Type"
          value={selectedPostType}
          onChange={onPostTypeChange}
          options={[
            { value: '', label: 'All Post Types' },
            ...postTypes.map(type => ({ value: type._id, label: type.name }))
          ]}
          colors={colors}
          icon="document-text-outline"
        />

        <FilterInputField
          label="Tag"
          value={selectedTag}
          onChange={onTagChange}
          options={[
            { value: '', label: 'All Tags' },
            ...tags.map(tag => ({ value: tag.name, label: `#${tag.name} (${tag.count})` }))
          ]}
          colors={colors}
          icon="pricetag-outline"
        />
      </ScrollView>

      <TouchableOpacity
        style={[styles.applyButton, { backgroundColor: colors.primary }]}
        onPress={onApplyFilters}
        accessibilityRole="button"
        accessibilityLabel="Apply filters"
      >
        <Text style={[styles.applyButtonText, { color: colors.primaryForeground }]}>
          Apply Filters
        </Text>
      </TouchableOpacity>
    </Card>
  );

  // Desktop layout with radio buttons in separate boxes
  const renderDesktopLayout = () => (
    <View style={styles.desktopContainer}>
      {/* Header with close button */}
      {onClose && (
        <View style={styles.desktopHeader}>
          <Text style={[styles.desktopHeaderTitle, { color: colors.foreground }]}>Filters</Text>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            accessibilityRole="button"
            accessibilityLabel="Close filters"
          >
            <Ionicons name="close" size={20} color={colors.foreground} />
          </TouchableOpacity>
        </View>
      )}

      {/* Teams Filter */}
      <Card style={styles.desktopFilterCard}>
        <View style={styles.desktopFilterHeader}>
          <Ionicons name="people-outline" size={20} color={colors.foreground} style={styles.desktopFilterIcon} />
          <Text style={[styles.desktopFilterTitle, { color: colors.foreground }]}>Teams</Text>
        </View>
        <ScrollView style={styles.desktopFilterList}>
          <RadioButton
            label="All Teams"
            value=""
            selectedValue={selectedTeam}
            onSelect={onTeamChange}
            colors={colors}
          />
          {teams.map((team) => (
            <RadioButton
              key={team._id}
              label={team.name}
              value={team._id}
              selectedValue={selectedTeam}
              onSelect={onTeamChange}
              colors={colors}
            />
          ))}
        </ScrollView>
      </Card>

      {/* Post Types Filter */}
      <Card style={styles.desktopFilterCard}>
        <View style={styles.desktopFilterHeader}>
          <Ionicons name="document-text-outline" size={20} color={colors.foreground} style={styles.desktopFilterIcon} />
          <Text style={[styles.desktopFilterTitle, { color: colors.foreground }]}>Post Types</Text>
        </View>
        <ScrollView style={styles.desktopFilterList}>
          <RadioButton
            label="All Post Types"
            value=""
            selectedValue={selectedPostType}
            onSelect={onPostTypeChange}
            colors={colors}
          />
          {postTypes.map((type) => (
            <RadioButton
              key={type._id}
              label={type.name}
              value={type._id}
              selectedValue={selectedPostType}
              onSelect={onPostTypeChange}
              colors={colors}
            />
          ))}
        </ScrollView>
      </Card>

      {/* Tags Filter */}
      <Card style={styles.desktopFilterCard}>
        <View style={styles.desktopFilterHeader}>
          <Ionicons name="pricetag-outline" size={20} color={colors.foreground} style={styles.desktopFilterIcon} />
          <Text style={[styles.desktopFilterTitle, { color: colors.foreground }]}>Tags</Text>
        </View>
        <ScrollView style={styles.desktopFilterList}>
          <RadioButton
            label="All Tags"
            value=""
            selectedValue={selectedTag}
            onSelect={onTagChange}
            colors={colors}
          />
          {tags.map((tag) => (
            <RadioButton
              key={tag._id}
              label={`#${tag.name}`}
              value={tag.name}
              selectedValue={selectedTag}
              onSelect={onTagChange}
              colors={colors}
              count={tag.count}
            />
          ))}
        </ScrollView>
      </Card>

      {hasActiveFilters && (
        <TouchableOpacity
          style={[styles.desktopClearButton, { borderColor: colors.border }]}
          onPress={onClearFilters}
        >
          <Text style={[styles.desktopClearButtonText, { color: colors.primary }]}>
            Clear all filters
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return useDesktopLayout ? renderDesktopLayout() : renderMobileLayout();
}

const styles = StyleSheet.create({
  // Mobile layout styles
  container: {
    marginHorizontal: 16,
    marginVertical: 8,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clearButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
  },
  clearButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  closeButton: {
    padding: 4,
  },
  filtersContainer: {
    maxHeight: 300,
  },
  filterSection: {
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  applyButton: {
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },

  // Desktop layout styles
  desktopContainer: {
    width: '100%',
    gap: 16,
  },
  desktopHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  desktopHeaderTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  desktopFilterCard: {
    padding: 16,
    marginBottom: 16,
  },
  desktopFilterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  desktopFilterIcon: {
    marginRight: 8,
  },
  desktopFilterTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  desktopFilterList: {
    maxHeight: 200,
  },
  desktopClearButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    marginTop: 8,
  },
  desktopClearButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
