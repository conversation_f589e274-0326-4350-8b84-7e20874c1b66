'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import Link from 'next/link';

interface PostType {
  _id: string;
  name: string;
  description?: string;
  recordTips?: string;
  companyId: string;
}

interface Team {
  _id: string;
  name: string;
  companyId: string;
}

export default function PreRecordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  
  const postTypeId = searchParams.get('postTypeId');
  const teamId = searchParams.get('teamId');
  
  const [postType, setPostType] = useState<PostType | null>(null);
  const [team, setTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!postTypeId) {
      router.push('/post/type-selection');
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch post type details
        const { data: postTypeData } = await api.get(endpoints.postTypes.byId(postTypeId));
        setPostType(postTypeData);
        
        // Fetch team details if teamId is provided
        if (teamId) {
          const { data: teamData } = await api.get(endpoints.teams.byId(teamId));
          setTeam(teamData);
        }
      } catch (err: any) {
        console.error('Failed to fetch data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [postTypeId, teamId, router]);

  const handleStartRecording = () => {
    // Navigate to the post creation page with the selected post type and team
    router.push(`/post/create?postTypeId=${postTypeId}${teamId ? `&teamId=${teamId}` : ''}`);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Ready to Record</h1>
          <div className="flex space-x-4">
            <Link 
              href="/post/type-selection"
              className="text-sm text-indigo-600 hover:text-indigo-800"
            >
              Back
            </Link>
            <Link 
              href="/feed"
              className="text-sm text-gray-600 hover:text-gray-800"
            >
              Cancel
            </Link>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 p-4 rounded-md">
            <p className="text-red-700">{error}</p>
            <button 
              onClick={() => router.push('/post/type-selection')}
              className="mt-2 text-sm text-red-700 underline"
            >
              Go back to post type selection
            </button>
          </div>
        ) : (
          <div className="bg-white shadow-md rounded-lg p-6">
            {team && (
              <div className="mb-4 inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                Team: {team.name}
              </div>
            )}
            
            <h2 className="text-xl font-semibold mb-4">{postType?.name}</h2>
            
            {postType?.description && (
              <div className="mb-6 text-gray-600" dangerouslySetInnerHTML={{ __html: postType.description }} />
            )}
            
            {postType?.recordTips && (
              <div className="mb-8">
                <h3 className="text-lg font-medium mb-2">Recording Tips:</h3>
                <div className="bg-indigo-50 p-4 rounded-md" dangerouslySetInnerHTML={{ __html: postType.recordTips }} />
              </div>
            )}
            
            <div className="mt-8 flex justify-center">
              <button
                onClick={handleStartRecording}
                className="px-6 py-3 bg-indigo-600 text-white rounded-full hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                </svg>
                Start Recording
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
