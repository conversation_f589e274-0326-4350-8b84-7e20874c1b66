import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Serve static files from the uploads directory
  app.useStaticAssets(join(process.cwd(), 'uploads'), {
    prefix: '/uploads',
  });

  // Define development origins for CORS
  const developmentOrigins = [
    // Next.js frontend
    'http://localhost:3010',
    'http://localhost:3002',
    // Expo web development server ports
    'http://localhost:19000',
    'http://localhost:19006',
    'http://localhost:8081',
    // Include all possible Expo web ports
    /^http:\/\/localhost:(19\d{3}|8\d{3})$/,
    // Allow any local IP for development
    /^http:\/\/192\.168\.\d+\.\d+:(19\d{3}|8\d{3}|3010|3002)$/,
    // Allow any IP with standard ports during development
    /^http:\/\/\d+\.\d+\.\d+\.\d+:(19\d{3}|8\d{3}|3010|3002)$/,
  ];

  // Enable CORS
  app.enableCors({
    origin: process.env.NODE_ENV === 'production'
      ? [
          process.env.FRONTEND_URL || 'http://localhost:3010',
          process.env.NATIVE_URL || 'http://localhost:19000',
        ].filter(Boolean)
      : developmentOrigins,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    credentials: true,
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    exposedHeaders: ['Content-Disposition'],
  });

  // Set global prefix but exclude certain routes
  const apiPrefix = process.env.API_PREFIX || 'api';
  app.setGlobalPrefix(apiPrefix, {
    exclude: ['/health'], // Exclude the health endpoint from the global prefix
  });

  // Enable validation
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    transform: true,
    forbidNonWhitelisted: true,
  }));

  // Start the server
  const port = parseInt(process.env.PORT || '3011', 10);
  const host = process.env.HOST || '0.0.0.0';

  console.log(`Attempting to start server on ${host}:${port}`);
  await app.listen(port, host);

  // Get the actual address the server is listening on
  const serverUrl = await app.getUrl();
  console.log(`Application is running on: ${serverUrl}/${apiPrefix}`);
}
bootstrap();
