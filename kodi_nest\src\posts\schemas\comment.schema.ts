import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type CommentDocument = Comment & Document;

@Schema({
  collection: 'kd_comments',
  timestamps: true,
})
export class Comment {
  @Prop({ required: true })
  body: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ 
    type: MongooseSchema.Types.ObjectId, 
    refPath: 'objectType', 
    required: true,
    index: true 
  })
  linkedObjectId: MongooseSchema.Types.ObjectId;

  @Prop({ 
    type: String, 
    required: true,
    enum: ['Post', 'Summary', 'Insight'] 
  })
  objectType: string;
}

export const CommentSchema = SchemaFactory.createForClass(Comment);
