// Simple monitoring and error tracking utility

// Types for performance metrics
interface PerformanceMetrics {
  pageLoadTime?: number;
  ttfb?: number;
  fcp?: number;
  lcp?: number;
  cls?: number;
  fid?: number;
  navigationStart?: number;
}

// Types for error tracking
interface ErrorEvent {
  message: string;
  source?: string;
  lineno?: number;
  colno?: number;
  error?: Error;
  timestamp: number;
  url: string;
  userAgent: string;
}

// Configuration
const config = {
  // Set to true to enable monitoring in production
  enabled: process.env.NODE_ENV === 'production',
  // Endpoint to send metrics to (replace with your actual endpoint)
  metricsEndpoint: '/api/monitoring/metrics',
  // Endpoint to send errors to (replace with your actual endpoint)
  errorsEndpoint: '/api/monitoring/errors',
  // Sample rate for performance monitoring (1 = 100%, 0.1 = 10%)
  sampleRate: 0.1,
  // Maximum number of errors to track per session
  maxErrorsPerSession: 10,
};

// State
let errorsTracked = 0;
let sessionId: string | null = null;

// Initialize monitoring
export function initMonitoring() {
  if (!config.enabled || typeof window === 'undefined') {
    return;
  }

  // Generate a session ID
  sessionId = generateSessionId();

  // Set up error tracking
  setupErrorTracking();

  // Set up performance monitoring
  setupPerformanceMonitoring();
}

// Generate a unique session ID
function generateSessionId(): string {
  return `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
}

// Set up error tracking
function setupErrorTracking() {
  window.addEventListener('error', handleError);
  window.addEventListener('unhandledrejection', handlePromiseRejection);
}

// Handle JavaScript errors
function handleError(event: ErrorEvent) {
  if (!config.enabled || errorsTracked >= config.maxErrorsPerSession) {
    return;
  }

  const errorData: ErrorEvent = {
    message: event.message,
    source: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error,
    timestamp: Date.now(),
    url: window.location.href,
    userAgent: navigator.userAgent,
  };

  sendErrorToServer(errorData);
  errorsTracked++;
}

// Handle unhandled promise rejections
function handlePromiseRejection(event: PromiseRejectionEvent) {
  if (!config.enabled || errorsTracked >= config.maxErrorsPerSession) {
    return;
  }

  const errorData: ErrorEvent = {
    message: `Unhandled Promise Rejection: ${event.reason}`,
    error: event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
    timestamp: Date.now(),
    url: window.location.href,
    userAgent: navigator.userAgent,
  };

  sendErrorToServer(errorData);
  errorsTracked++;
}

// Send error data to the server
async function sendErrorToServer(errorData: ErrorEvent) {
  try {
    if (navigator.sendBeacon) {
      const blob = new Blob([JSON.stringify({ ...errorData, sessionId })], {
        type: 'application/json',
      });
      navigator.sendBeacon(config.errorsEndpoint, blob);
    } else {
      await fetch(config.errorsEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ...errorData, sessionId }),
        // Use keepalive to ensure the request completes even if the page is unloading
        keepalive: true,
      });
    }
  } catch (e) {
    // Silently fail if we can't send the error
    console.error('Failed to send error to server:', e);
  }
}

// Set up performance monitoring
function setupPerformanceMonitoring() {
  // Only monitor a sample of users
  if (Math.random() > config.sampleRate) {
    return;
  }

  // Listen for the load event to capture initial metrics
  window.addEventListener('load', () => {
    // Use requestIdleCallback to avoid impacting page performance
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => capturePerformanceMetrics());
    } else {
      setTimeout(() => capturePerformanceMetrics(), 1000);
    }
  });

  // Set up Core Web Vitals reporting
  setupCoreWebVitals();
}

// Capture performance metrics
function capturePerformanceMetrics() {
  if (!performance || !performance.timing) {
    return;
  }

  const metrics: PerformanceMetrics = {};
  const timing = performance.timing;

  // Basic metrics
  metrics.navigationStart = timing.navigationStart;
  metrics.pageLoadTime = timing.loadEventEnd - timing.navigationStart;
  metrics.ttfb = timing.responseStart - timing.requestStart;

  // Send metrics to server
  sendMetricsToServer(metrics);
}

// Set up Core Web Vitals reporting
function setupCoreWebVitals() {
  if (!('PerformanceObserver' in window)) {
    return;
  }

  // First Contentful Paint (FCP)
  try {
    const fcpObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      if (entries.length > 0) {
        const fcp = entries[0];
        const metrics: PerformanceMetrics = {
          fcp: fcp.startTime,
        };
        sendMetricsToServer(metrics);
        fcpObserver.disconnect();
      }
    });
    fcpObserver.observe({ type: 'paint', buffered: true });
  } catch (e) {
    console.error('FCP monitoring error:', e);
  }

  // Largest Contentful Paint (LCP)
  try {
    const lcpObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        const metrics: PerformanceMetrics = {
          lcp: lastEntry.startTime,
        };
        sendMetricsToServer(metrics);
      }
    });
    lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
  } catch (e) {
    console.error('LCP monitoring error:', e);
  }

  // Cumulative Layout Shift (CLS)
  try {
    let clsValue = 0;
    let clsEntries: PerformanceEntry[] = [];

    const clsObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry) => {
        // Only count layout shifts without recent user input
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
          clsEntries.push(entry);
        }
      });
    });
    clsObserver.observe({ type: 'layout-shift', buffered: true });

    // Report CLS when the page is hidden or unloaded
    const reportCLS = () => {
      if (clsEntries.length > 0) {
        const metrics: PerformanceMetrics = {
          cls: clsValue,
        };
        sendMetricsToServer(metrics);
      }
    };

    window.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        reportCLS();
      }
    });

    window.addEventListener('beforeunload', reportCLS);
  } catch (e) {
    console.error('CLS monitoring error:', e);
  }

  // First Input Delay (FID)
  try {
    const fidObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      if (entries.length > 0) {
        const firstInput = entries[0];
        const metrics: PerformanceMetrics = {
          fid: (firstInput as any).processingStart - firstInput.startTime,
        };
        sendMetricsToServer(metrics);
        fidObserver.disconnect();
      }
    });
    fidObserver.observe({ type: 'first-input', buffered: true });
  } catch (e) {
    console.error('FID monitoring error:', e);
  }
}

// Send metrics to the server
async function sendMetricsToServer(metrics: PerformanceMetrics) {
  try {
    if (navigator.sendBeacon) {
      const blob = new Blob([JSON.stringify({ ...metrics, sessionId, url: window.location.href })], {
        type: 'application/json',
      });
      navigator.sendBeacon(config.metricsEndpoint, blob);
    } else {
      await fetch(config.metricsEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ...metrics, sessionId, url: window.location.href }),
        keepalive: true,
      });
    }
  } catch (e) {
    // Silently fail if we can't send the metrics
    console.error('Failed to send metrics to server:', e);
  }
}

// Track a custom event
export function trackEvent(eventName: string, eventData: Record<string, any> = {}) {
  if (!config.enabled || !sessionId) {
    return;
  }

  try {
    if (navigator.sendBeacon) {
      const blob = new Blob(
        [
          JSON.stringify({
            eventName,
            eventData,
            sessionId,
            timestamp: Date.now(),
            url: window.location.href,
          }),
        ],
        { type: 'application/json' }
      );
      navigator.sendBeacon('/api/monitoring/events', blob);
    } else {
      fetch('/api/monitoring/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventName,
          eventData,
          sessionId,
          timestamp: Date.now(),
          url: window.location.href,
        }),
        keepalive: true,
      });
    }
  } catch (e) {
    // Silently fail if we can't send the event
    console.error('Failed to send event to server:', e);
  }
}

// Export a function to manually report an error
export function reportError(error: Error, context: Record<string, any> = {}) {
  if (!config.enabled || errorsTracked >= config.maxErrorsPerSession) {
    return;
  }

  const errorData: ErrorEvent = {
    message: error.message,
    error,
    timestamp: Date.now(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    ...context,
  };

  sendErrorToServer(errorData);
  errorsTracked++;
}

// Initialize monitoring when this module is imported
if (typeof window !== 'undefined') {
  initMonitoring();
}
