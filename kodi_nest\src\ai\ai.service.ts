import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Summary, SummaryDocument } from './schemas/summary.schema';
import { Insight, InsightDocument } from './schemas/insight.schema';
import { AiPrompt, AiPromptDocument } from './schemas/ai-prompt.schema';
import { AiChat, AiChatDocument, ChatMessage } from './schemas/ai-chat.schema';
import { AppSettings, AppSettingsDocument } from './schemas/app-settings.schema';
import { CreateSummaryDto } from './dto/create-summary.dto';
import { CreateInsightDto } from './dto/create-insight.dto';
import { CreateAiPromptDto } from './dto/create-ai-prompt.dto';
import { CreateAiChatDto } from './dto/create-ai-chat.dto';
import { CreateAppSettingsDto } from './dto/create-app-settings.dto';
import { TranscribeAudioDto } from './dto/transcribe-audio.dto';
import { PostsService } from '../posts/posts.service';

@Injectable()
export class AiService {
  constructor(
    @InjectModel(Summary.name) private summaryModel: Model<SummaryDocument>,
    @InjectModel(Insight.name) private insightModel: Model<InsightDocument>,
    @InjectModel(AiPrompt.name) private aiPromptModel: Model<AiPromptDocument>,
    @InjectModel(AiChat.name) private aiChatModel: Model<AiChatDocument>,
    @InjectModel(AppSettings.name) private appSettingsModel: Model<AppSettingsDocument>,
    private postsService: PostsService,
  ) {}

  // Summary methods
  async createSummary(createSummaryDto: CreateSummaryDto): Promise<SummaryDocument> {
    const newSummary = new this.summaryModel(createSummaryDto);
    return newSummary.save();
  }

  async findAllSummaries(
    userId?: string,
    companyId?: string,
    tag?: string,
  ): Promise<SummaryDocument[]> {
    const query: any = {};

    if (userId) {
      query.userId = userId;
    }

    if (companyId) {
      query.companyId = companyId;
    }

    if (tag) {
      query.tag = tag;
    }

    return this.summaryModel
      .find(query)
      .sort({ createdAt: -1 })
      .exec();
  }

  async findSummaryById(id: string): Promise<SummaryDocument> {
    const summary = await this.summaryModel.findById(id).exec();
    if (!summary) {
      throw new NotFoundException(`Summary with ID ${id} not found`);
    }
    return summary;
  }

  async updateSummary(id: string, updateSummaryDto: any): Promise<SummaryDocument> {
    const updatedSummary = await this.summaryModel
      .findByIdAndUpdate(id, updateSummaryDto, { new: true })
      .exec();

    if (!updatedSummary) {
      throw new NotFoundException(`Summary with ID ${id} not found`);
    }

    return updatedSummary;
  }

  async removeSummary(id: string): Promise<SummaryDocument> {
    const deletedSummary = await this.summaryModel.findByIdAndDelete(id).exec();

    if (!deletedSummary) {
      throw new NotFoundException(`Summary with ID ${id} not found`);
    }

    return deletedSummary;
  }

  async generateSummary(createSummaryDto: CreateSummaryDto): Promise<SummaryDocument> {
    // In a real implementation, this would call an AI service
    // For now, we'll just create a mock summary
    const mockSummary = `This is a generated summary based on the prompt: "${createSummaryDto.userPrompt}"

    Key points:
    - Point 1: Lorem ipsum dolor sit amet
    - Point 2: Consectetur adipiscing elit
    - Point 3: Sed do eiusmod tempor incididunt

    Conclusion: This is a placeholder summary that would normally be generated by an AI service.`;

    createSummaryDto.summary = mockSummary;
    createSummaryDto.lastUpdated = new Date();

    return this.createSummary(createSummaryDto);
  }

  // Insight methods
  async createInsight(createInsightDto: CreateInsightDto): Promise<InsightDocument> {
    const newInsight = new this.insightModel(createInsightDto);
    return newInsight.save();
  }

  async findAllInsights(
    userId?: string,
    companyId?: string,
    tag?: string,
  ): Promise<InsightDocument[]> {
    const query: any = {};

    if (userId) {
      query.userId = userId;
    }

    if (companyId) {
      query.companyId = companyId;
    }

    if (tag) {
      query.tag = tag;
    }

    return this.insightModel
      .find(query)
      .sort({ createdAt: -1 })
      .exec();
  }

  async findInsightById(id: string): Promise<InsightDocument> {
    const insight = await this.insightModel.findById(id).exec();
    if (!insight) {
      throw new NotFoundException(`Insight with ID ${id} not found`);
    }
    return insight;
  }

  async updateInsight(id: string, updateInsightDto: any): Promise<InsightDocument> {
    const updatedInsight = await this.insightModel
      .findByIdAndUpdate(id, updateInsightDto, { new: true })
      .exec();

    if (!updatedInsight) {
      throw new NotFoundException(`Insight with ID ${id} not found`);
    }

    return updatedInsight;
  }

  async removeInsight(id: string): Promise<InsightDocument> {
    const deletedInsight = await this.insightModel.findByIdAndDelete(id).exec();

    if (!deletedInsight) {
      throw new NotFoundException(`Insight with ID ${id} not found`);
    }

    return deletedInsight;
  }

  async analyzeInsight(createInsightDto: CreateInsightDto): Promise<InsightDocument> {
    // In a real implementation, this would call an AI service
    // For now, we'll just create a mock insight
    const mockSummary = `This is a generated insight analysis for "${createInsightDto.title}"

    Analysis:
    - Finding 1: Lorem ipsum dolor sit amet
    - Finding 2: Consectetur adipiscing elit
    - Finding 3: Sed do eiusmod tempor incididunt

    Recommendation: This is a placeholder insight that would normally be generated by an AI service.`;

    createInsightDto.summary = mockSummary;

    return this.createInsight(createInsightDto);
  }

  // AI Prompt methods
  async createAiPrompt(createAiPromptDto: CreateAiPromptDto): Promise<AiPromptDocument> {
    const newAiPrompt = new this.aiPromptModel(createAiPromptDto);
    return newAiPrompt.save();
  }

  async findAllAiPrompts(
    companyId?: string,
    isSystem?: boolean,
  ): Promise<AiPromptDocument[]> {
    const query: any = {};

    if (companyId) {
      query.companyId = companyId;
    }

    if (isSystem !== undefined) {
      query.isSystem = isSystem;
    }

    return this.aiPromptModel.find(query).exec();
  }

  async findAiPromptById(id: string): Promise<AiPromptDocument> {
    const aiPrompt = await this.aiPromptModel.findById(id).exec();
    if (!aiPrompt) {
      throw new NotFoundException(`AI Prompt with ID ${id} not found`);
    }
    return aiPrompt;
  }

  async updateAiPrompt(id: string, updateAiPromptDto: any): Promise<AiPromptDocument> {
    const updatedAiPrompt = await this.aiPromptModel
      .findByIdAndUpdate(id, updateAiPromptDto, { new: true })
      .exec();

    if (!updatedAiPrompt) {
      throw new NotFoundException(`AI Prompt with ID ${id} not found`);
    }

    return updatedAiPrompt;
  }

  async removeAiPrompt(id: string): Promise<AiPromptDocument> {
    const deletedAiPrompt = await this.aiPromptModel.findByIdAndDelete(id).exec();

    if (!deletedAiPrompt) {
      throw new NotFoundException(`AI Prompt with ID ${id} not found`);
    }

    return deletedAiPrompt;
  }

  // AI Chat methods
  async createAiChat(createAiChatDto: CreateAiChatDto): Promise<AiChatDocument> {
    const newAiChat = new this.aiChatModel(createAiChatDto);
    return newAiChat.save();
  }

  async findAllAiChats(userId: string): Promise<AiChatDocument[]> {
    return this.aiChatModel
      .find({ userId })
      .sort({ updatedAt: -1 })
      .exec();
  }

  async findAiChatById(id: string): Promise<AiChatDocument> {
    const aiChat = await this.aiChatModel.findById(id).exec();
    if (!aiChat) {
      throw new NotFoundException(`AI Chat with ID ${id} not found`);
    }
    return aiChat;
  }

  async updateAiChat(id: string, updateAiChatDto: any): Promise<AiChatDocument> {
    const updatedAiChat = await this.aiChatModel
      .findByIdAndUpdate(id, updateAiChatDto, { new: true })
      .exec();

    if (!updatedAiChat) {
      throw new NotFoundException(`AI Chat with ID ${id} not found`);
    }

    return updatedAiChat;
  }

  async removeAiChat(id: string): Promise<AiChatDocument> {
    const deletedAiChat = await this.aiChatModel.findByIdAndDelete(id).exec();

    if (!deletedAiChat) {
      throw new NotFoundException(`AI Chat with ID ${id} not found`);
    }

    return deletedAiChat;
  }

  async sendChatMessage(chatId: string, message: string, userId: string): Promise<AiChatDocument> {
    const chat = await this.aiChatModel.findById(chatId).exec();
    if (!chat) {
      throw new NotFoundException(`AI Chat with ID ${chatId} not found`);
    }

    // Check if the user owns this chat
    if (chat.userId.toString() !== userId) {
      throw new ForbiddenException('You do not have permission to access this chat');
    }

    // Add user message
    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date(),
    };

    // In a real implementation, this would call an AI service
    // For now, we'll just create a mock response
    const aiResponse: ChatMessage = {
      role: 'assistant',
      content: `This is a mock response to: "${message}"

      I'm an AI assistant that would normally provide a helpful response based on your message and the conversation context.`,
      timestamp: new Date(),
    };

    // Update the chat with the new messages
    chat.messages.push(userMessage, aiResponse);

    // If this is the first message, update the title
    if (chat.messages.length === 2) {
      chat.title = message.substring(0, 50) + (message.length > 50 ? '...' : '');
    }

    return chat.save();
  }

  // App Settings methods
  async createAppSettings(createAppSettingsDto: CreateAppSettingsDto): Promise<AppSettingsDocument> {
    const newAppSettings = new this.appSettingsModel(createAppSettingsDto);
    return newAppSettings.save();
  }

  async findAllAppSettings(
    type?: string,
    companyId?: string,
  ): Promise<AppSettingsDocument[]> {
    const query: any = {};

    if (type) {
      query.type = type;
    }

    if (companyId) {
      query.companyId = companyId;
    }

    return this.appSettingsModel.find(query).exec();
  }

  async findAppSettingsById(id: string): Promise<AppSettingsDocument> {
    const appSettings = await this.appSettingsModel.findById(id).exec();
    if (!appSettings) {
      throw new NotFoundException(`App Settings with ID ${id} not found`);
    }
    return appSettings;
  }

  async updateAppSettings(id: string, updateAppSettingsDto: any): Promise<AppSettingsDocument> {
    const updatedAppSettings = await this.appSettingsModel
      .findByIdAndUpdate(id, updateAppSettingsDto, { new: true })
      .exec();

    if (!updatedAppSettings) {
      throw new NotFoundException(`App Settings with ID ${id} not found`);
    }

    return updatedAppSettings;
  }

  async removeAppSettings(id: string): Promise<AppSettingsDocument> {
    const deletedAppSettings = await this.appSettingsModel.findByIdAndDelete(id).exec();

    if (!deletedAppSettings) {
      throw new NotFoundException(`App Settings with ID ${id} not found`);
    }

    return deletedAppSettings;
  }

  // Transcription method
  async transcribeAudio(transcribeAudioDto: TranscribeAudioDto): Promise<{ text: string }> {
    const { audioUrl, postTypeId } = transcribeAudioDto;

    // In a real implementation, you would call an STT service like OpenAI Whisper API
    // For now, we'll simulate a transcription with a placeholder

    let transcription = 'This is a simulated transcription of the audio recording.';

    // If a post type is provided, we can customize the transcription based on the post type
    if (postTypeId) {
      try {
        const postType = await this.postsService.findPostTypeById(postTypeId);
        if (postType) {
          // In a real implementation, you might use the post type to guide the AI transcription
          // For now, we'll just add a reference to the post type
          transcription += ` This is for the post type: ${postType.name}.`;
        }
      } catch (error) {
        // If post type not found, just continue with basic transcription
        console.error('Error finding post type:', error);
      }
    }

    return { text: transcription };
  }
}
