/**
 * Web implementation of expo-image-picker
 * This provides a browser-based implementation for web platforms
 */

// Define the interfaces to match the native ImagePicker API
export enum MediaTypeOptions {
  All = 'All',
  Images = 'Images',
  Videos = 'Videos',
}

export enum UIImagePickerControllerQualityType {
  High = 0,
  Medium = 1,
  Low = 2,
  Standard = 0,
  HEVCStandard = 0,
}

export interface ImagePickerOptions {
  mediaTypes?: MediaTypeOptions;
  allowsEditing?: boolean;
  aspect?: [number, number];
  quality?: number;
  base64?: boolean;
  exif?: boolean;
}

export interface ImagePickerResult {
  cancelled: boolean;
  uri?: string;
  width?: number;
  height?: number;
  type?: string;
  base64?: string;
  exif?: any;
}

// Create a file input element and handle the selection
const createFileInput = (options: ImagePickerOptions, accept: string): Promise<ImagePickerResult> => {
  return new Promise((resolve) => {
    // Create a file input element
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = accept;
    input.style.display = 'none';
    document.body.appendChild(input);

    // Handle cancellation
    const handleCancel = () => {
      document.body.removeChild(input);
      resolve({ cancelled: true });
      document.removeEventListener('focus', handleFocus);
    };

    // Handle focus back to window (user might have cancelled)
    const handleFocus = () => {
      setTimeout(() => {
        // If the input still has no files, assume cancelled
        if (input.files?.length === 0) {
          handleCancel();
        }
      }, 300);
    };

    // Handle file selection
    input.onchange = async () => {
      document.removeEventListener('focus', handleFocus);
      
      if (input.files && input.files.length > 0) {
        const file = input.files[0];
        const reader = new FileReader();
        
        reader.onload = (e) => {
          const uri = e.target?.result as string;
          
          // Create an image to get dimensions
          const img = new Image();
          img.onload = () => {
            document.body.removeChild(input);
            
            const result: ImagePickerResult = {
              cancelled: false,
              uri,
              width: img.width,
              height: img.height,
              type: file.type,
            };
            
            // Add base64 data if requested
            if (options.base64 && typeof uri === 'string' && uri.includes('base64')) {
              result.base64 = uri.split(',')[1];
            }
            
            resolve(result);
          };
          
          img.onerror = () => {
            document.body.removeChild(input);
            resolve({
              cancelled: false,
              uri,
              type: file.type,
            });
          };
          
          img.src = uri;
        };
        
        reader.onerror = () => {
          document.body.removeChild(input);
          resolve({ cancelled: true });
        };
        
        reader.readAsDataURL(file);
      } else {
        document.body.removeChild(input);
        resolve({ cancelled: true });
      }
    };

    // Listen for focus back to window (for cancel detection)
    document.addEventListener('focus', handleFocus);
    
    // Trigger the file selection dialog
    input.click();
  });
};

// Create the web implementation
const ExpoImagePicker = {
  // Request permissions (not needed on web)
  async requestMediaLibraryPermissionsAsync(): Promise<{ status: string; granted: boolean }> {
    return { status: 'granted', granted: true };
  },
  
  async requestCameraPermissionsAsync(): Promise<{ status: string; granted: boolean }> {
    return { status: 'granted', granted: true };
  },
  
  // Launch the image picker
  async launchImageLibraryAsync(options: ImagePickerOptions = {}): Promise<ImagePickerResult> {
    const mediaTypes = options.mediaTypes || MediaTypeOptions.Images;
    let accept = '';
    
    if (mediaTypes === MediaTypeOptions.Images) {
      accept = 'image/*';
    } else if (mediaTypes === MediaTypeOptions.Videos) {
      accept = 'video/*';
    } else {
      accept = 'image/*,video/*';
    }
    
    return createFileInput(options, accept);
  },
  
  // Launch the camera (not fully supported on web)
  async launchCameraAsync(options: ImagePickerOptions = {}): Promise<ImagePickerResult> {
    console.warn('Camera capture is not fully supported in web. Using file picker instead.');
    return this.launchImageLibraryAsync(options);
  },
  
  // Get permissions (always returns granted on web)
  async getMediaLibraryPermissionsAsync(): Promise<{ status: string; granted: boolean }> {
    return { status: 'granted', granted: true };
  },
  
  async getCameraPermissionsAsync(): Promise<{ status: string; granted: boolean }> {
    return { status: 'granted', granted: true };
  },
};

export default ExpoImagePicker;
