import { OnModuleInit } from '@nestjs/common';
import { Model } from 'mongoose';
import { CompanyDocument } from './schemas/company.schema';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { ModuleRef } from '@nestjs/core';
export declare class CompaniesService implements OnModuleInit {
    private companyModel;
    private moduleRef;
    private postsService;
    constructor(companyModel: Model<CompanyDocument>, moduleRef: ModuleRef);
    onModuleInit(): void;
    create(createCompanyDto: CreateCompanyDto): Promise<CompanyDocument>;
    createDefaultPostTypes(companyId: string, userId: string): Promise<void>;
    findAll(): Promise<CompanyDocument[]>;
    findById(id: string): Promise<CompanyDocument>;
    findByInviteCode(inviteCode: string): Promise<CompanyDocument | null>;
    update(id: string, updateCompanyDto: UpdateCompanyDto): Promise<CompanyDocument>;
    remove(id: string): Promise<CompanyDocument>;
    private generateInviteCode;
}
