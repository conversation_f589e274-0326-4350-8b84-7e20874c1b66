'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import AudioRecorder from './AudioRecorder';

interface Team {
  _id: string;
  name: string;
}

interface PostType {
  _id: string;
  name: string;
  recordTips?: string;
}

interface CreatePostFormProps {
  onPostCreated?: () => void;
}

export default function CreatePostForm({ onPostCreated }: CreatePostFormProps) {
  const { user } = useAuth();
  const [body, setBody] = useState('');
  const [teams, setTeams] = useState<Team[]>([]);
  const [postTypes, setPostTypes] = useState<PostType[]>([]);
  const [selectedTeam, setSelectedTeam] = useState<string>('');
  const [selectedPostType, setSelectedPostType] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [isDraft, setIsDraft] = useState(true);
  const [selectedPostTypeRecord, setSelectedPostTypeRecord] = useState<PostType | null>(null);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [hashtagInput, setHashtagInput] = useState('');

  useEffect(() => {
    if (user?.profile?.companyId) {
      fetchTeamsAndPostTypes(user.profile.companyId);
    }
  }, [user]);

  const fetchTeamsAndPostTypes = async (companyId: string) => {
    try {
      const [teamsRes, postTypesRes] = await Promise.all([
        api.get(`${endpoints.teams.base}?companyId=${companyId}`),
        api.get(`${endpoints.postTypes.base}?companyId=${companyId}`),
      ]);
      setTeams(teamsRes.data);
      setPostTypes(postTypesRes.data);
    } catch (err) {
      console.error('Failed to fetch teams and post types:', err);
    }
  };

  // Update selected post type record when post type changes
  useEffect(() => {
    if (selectedPostType) {
      const postType = postTypes.find(pt => pt._id === selectedPostType);
      setSelectedPostTypeRecord(postType || null);
    } else {
      setSelectedPostTypeRecord(null);
    }
  }, [selectedPostType, postTypes]);

  // Extract hashtags from body when it changes
  useEffect(() => {
    const extractHashtags = (text: string): string[] => {
      const hashtagRegex = /#(\w+)/g;
      const matches = text.match(hashtagRegex);

      if (!matches) {
        return [];
      }

      // Remove the # symbol and return unique hashtags
      return [...new Set(matches.map(tag => tag.substring(1).toLowerCase()))];
    };

    const extractedHashtags = extractHashtags(body);
    setHashtags(extractedHashtags);
  }, [body]);

  const handleAudioRecorded = (audioUrl: string, transcription: string) => {
    setAudioUrl(audioUrl);
    setBody(transcription);
  };

  const addHashtag = () => {
    if (!hashtagInput.trim()) return;

    const tag = hashtagInput.trim().replace(/^#/, '');
    if (!hashtags.includes(tag)) {
      setHashtags([...hashtags, tag]);
      // Add the hashtag to the body text
      setBody(prev => `${prev} #${tag}`);
    }
    setHashtagInput('');
  };

  const removeHashtag = (tag: string) => {
    setHashtags(hashtags.filter(t => t !== tag));
    // Remove the hashtag from the body text
    setBody(prev => prev.replace(new RegExp(`#${tag}\\b`, 'g'), ''));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!body.trim()) {
      setError('Post content cannot be empty');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      await api.post(endpoints.posts.base, {
        body,
        teamId: selectedTeam || undefined,
        postTypeId: selectedPostType || undefined,
        companyId: user?.profile?.companyId,
        userId: user?.id,
        isDraft,
        audioUrl: audioUrl || undefined,
        hashtags,
      });

      setBody('');
      setSelectedTeam('');
      setSelectedPostType('');
      setIsDraft(false);
      setAudioUrl('');
      setHashtags([]);
      setHashtagInput('');

      if (onPostCreated) {
        onPostCreated();
      }
    } catch (err: any) {
      console.error('Failed to create post:', err);
      setError(err.response?.data?.message || 'Failed to create post. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-semibold mb-4">Create Post</h2>

      {error && (
        <div className="bg-red-50 p-4 rounded-md mb-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="team" className="block text-sm font-medium text-gray-700">
                Team (optional)
              </label>
              <select
                id="team"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                value={selectedTeam}
                onChange={(e) => setSelectedTeam(e.target.value)}
              >
                <option value="">Select a team</option>
                {teams.map((team) => (
                  <option key={team._id} value={team._id}>
                    {team.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="postType" className="block text-sm font-medium text-gray-700">
                Post Type (optional)
              </label>
              <select
                id="postType"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                value={selectedPostType}
                onChange={(e) => setSelectedPostType(e.target.value)}
              >
                <option value="">Select a post type</option>
                {postTypes.map((type) => (
                  <option key={type._id} value={type._id}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Audio Recorder */}
          <AudioRecorder
            onAudioRecorded={handleAudioRecorded}
            postTypeId={selectedPostType}
            recordTips={selectedPostTypeRecord?.recordTips}
          />

          <div>
            <label htmlFor="body" className="block text-sm font-medium text-gray-700">
              {audioUrl ? 'Transcription (you can edit this)' : 'What\'s on your mind?'}
            </label>
            <textarea
              id="body"
              rows={4}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              placeholder="Share your thoughts, ideas, or updates..."
              value={body}
              onChange={(e) => setBody(e.target.value)}
            ></textarea>
          </div>

          <div>
            <label htmlFor="hashtags" className="block text-sm font-medium text-gray-700 mt-4">
              Hashtags
            </label>
            <div className="flex mt-1">
              <input
                id="hashtags"
                type="text"
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="Add a hashtag (without #)"
                value={hashtagInput}
                onChange={(e) => setHashtagInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addHashtag();
                  }
                }}
              />
              <button
                type="button"
                onClick={addHashtag}
                className="ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Add
              </button>
            </div>
            {hashtags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {hashtags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                  >
                    #{tag}
                    <button
                      type="button"
                      onClick={() => removeHashtag(tag)}
                      className="ml-1 inline-flex text-indigo-500 hover:text-indigo-700 focus:outline-none"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>


          <div className="flex items-center">
            <input
              id="isPublished"
              type="checkbox"
              className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              checked={!isDraft}
              onChange={(e) => setIsDraft(!e.target.checked)}
            />
            <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
              Publish immediately (otherwise saved as draft)
            </label>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:bg-indigo-400"
            >
              {isSubmitting ? 'Posting...' : isDraft ? 'Save Draft' : 'Post'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
