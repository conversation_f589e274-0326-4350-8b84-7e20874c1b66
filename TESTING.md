# Kodi Application Testing Guide

This document provides instructions for running tests for the Kodi application, which consists of a Next.js frontend and a NestJS backend.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Frontend Tests](#frontend-tests)
3. [Backend Tests](#backend-tests)
4. [End-to-End Tests](#end-to-end-tests)
5. [Visual Regression Tests](#visual-regression-tests)
6. [Performance Tests](#performance-tests)
7. [Accessibility Tests](#accessibility-tests)
8. [Monitoring](#monitoring)
9. [Continuous Integration](#continuous-integration)
10. [Test Coverage](#test-coverage)
11. [Troubleshooting](#troubleshooting)

## Prerequisites

Before running tests, ensure you have the following installed:

- Node.js (v18+)
- npm or yarn
- MongoDB (for backend tests)

## Frontend Tests

The frontend tests are located in the `kodi_next` directory and use Jest with React Testing Library.

### Installing Dependencies

```bash
cd kodi_next
npm install
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Structure

The frontend tests are organized as follows:

- **Unit Tests**: Test individual components and hooks
  - Located alongside the components they test with `.test.tsx` extension
  - Example: `src/components/Button.test.tsx`

- **Integration Tests**: Test interactions between components
  - Located in the same directory as the components they test
  - Example: `src/app/(main)/feed/page.test.tsx`

- **API Integration Tests**: Test API client functionality
  - Located in `src/lib/api.integration.test.ts`

### Key Components Tested

The following key components have tests:

1. **Auth Components**:
   - AuthContext - Tests authentication state management
   - Login page - Tests login form validation and submission

2. **Layout Components**:
   - Navbar - Tests navigation, user menu, and responsive behavior
   - ThemeToggle - Tests theme switching functionality
   - ThemeContext - Tests theme state management

3. **Post Components**:
   - Feed page - Tests post loading, filtering, and display
   - AudioRecorder - Tests audio recording and processing

4. **API Integration**:
   - API client - Tests request handling and authentication
   - Token refresh - Tests automatic token refresh flow

## Backend Tests

The backend tests are located in the `kodi_nest` directory and use Jest with Supertest for API testing.

### Installing Dependencies

```bash
cd kodi_nest
npm install
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run end-to-end tests
npm run test:e2e
```

### Test Structure

The backend tests are organized as follows:

- **Unit Tests**: Test individual services and controllers
  - Located alongside the files they test with `.spec.ts` extension
  - Example: `src/auth/auth.service.spec.ts`

- **End-to-End Tests**: Test API endpoints and flows
  - Located in the `test` directory with `.e2e-spec.ts` extension
  - Example: `test/auth.e2e-spec.ts`

### Key Services Tested

The following key services have tests:

1. **Authentication Services**:
   - AuthService - Tests user authentication and token management
   - AuthController - Tests authentication endpoints

2. **Core Data Services**:
   - UsersService - Tests user management operations
   - CompaniesService - Tests company creation and management
   - TeamsService - Tests team and membership operations
   - PostsService - Tests post creation, retrieval, and management

3. **Feature Services**:
   - AiService - Tests AI-powered features like summaries and chat
   - MessagingService - Tests conversation and message handling
   - UploadsService - Tests file upload and management

4. **End-to-End Flows**:
   - Authentication flow - Tests registration, login, and profile access
   - Posts flow - Tests creating, retrieving, and managing posts

## End-to-End Tests

End-to-end tests verify the entire application flow from frontend to backend. These tests are located in the `kodi_nest/test` directory.

### Running E2E Tests

```bash
# From the kodi_nest directory
npm run test:e2e
```

The E2E tests use an in-memory MongoDB instance, so you don't need to have MongoDB running locally.

## Visual Regression Tests

Visual regression tests ensure that UI components maintain their visual appearance over time. These tests are implemented using Storybook and Chromatic.

### Storybook

Storybook is used to develop and test UI components in isolation. Each component has a set of stories that showcase its different states and variations.

#### Running Storybook Locally

```bash
cd kodi_next
npm run storybook
```

This will start Storybook on [http://localhost:6006](http://localhost:6006).

#### Building Storybook

```bash
cd kodi_next
npm run build-storybook
```

This will generate a static Storybook build in the `kodi_next/storybook-static` directory.

### Chromatic

Chromatic is a visual testing tool that captures screenshots of Storybook stories and compares them against baseline images to detect visual changes.

#### Running Chromatic

```bash
cd kodi_next
npm run chromatic
```

This requires a Chromatic project token, which can be set as an environment variable:

```bash
export CHROMATIC_PROJECT_TOKEN=your-project-token
```

### Key Components with Visual Tests

The following components have visual regression tests:

1. **UI Components**:
   - Button - Tests different variants, sizes, and states
   - ThemeToggle - Tests light and dark mode appearance

2. **Layout Components**:
   - Navbar - Tests responsive behavior and theme variations

3. **Post Components**:
   - AudioRecorder - Tests recording interface and states
   - SamplePost - Tests content display in different themes

## Performance Tests

Performance tests ensure that the application meets performance requirements under various conditions. These tests are implemented using Lighthouse CI for the frontend and k6 for the backend.

### Frontend Performance Tests with Lighthouse CI

Lighthouse CI is used to measure and track the performance of the frontend application.

#### Running Lighthouse Tests

```bash
cd kodi_next
npm run lighthouse
```

You can also run specific device profiles:

```bash
# Run tests for mobile devices
npm run lighthouse:mobile

# Run tests for desktop devices
npm run lighthouse:desktop
```

### Backend Performance Tests with k6

k6 is used to load test the backend API endpoints.

#### Running k6 Tests

```bash
cd kodi_nest
npm run test:perf          # Run authentication performance tests
npm run test:perf:posts    # Run posts performance tests
npm run test:perf:all      # Run all performance tests
```

### Performance Test Scenarios

The following performance test scenarios are implemented:

1. **Frontend Performance**:
   - Page load performance for key pages (home, feed, login)
   - Core Web Vitals (LCP, FID, CLS)
   - Accessibility and best practices

2. **Backend Performance**:
   - Authentication flow under load
   - Post creation and retrieval under load
   - Feed performance with different filter combinations

### Performance Thresholds

The performance tests include the following thresholds:

- **Frontend**:
  - Performance score: ≥ 80
  - Accessibility score: ≥ 90
  - First Contentful Paint: < 2s
  - Largest Contentful Paint: < 2.5s
  - Cumulative Layout Shift: < 0.1

- **Backend**:
  - 95% of requests complete in < 500ms
  - Authentication endpoint handles 20 concurrent users
  - Post creation endpoint handles 10 concurrent users

## Accessibility Tests

Accessibility tests ensure that the application is usable by people with disabilities. These tests are implemented using axe-core and jest-axe.

### Running Accessibility Linting

```bash
cd kodi_next
npm run lint:a11y
```

This will run ESLint with the jsx-a11y plugin to check for accessibility issues in the code.

### Running Accessibility Tests

```bash
cd kodi_next
npm run test:a11y
```

This will run Jest tests with jest-axe to check for accessibility violations in the rendered components.

### Key Components with Accessibility Tests

The following components have accessibility tests:

1. **UI Components**:
   - Button - Tests different variants, states, and with icons
   - Navbar - Tests navigation, menus, and responsive behavior

2. **Form Components**:
   - Input fields - Tests labels, validation, and error states
   - Select dropdowns - Tests keyboard navigation and ARIA attributes

3. **Content Components**:
   - Post cards - Tests content structure and semantic HTML
   - Modal dialogs - Tests focus management and keyboard trapping

### Accessibility Standards

The accessibility tests check compliance with the following standards:

- WCAG 2.1 Level AA
- Section 508
- WAI-ARIA 1.2

## Monitoring

The application includes real-user monitoring to track performance, errors, and user behavior in production.

### Performance Monitoring

The monitoring system tracks the following performance metrics:

- Page load time
- Time to First Byte (TTFB)
- First Contentful Paint (FCP)
- Largest Contentful Paint (LCP)
- Cumulative Layout Shift (CLS)
- First Input Delay (FID)

### Error Tracking

The monitoring system tracks JavaScript errors and unhandled promise rejections, including:

- Error message
- Stack trace
- Source file and line number
- Browser and OS information
- Session information

### Custom Event Tracking

The application can track custom events to monitor user behavior:

```javascript
import { trackEvent } from '@/lib/monitoring';

// Track a custom event
trackEvent('button_click', { buttonId: 'submit', page: 'login' });
```

### Monitoring Endpoints

The monitoring data is sent to the following API endpoints:

- `/api/monitoring/metrics` - Performance metrics
- `/api/monitoring/errors` - Error reports
- `/api/monitoring/events` - Custom events

In a production environment, these endpoints would forward the data to a monitoring service like New Relic, Datadog, or Sentry.

## Continuous Integration

The project is set up with CI/CD pipelines that run tests automatically on each push and pull request. The pipeline includes:

1. Installing dependencies
2. Running linters (including accessibility linting)
3. Running frontend tests
4. Running backend tests
5. Running E2E tests
6. Running accessibility tests
7. Running visual regression tests with Chromatic
8. Running performance tests with Lighthouse CI and k6
9. Generating coverage reports

## Test Coverage

To view test coverage reports:

### Frontend

```bash
cd kodi_next
npm run test:coverage
```

The coverage report will be generated in the `kodi_next/coverage` directory.

### Backend

```bash
cd kodi_nest
npm run test:cov
```

The coverage report will be generated in the `kodi_nest/coverage` directory.

## Troubleshooting

### Common Issues

1. **Tests failing due to MongoDB connection**
   - Make sure MongoDB is running locally
   - For E2E tests, no MongoDB instance is needed as they use an in-memory database

2. **Tests timing out**
   - Increase the timeout in Jest configuration
   - Check for asynchronous operations that might not be resolving

3. **Authentication issues in tests**
   - Ensure the mock JWT tokens are properly set up
   - Check that the auth guards are properly mocked

4. **Frontend tests failing with "Error: Not implemented: navigation"**
   - Make sure you've properly mocked the Next.js router

### Getting Help

If you encounter issues not covered here, please:

1. Check the error logs for specific error messages
2. Review the test documentation for Jest, React Testing Library, or NestJS
3. Reach out to the development team for assistance
