import type { <PERSON>a, StoryObj } from '@storybook/react';
import PostItem from './PostItem';

const meta: Meta<typeof PostItem> = {
  title: 'Components/Posts/PostItem',
  component: PostItem,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="max-w-2xl mx-auto">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof PostItem>;

export const Default: Story = {
  args: {
    post: {
      _id: 'post-1',
      body: 'This is a test post with #hashtag',
      userId: 'user-1',
      createdAt: new Date().toISOString(),
      hashtags: ['hashtag'],
      user: {
        firstName: 'John',
        lastName: 'Doe',
      },
    },
    onTagClick: (tag) => console.log(`Tag clicked: ${tag}`),
  },
};

export const WithAvatar: Story = {
  args: {
    post: {
      _id: 'post-2',
      body: 'This is a post with an avatar',
      userId: 'user-2',
      createdAt: new Date().toISOString(),
      hashtags: ['avatar'],
      user: {
        firstName: '<PERSON>',
        lastName: 'Smith',
        avatar: 'https://i.pravatar.cc/300?u=jane',
      },
    },
    onTagClick: (tag) => console.log(`Tag clicked: ${tag}`),
  },
};

export const WithTeamAndType: Story = {
  args: {
    post: {
      _id: 'post-3',
      body: 'This post has team and type information',
      userId: 'user-3',
      createdAt: new Date().toISOString(),
      hashtags: ['team', 'type'],
      user: {
        firstName: 'Alex',
        lastName: 'Johnson',
      },
      teamId: 'team-1',
      team: {
        name: 'Engineering',
      },
      postTypeId: 'type-1',
      postType: {
        name: 'Strategic feedback',
      },
    },
    onTagClick: (tag) => console.log(`Tag clicked: ${tag}`),
  },
};

export const WithEnhancedBody: Story = {
  args: {
    post: {
      _id: 'post-4',
      body: 'This post has enhanced body content',
      enhancedBody: `
        <h3>Enhanced Content</h3>
        <p>This is a post with <strong>enhanced</strong> body content that includes:</p>
        <ul>
          <li>Formatted text</li>
          <li>Lists</li>
          <li>And other HTML elements</li>
        </ul>
      `,
      userId: 'user-4',
      createdAt: new Date().toISOString(),
      hashtags: ['enhanced', 'content'],
      user: {
        firstName: 'Sam',
        lastName: 'Taylor',
      },
    },
    onTagClick: (tag) => console.log(`Tag clicked: ${tag}`),
  },
};

export const WithAudio: Story = {
  args: {
    post: {
      _id: 'post-5',
      body: 'This post includes an audio recording',
      userId: 'user-5',
      createdAt: new Date().toISOString(),
      hashtags: ['audio', 'recording'],
      user: {
        firstName: 'Morgan',
        lastName: 'Lee',
      },
      audioUrl: 'https://actions.google.com/sounds/v1/alarms/digital_watch_alarm_long.ogg',
    },
    onTagClick: (tag) => console.log(`Tag clicked: ${tag}`),
  },
};

export const WithMultipleHashtags: Story = {
  args: {
    post: {
      _id: 'post-6',
      body: 'This post has multiple hashtags: #first #second #third',
      userId: 'user-6',
      createdAt: new Date().toISOString(),
      hashtags: ['first', 'second', 'third', 'multiple', 'hashtags'],
      user: {
        firstName: 'Taylor',
        lastName: 'Kim',
      },
    },
    onTagClick: (tag) => console.log(`Tag clicked: ${tag}`),
  },
};
