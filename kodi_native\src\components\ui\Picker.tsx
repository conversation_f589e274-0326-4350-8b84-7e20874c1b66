import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface PickerItem {
  _id: string;
  name: string;
}

interface PickerProps {
  items: PickerItem[];
  selectedValue: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  colors: any;
  icon?: string;
}

export default function Picker({
  items,
  selectedValue,
  onValueChange,
  placeholder = 'Select an option',
  colors,
  icon,
}: PickerProps) {
  const [modalVisible, setModalVisible] = useState(false);
  
  const selectedItem = items.find(item => item._id === selectedValue);
  const displayText = selectedItem ? selectedItem.name : placeholder;

  return (
    <>
      <TouchableOpacity
        style={[
          styles.pickerButton,
          { 
            borderColor: colors.border,
            backgroundColor: colors.card,
          }
        ]}
        onPress={() => setModalVisible(true)}
        accessibilityRole="button"
        accessibilityLabel={placeholder}
      >
        {icon && (
          <Ionicons 
            name={icon as any} 
            size={18} 
            color={colors.mutedForeground} 
            style={styles.icon} 
          />
        )}
        <Text 
          style={[
            styles.pickerText, 
            { 
              color: selectedItem ? colors.foreground : colors.mutedForeground 
            }
          ]}
        >
          {displayText}
        </Text>
        <Ionicons 
          name="chevron-down" 
          size={18} 
          color={colors.mutedForeground} 
        />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.foreground }]}>
              {placeholder}
            </Text>
            <TouchableOpacity
              onPress={() => setModalVisible(false)}
              accessibilityRole="button"
              accessibilityLabel="Close"
            >
              <Ionicons name="close" size={24} color={colors.foreground} />
            </TouchableOpacity>
          </View>

          <FlatList
            data={items}
            keyExtractor={(item) => item._id}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.item,
                  selectedValue === item._id && { backgroundColor: colors.secondary }
                ]}
                onPress={() => {
                  onValueChange(item._id);
                  setModalVisible(false);
                }}
                accessibilityRole="button"
                accessibilityLabel={item.name}
              >
                <Text style={[styles.itemText, { color: colors.foreground }]}>
                  {item.name}
                </Text>
                {selectedValue === item._id && (
                  <Ionicons name="checkmark" size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
            )}
          />
        </SafeAreaView>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    height: 48,
  },
  pickerText: {
    flex: 1,
    fontSize: 16,
  },
  icon: {
    marginRight: 8,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#ccc',
  },
  itemText: {
    fontSize: 16,
  },
});
