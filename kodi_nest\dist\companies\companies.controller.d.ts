import { CompaniesService } from './companies.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
export declare class CompaniesController {
    private readonly companiesService;
    constructor(companiesService: CompaniesService);
    create(createCompanyDto: CreateCompanyDto): Promise<import("./schemas/company.schema").CompanyDocument>;
    findAll(): Promise<import("./schemas/company.schema").CompanyDocument[]>;
    findByInviteCode(code: string): Promise<import("./schemas/company.schema").CompanyDocument | null>;
    findOne(id: string): Promise<import("./schemas/company.schema").CompanyDocument>;
    update(id: string, updateCompanyDto: UpdateCompanyDto): Promise<import("./schemas/company.schema").CompanyDocument>;
    remove(id: string): Promise<import("./schemas/company.schema").CompanyDocument>;
    setupPostTypes(id: string, body: {
        userId: string;
    }): Promise<void>;
}
