import { Test, TestingModule } from '@nestjs/testing';
import { PostsService } from './posts.service';
import { getModelToken } from '@nestjs/mongoose';
import { Post } from './schemas/post.schema';
import { PostType } from './schemas/post-type.schema';
import { Tag } from './schemas/tag.schema';
import { Comment } from './schemas/comment.schema';
import { ModuleRef } from '@nestjs/core';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { Model } from 'mongoose';

describe('PostsService', () => {
  let service: PostsService;
  let postModel: Model<Post>;
  let postTypeModel: Model<PostType>;
  let tagModel: Model<Tag>;
  let commentModel: Model<Comment>;

  const mockPostModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockPostTypeModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockTagModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
  };

  const mockCommentModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
    create: jest.fn(),
    new: jest.fn(),
    deleteMany: jest.fn(),
  };

  const mockModuleRef = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostsService,
        {
          provide: getModelToken(Post.name),
          useValue: mockPostModel,
        },
        {
          provide: getModelToken(PostType.name),
          useValue: mockPostTypeModel,
        },
        {
          provide: getModelToken(Tag.name),
          useValue: mockTagModel,
        },
        {
          provide: getModelToken(Comment.name),
          useValue: mockCommentModel,
        },
        {
          provide: ModuleRef,
          useValue: mockModuleRef,
        },
      ],
    }).compile();

    service = module.get<PostsService>(PostsService);
    postModel = module.get<Model<Post>>(getModelToken(Post.name));
    postTypeModel = module.get<Model<PostType>>(getModelToken(PostType.name));
    tagModel = module.get<Model<Tag>>(getModelToken(Tag.name));
    commentModel = module.get<Model<Comment>>(getModelToken(Comment.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAllPosts', () => {
    it('should return all posts with no filters', async () => {
      const mockPosts = [
        { _id: 'post1', body: 'Test post 1' },
        { _id: 'post2', body: 'Test post 2' },
      ];

      mockPostModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockPosts),
        }),
      });

      const result = await service.findAllPosts();

      expect(mockPostModel.find).toHaveBeenCalledWith({});
      expect(result).toEqual(mockPosts);
    });

    it('should apply filters when provided', async () => {
      const mockPosts = [{ _id: 'post1', body: 'Test post 1' }];

      mockPostModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockPosts),
        }),
      });

      const result = await service.findAllPosts('company1', 'team1', 'postType1', 'tag1', false);

      expect(mockPostModel.find).toHaveBeenCalledWith({
        companyId: 'company1',
        teamId: 'team1',
        postTypeId: 'postType1',
        hashtags: 'tag1',
        isDraft: false,
      });
      expect(result).toEqual(mockPosts);
    });
  });

  describe('findPostById', () => {
    it('should return a post when it exists', async () => {
      const mockPost = { _id: 'post1', body: 'Test post 1' };

      mockPostModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockPost),
      });

      const result = await service.findPostById('post1');

      expect(mockPostModel.findById).toHaveBeenCalledWith('post1');
      expect(result).toEqual(mockPost);
    });

    it('should throw NotFoundException when post does not exist', async () => {
      mockPostModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.findPostById('nonexistent')).rejects.toThrow(NotFoundException);
      expect(mockPostModel.findById).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('createPost', () => {
    it('should create a new post and extract hashtags', async () => {
      const createPostDto = {
        body: 'Test post with #hashtag1 and #hashtag2',
        userId: 'user1',
        companyId: 'company1',
      };

      const savedPost = {
        _id: 'post1',
        ...createPostDto,
        hashtags: ['hashtag1', 'hashtag2'],
      };

      // Mock the save method
      jest.spyOn(service, 'extractHashtags').mockReturnValue(['hashtag1', 'hashtag2']);
      jest.spyOn(service, 'updateTags').mockResolvedValue(undefined);
      
      // Mock the model constructor and save
      mockPostModel.new = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(savedPost),
      }));

      // Mock the model constructor
      jest.spyOn(postModel, 'create').mockResolvedValue(savedPost);

      const result = await service.createPost(createPostDto);

      expect(service.extractHashtags).toHaveBeenCalledWith(createPostDto.body);
      expect(service.updateTags).toHaveBeenCalledWith(['hashtag1', 'hashtag2'], 'company1');
      expect(result).toEqual(savedPost);
    });
  });

  describe('updatePost', () => {
    it('should update a post and extract new hashtags if body is updated', async () => {
      const updatePostDto = {
        body: 'Updated post with #newtag',
      };

      const existingPost = {
        _id: 'post1',
        body: 'Original post',
        companyId: 'company1',
      };

      const updatedPost = {
        _id: 'post1',
        body: 'Updated post with #newtag',
        hashtags: ['newtag'],
        companyId: 'company1',
      };

      mockPostModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingPost),
      });

      mockPostModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedPost),
      });

      jest.spyOn(service, 'extractHashtags').mockReturnValue(['newtag']);
      jest.spyOn(service, 'updateTags').mockResolvedValue(undefined);

      const result = await service.updatePost('post1', updatePostDto);

      expect(mockPostModel.findById).toHaveBeenCalledWith('post1');
      expect(service.extractHashtags).toHaveBeenCalledWith(updatePostDto.body);
      expect(service.updateTags).toHaveBeenCalledWith(['newtag'], 'company1');
      expect(mockPostModel.findByIdAndUpdate).toHaveBeenCalledWith('post1', {
        body: 'Updated post with #newtag',
        hashtags: ['newtag'],
      }, { new: true });
      expect(result).toEqual(updatedPost);
    });

    it('should throw NotFoundException when post does not exist', async () => {
      mockPostModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.updatePost('nonexistent', { title: 'Updated' })).rejects.toThrow(NotFoundException);
    });
  });

  describe('removePost', () => {
    it('should remove a post and its comments', async () => {
      const deletedPost = { _id: 'post1', body: 'Test post' };

      mockCommentModel.deleteMany.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 2 }),
      });

      mockPostModel.findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(deletedPost),
      });

      const result = await service.removePost('post1');

      expect(mockCommentModel.deleteMany).toHaveBeenCalledWith({
        linkedObjectId: 'post1',
        objectType: 'Post',
      });
      expect(mockPostModel.findByIdAndDelete).toHaveBeenCalledWith('post1');
      expect(result).toEqual(deletedPost);
    });

    it('should throw NotFoundException when post does not exist', async () => {
      mockCommentModel.deleteMany.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 0 }),
      });

      mockPostModel.findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.removePost('nonexistent')).rejects.toThrow(NotFoundException);
    });
  });
});
