import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AudioRecorder from './AudioRecorder';
import api from '@/lib/api';

// Mock the API
jest.mock('@/lib/api', () => ({
  post: jest.fn(),
}));

// Mock MediaRecorder
const mockMediaRecorder = {
  start: jest.fn(),
  stop: jest.fn(),
  ondataavailable: jest.fn(),
  onstop: jest.fn(),
};

// Mock getUserMedia
const mockGetUserMedia = jest.fn();
Object.defineProperty(global.navigator, 'mediaDevices', {
  value: {
    getUserMedia: mockGetUserMedia,
  },
  writable: true,
});

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-audio-url');

describe('AudioRecorder', () => {
  const mockOnAudioRecorded = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful media access
    mockGetUserMedia.mockResolvedValue({
      getTracks: () => [{ stop: jest.fn() }],
    });
    
    // Mock MediaRecorder
    global.MediaRecorder = jest.fn().mockImplementation(() => mockMediaRecorder);
  });

  it('renders the audio recorder', () => {
    render(<AudioRecorder onAudioRecorded={mockOnAudioRecorded} />);
    
    expect(screen.getByText(/Record Audio/i)).toBeInTheDocument();
  });

  it('shows recording tips when available', async () => {
    const recordTips = '<ul><li>Speak clearly</li><li>Be concise</li></ul>';
    
    render(
      <AudioRecorder 
        onAudioRecorded={mockOnAudioRecorded} 
        recordTips={recordTips}
      />
    );
    
    // Tips should be hidden initially
    expect(screen.getByText(/Show recording tips/i)).toBeInTheDocument();
    
    // Click to show tips
    const user = userEvent.setup();
    await user.click(screen.getByText(/Show recording tips/i));
    
    // Tips should be visible
    expect(screen.getByText(/Hide recording tips/i)).toBeInTheDocument();
    const tipsContainer = screen.getByText(/Speak clearly/i).closest('div');
    expect(tipsContainer).toBeInTheDocument();
    expect(tipsContainer).toHaveProperty('innerHTML', recordTips);
  });

  it('starts recording when the record button is clicked', async () => {
    render(<AudioRecorder onAudioRecorded={mockOnAudioRecorded} />);
    
    // Click the record button
    const user = userEvent.setup();
    await user.click(screen.getByText(/Record Audio/i));
    
    // Check that getUserMedia was called
    expect(mockGetUserMedia).toHaveBeenCalledWith({ audio: true });
    
    // Check that MediaRecorder was created and started
    expect(global.MediaRecorder).toHaveBeenCalled();
    expect(mockMediaRecorder.start).toHaveBeenCalled();
    
    // Check that the UI shows recording state
    expect(screen.getByText(/Stop Recording/i)).toBeInTheDocument();
    expect(screen.getByText(/00:00/i)).toBeInTheDocument();
  });

  it('stops recording when the stop button is clicked', async () => {
    render(<AudioRecorder onAudioRecorded={mockOnAudioRecorded} />);
    
    // Start recording
    const user = userEvent.setup();
    await user.click(screen.getByText(/Record Audio/i));
    
    // Stop recording
    await user.click(screen.getByText(/Stop Recording/i));
    
    // Check that MediaRecorder was stopped
    expect(mockMediaRecorder.stop).toHaveBeenCalled();
  });

  it('processes audio when "Use Recording" is clicked', async () => {
    // Mock API responses
    api.post.mockImplementation((url) => {
      if (url.includes('upload')) {
        return Promise.resolve({ data: { url: 'https://example.com/audio.webm' } });
      }
      if (url.includes('transcribe')) {
        return Promise.resolve({ data: { transcription: 'This is a test transcription' } });
      }
      return Promise.reject(new Error('Unknown URL'));
    });
    
    render(<AudioRecorder onAudioRecorded={mockOnAudioRecorded} />);
    
    // Start recording
    const user = userEvent.setup();
    await user.click(screen.getByText(/Record Audio/i));
    
    // Simulate recording completion
    const audioBlob = new Blob(['fake audio data'], { type: 'audio/webm' });
    act(() => {
      // Trigger ondataavailable
      if (mockMediaRecorder.ondataavailable) {
        mockMediaRecorder.ondataavailable({ data: audioBlob });
      }
      
      // Trigger onstop
      if (mockMediaRecorder.onstop) {
        mockMediaRecorder.onstop();
      }
    });
    
    // Wait for UI to update
    await waitFor(() => {
      expect(screen.getByText(/Use Recording/i)).toBeInTheDocument();
    });
    
    // Click "Use Recording"
    await user.click(screen.getByText(/Use Recording/i));
    
    // Check that API calls were made
    await waitFor(() => {
      expect(api.post).toHaveBeenCalledTimes(2);
      expect(api.post.mock.calls[0][0]).toContain('upload');
      expect(api.post.mock.calls[1][0]).toContain('transcribe');
    });
    
    // Check that onAudioRecorded was called with the right parameters
    expect(mockOnAudioRecorded).toHaveBeenCalledWith(
      'https://example.com/audio.webm',
      'This is a test transcription'
    );
  });

  it('handles errors when accessing microphone', async () => {
    // Mock failed media access
    mockGetUserMedia.mockRejectedValue(new Error('Permission denied'));
    
    render(<AudioRecorder onAudioRecorded={mockOnAudioRecorded} />);
    
    // Click the record button
    const user = userEvent.setup();
    await user.click(screen.getByText(/Record Audio/i));
    
    // Check that error message is displayed
    await waitFor(() => {
      expect(screen.getByText(/Could not access microphone/i)).toBeInTheDocument();
    });
  });
});

// Helper function for act
function act(callback: () => void) {
  callback();
}
