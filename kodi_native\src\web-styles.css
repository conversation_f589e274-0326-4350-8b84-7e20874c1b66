/**
 * Web-specific styles for the Kodi Native app
 */

/* Reset and base styles */
html, body, #root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* Make the app container fill the viewport */
#root {
  display: flex;
  flex-direction: column;
}

/* Responsive layout adjustments */
@media (min-width: 768px) {
  /* Tablet and desktop styles */
  .app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  /* Adjust navigation for larger screens */
  .tab-bar {
    padding: 10px 20px;
  }
}

@media (max-width: 767px) {
  /* Mobile styles */
  .app-container {
    width: 100%;
    padding: 0 10px;
  }
  
  /* Adjust navigation for smaller screens */
  .tab-bar {
    padding: 10px;
  }
}

/* Scrollbar styling for web */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Focus styles for accessibility */
:focus {
  outline: 2px solid #4630EB;
  outline-offset: 2px;
}

/* Button hover effects (web only) */
button:hover {
  opacity: 0.9;
  cursor: pointer;
}

/* Input focus styles */
input:focus, textarea:focus {
  border-color: #4630EB;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #121212;
    color: #ffffff;
  }
  
  ::-webkit-scrollbar-track {
    background: #2d2d2d;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #666;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #888;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white;
    color: black;
  }
}
