import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../../users/users.service';
export declare class UserContextMiddleware implements NestMiddleware {
    private readonly jwtService;
    private readonly usersService;
    constructor(jwtService: JwtService, usersService: UsersService);
    use(req: Request, res: Response, next: NextFunction): Promise<void>;
}
declare global {
    namespace Express {
        interface Request {
            userContext?: {
                userId: string;
                companyId: string;
                roles: string[];
            };
        }
    }
}
