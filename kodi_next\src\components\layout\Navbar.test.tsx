import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Navbar from './Navbar';

// Mock the useAuth hook
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn(() => ({
    user: {
      email: '<EMAIL>',
      profile: {
        firstName: 'Test',
        lastName: 'User',
        avatar: null,
      },
    },
    logout: jest.fn(),
  })),
}));

// Mock the usePathname hook
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(() => '/feed'),
}));

// Mock the ThemeToggle component
jest.mock('./ThemeToggle', () => {
  return function MockThemeToggle() {
    return <div data-testid="theme-toggle">Theme Toggle</div>;
  };
});

describe('Navbar', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the navbar with logo and navigation links', () => {
    render(<Navbar />);
    
    // Check for logo
    const logo = screen.getByAltText('Kodi');
    expect(logo).toBeInTheDocument();
    
    // Check for navigation links
    expect(screen.getByText('Feed')).toBeInTheDocument();
    expect(screen.getByText('Drafts')).toBeInTheDocument();
    expect(screen.getByText('Analysis')).toBeInTheDocument();
    
    // Check for POST button
    expect(screen.getByText('POST')).toBeInTheDocument();
  });

  it('highlights the active link based on current path', () => {
    // Mock the usePathname to return '/feed'
    require('next/navigation').usePathname.mockReturnValue('/feed');
    
    render(<Navbar />);
    
    // The Feed link should be highlighted
    const feedLink = screen.getByText('Feed').closest('a');
    expect(feedLink).toHaveClass('border-indigo-500');
    
    // Other links should not be highlighted
    const draftsLink = screen.getByText('Drafts').closest('a');
    expect(draftsLink).not.toHaveClass('border-indigo-500');
  });

  it('displays user information in the profile menu', async () => {
    render(<Navbar />);
    
    // Open the profile menu
    const user = userEvent.setup();
    await user.click(screen.getByRole('button', { name: /Open user menu/i }));
    
    // Check that user info is displayed
    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    
    // Check for profile menu items
    expect(screen.getByText('Your Profile')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Sign out')).toBeInTheDocument();
  });

  it('calls logout when sign out is clicked', async () => {
    const mockLogout = jest.fn();
    require('@/contexts/AuthContext').useAuth.mockReturnValue({
      user: {
        email: '<EMAIL>',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          avatar: null,
        },
      },
      logout: mockLogout,
    });
    
    render(<Navbar />);
    
    // Open the profile menu
    const user = userEvent.setup();
    await user.click(screen.getByRole('button', { name: /Open user menu/i }));
    
    // Click sign out
    await user.click(screen.getByText('Sign out'));
    
    // Check that logout was called
    expect(mockLogout).toHaveBeenCalled();
  });

  it('displays user initials when no avatar is available', () => {
    render(<Navbar />);
    
    // Check that user initials are displayed
    const initialsElement = screen.getByText('TU');
    expect(initialsElement).toBeInTheDocument();
  });

  it('includes the theme toggle component', () => {
    render(<Navbar />);
    
    // Check that theme toggle is included
    expect(screen.getByTestId('theme-toggle')).toBeInTheDocument();
  });

  it('renders mobile menu with the same links', async () => {
    // Mock window.innerWidth to simulate mobile view
    global.innerWidth = 500;
    global.dispatchEvent(new Event('resize'));
    
    render(<Navbar />);
    
    // Check for mobile menu button
    const menuButton = screen.getByRole('button', { name: /Open main menu/i });
    expect(menuButton).toBeInTheDocument();
    
    // Open mobile menu
    const user = userEvent.setup();
    await user.click(menuButton);
    
    // Check for navigation links in mobile menu
    expect(screen.getAllByText('Feed')[1]).toBeInTheDocument();
    expect(screen.getAllByText('Drafts')[1]).toBeInTheDocument();
    expect(screen.getAllByText('Analysis')[1]).toBeInTheDocument();
  });
});
