// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Determine if we're targeting web
const isWeb = process.env.EXPO_TARGET === 'web';

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Ensure we don't process binary files
config.resolver.assetExts = [...config.resolver.assetExts, 'bin'];

// Enable symlinks
config.resolver.symlinks = true;

// Add .cjs extension for CommonJS modules
config.resolver.sourceExts = isWeb
  ? ['web.tsx', 'web.ts', 'web.jsx', 'web.js', 'tsx', 'ts', 'jsx', 'js', 'cjs', 'json', 'wasm']
  : ['tsx', 'ts', 'jsx', 'js', 'cjs', 'json', 'wasm'];

// Add extraNodeModules to allow overriding node_modules packages
config.resolver.extraNodeModules = {
  ...(isWeb ? { 'expo-secure-store': path.resolve(__dirname, 'src/expo-secure-store.web.ts') } : {})
};

module.exports = config;
