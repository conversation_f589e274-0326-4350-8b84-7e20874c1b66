import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type SummaryDocument = Summary & Document;

@Schema({
  collection: 'kd_summaries',
  timestamps: true,
})
export class Summary {
  @Prop({ required: false })
  tag: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Team', required: false, index: true })
  teamId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'PostType', required: false, index: true })
  postTypeId: MongooseSchema.Types.ObjectId;

  @Prop({ required: false, index: true })
  timeRange: string;

  @Prop({ required: false })
  userPrompt: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'AiPrompt', required: false })
  promptId: MongooseSchema.Types.ObjectId;

  @Prop({ required: false })
  summary: string;

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: 'Post' }] })
  postIds: MongooseSchema.Types.ObjectId[];

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true, index: true })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Company', required: false, index: true })
  companyId: MongooseSchema.Types.ObjectId;

  @Prop({ type: Date, required: false })
  lastUpdated: Date;

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: 'Comment' }] })
  comments: MongooseSchema.Types.ObjectId[];
}

export const SummarySchema = SchemaFactory.createForClass(Summary);
