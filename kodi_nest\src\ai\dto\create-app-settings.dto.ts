import { IsNotEmpty, <PERSON>S<PERSON>, <PERSON><PERSON><PERSON>al, <PERSON>Array } from 'class-validator';

export class CreateAppSettingsDto {
  @IsNotEmpty()
  @IsString()
  type: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsArray()
  settings?: Array<{
    key: string;
    value: any;
    type: string;
  }>;

  @IsOptional()
  @IsString()
  companyId?: string;

  @IsNotEmpty()
  @IsString()
  userId: string;
}
