# Kodi Application Architecture

## Overview
The Kodi application is a modern reimplementation of a legacy Meteor application, designed to provide a platform for team collaboration, content sharing, and AI-powered analysis. The system consists of:

- Next.js frontend (React)
- NestJS backend (Node.js)
- MongoDB database (with 'kd_' prefixed collections)
- AWS S3 for file storage

## Communication Flow
1. Client makes requests to Next.js frontend
2. Next.js handles UI rendering and client-side state
3. API calls are made to NestJS backend
4. NestJS handles business logic and database operations
5. Data flows back to the frontend for display

## Authentication
- JWT-based authentication
- Refresh token rotation
- Role-based access control

## Frontend Architecture (Next.js)

The frontend is built using Next.js, a React framework that provides server-side rendering, static site generation, and API routes.

### Key Components

1. **Pages**
   - Organized in the `app` directory using the App Router
   - Authentication pages in `(auth)` route group
   - Main application pages in `(main)` route group

2. **Components**
   - Reusable UI components in `components` directory
   - Layout components for consistent UI structure
   - Feature-specific components (posts, teams, etc.)

3. **Context Providers**
   - `AuthContext` for authentication state management
   - Other context providers for global state management

4. **API Integration**
   - Axios-based API client in `lib/api.ts`
   - Centralized endpoint definitions
   - Token management and refresh logic

5. **Styling**
   - Tailwind CSS for utility-first styling
   - Geist font for typography

## Backend Architecture (NestJS)

The backend is built using NestJS, a progressive Node.js framework for building efficient and scalable server-side applications.

### Key Components

1. **Modules**
   - Feature-based modules (users, posts, teams, etc.)
   - Each module contains controllers, services, DTOs, and schemas

2. **Controllers**
   - Handle HTTP requests and define API endpoints
   - Apply route guards for authentication and authorization

3. **Services**
   - Implement business logic
   - Interact with the database through Mongoose models

4. **Schemas**
   - MongoDB schemas defined using Mongoose
   - All collections prefixed with 'kd_' to segregate data

5. **Authentication**
   - JWT-based authentication
   - Role-based access control
   - Token refresh mechanism

6. **File Storage**
   - AWS S3 integration for file uploads
   - Presigned URLs for secure direct uploads

## Database Design

The application uses MongoDB as its database, with the following collections:

1. **User Management**
   - `kd_users` - User accounts and authentication
   - `kd_profiles` - User profile information

2. **Organization**
   - `kd_companies` - Company information
   - `kd_teams` - Teams within companies
   - `kd_memberships` - Team memberships

3. **Content**
   - `kd_posts` - User posts
   - `kd_post_types` - Post categories
   - `kd_tags` - Hashtags and tags
   - `kd_comments` - Comments on posts and other content

4. **AI Features**
   - `kd_summaries` - AI-generated summaries
   - `kd_insights` - AI-generated insights
   - `kd_ai_prompts` - Templates for AI generation
   - `kd_ai_chats` - Chat conversations with AI

5. **System**
   - `kd_file_uploads` - Uploaded files metadata
   - `kd_app_settings` - Application settings