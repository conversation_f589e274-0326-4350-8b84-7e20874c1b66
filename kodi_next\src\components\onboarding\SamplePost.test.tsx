import React from 'react';
import { render, screen } from '@testing-library/react';
import SamplePost from './SamplePost';

describe('SamplePost', () => {
  it('renders the sample post with company name', () => {
    render(<SamplePost companyName="Test Company" />);
    
    // Check for welcome message
    expect(screen.getByText('Welcome to Kodi!')).toBeInTheDocument();
    
    // Check for company name in the content
    expect(screen.getByText(/As an admin of Test Company, you can also:/i)).toBeInTheDocument();
    
    // Check for hashtags
    expect(screen.getByText('#welcome')).toBeInTheDocument();
    expect(screen.getByText('#gettingStarted')).toBeInTheDocument();
    expect(screen.getByText('#kodi')).toBeInTheDocument();
  });

  it('renders with a long company name', () => {
    const longCompanyName = 'Super Long Company Name That Might Wrap Around The Screen Incorporated';
    render(<SamplePost companyName={longCompanyName} />);
    
    // Check that the long company name is displayed correctly
    expect(screen.getByText(`As an admin of ${longCompanyName}, you can also:`)).toBeInTheDocument();
  });

  it('displays the current date', () => {
    // Mock Date.now to return a fixed date
    const originalDate = Date;
    const mockDate = new Date('2023-01-01T12:00:00Z');
    global.Date = class extends Date {
      constructor() {
        super();
        return mockDate;
      }
      static now() {
        return mockDate.getTime();
      }
    } as unknown as DateConstructor;

    render(<SamplePost companyName="Test Company" />);
    
    // Check that the date is displayed in the expected format
    // Note: This depends on the locale of the test environment
    expect(screen.getByText('1/1/2023')).toBeInTheDocument();
    
    // Restore original Date
    global.Date = originalDate;
  });

  it('includes all the expected content sections', () => {
    render(<SamplePost companyName="Test Company" />);
    
    // Check for the main sections
    expect(screen.getByText("This is a sample post to help you get started with Kodi. Here's what you can do:")).toBeInTheDocument();
    expect(screen.getByText("As an admin of Test Company, you can also:")).toBeInTheDocument();
    expect(screen.getByText("We hope you enjoy using Kodi for your team's voice-based communication!")).toBeInTheDocument();
    
    // Check for list items about what users can do
    expect(screen.getByText(/Create posts by clicking the POST button/i)).toBeInTheDocument();
    expect(screen.getByText(/Record audio and have it automatically transcribed/i)).toBeInTheDocument();
    expect(screen.getByText(/Use hashtags to categorize your posts/i)).toBeInTheDocument();
    expect(screen.getByText(/Filter posts by team, type, or tag/i)).toBeInTheDocument();
    expect(screen.getByText(/Generate AI-powered analysis/i)).toBeInTheDocument();
    
    // Check for list items about what admins can do
    expect(screen.getByText(/Invite team members using your company's invite code/i)).toBeInTheDocument();
    expect(screen.getByText(/Create and manage teams/i)).toBeInTheDocument();
    expect(screen.getByText(/Define custom post types/i)).toBeInTheDocument();
    expect(screen.getByText(/Configure AI prompts/i)).toBeInTheDocument();
  });
});
