'use client';

import React, { useState, useRef } from 'react';
import api, { endpoints } from '@/lib/api';

interface AudioInputFieldProps {
  onAudioProcessed: (text: string) => void;
  placeholder?: string;
  label?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  id?: string;
}

export default function AudioInputField({
  onAudioProcessed,
  placeholder = 'Enter text or record audio...',
  label,
  value,
  onChange,
  className = '',
  inputClassName = '',
  labelClassName = '',
  id,
}: AudioInputFieldProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const [recordingTime, setRecordingTime] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const inputId = id || `audio-input-${Math.random().toString(36).substring(2, 9)}`;

  const startRecording = async () => {
    try {
      setError('');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        setAudioBlob(audioBlob);

        // Stop all tracks to release the microphone
        stream.getTracks().forEach(track => track.stop());

        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
      };

      mediaRecorder.start();
      setIsRecording(true);

      // Start timer
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } catch (err) {
      console.error('Error accessing microphone:', err);
      setError('Could not access microphone. Please check your browser permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const processAudio = async () => {
    if (!audioBlob) return;

    setIsProcessing(true);
    setError('');

    try {
      // First, upload the audio file
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.webm');

      const uploadResponse = await api.post(endpoints.uploads.audioUpload, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const audioUrl = uploadResponse.data.url;

      // Then, transcribe the audio
      const transcribeResponse = await api.post(endpoints.ai.transcribe, {
        audioUrl,
      });

      const transcription = transcribeResponse.data.text;

      // Pass the transcription back to the parent component
      onAudioProcessed(transcription);

      // Reset state
      setAudioBlob(null);
    } catch (err: any) {
      console.error('Error processing audio:', err);
      setError(err.response?.data?.message || 'Failed to process audio. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const cancelRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
    setAudioBlob(null);
  };

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label htmlFor={inputId} className={`block text-sm font-medium text-gray-700 mb-1 ${labelClassName}`}>
          {label}
        </label>
      )}
      
      <div className="relative flex items-center">
        <input
          type="text"
          id={inputId}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 pr-10 ${inputClassName}`}
          disabled={isRecording || isProcessing}
        />
        
        {!isRecording && !audioBlob && !isProcessing && (
          <button
            type="button"
            onClick={startRecording}
            className="absolute right-2 text-gray-400 hover:text-gray-600"
            title="Record audio"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>

      {error && (
        <div className="mt-1 text-sm text-red-600">
          {error}
        </div>
      )}

      {isRecording && (
        <div className="mt-2 flex items-center space-x-2">
          <div className="animate-pulse h-2 w-2 bg-red-600 rounded-full"></div>
          <span className="text-sm text-red-600">Recording... {formatTime(recordingTime)}</span>
          <button
            type="button"
            onClick={stopRecording}
            className="ml-2 px-2 py-1 text-xs bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          >
            Stop
          </button>
        </div>
      )}

      {audioBlob && !isProcessing && (
        <div className="mt-2">
          <audio controls src={URL.createObjectURL(audioBlob)} className="w-full h-8" />
          <div className="mt-1 flex space-x-2">
            <button
              type="button"
              onClick={processAudio}
              className="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
            >
              Use Recording
            </button>
            <button
              type="button"
              onClick={cancelRecording}
              className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {isProcessing && (
        <div className="mt-2 flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-indigo-500 mr-2"></div>
          <span className="text-sm text-gray-600">Processing audio...</span>
        </div>
      )}
    </div>
  );
}
