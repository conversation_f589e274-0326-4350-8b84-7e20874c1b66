import api, { endpoints } from './api';
import { server } from '@/test-utils/mocks/server';
import { rest } from 'msw';

describe('API Integration', () => {
  beforeAll(() => server.listen());
  afterEach(() => {
    server.resetHandlers();
    localStorage.clear();
    jest.clearAllMocks();
  });
  afterAll(() => server.close());

  it('should make successful API calls with authentication', async () => {
    // Set up mock token in localStorage
    localStorage.setItem('access_token', 'mock-token');

    // Set up mock response
    server.use(
      rest.get('*/api/posts/feed', (req, res, ctx) => {
        // Check if the request has the authorization header
        const authHeader = req.headers.get('Authorization');
        
        if (authHeader === 'Bearer mock-token') {
          return res(
            ctx.json([
              {
                id: 'post-1',
                body: 'Test post 1',
                userId: 'user-1',
              },
            ])
          );
        } else {
          return res(ctx.status(401), ctx.json({ message: 'Unauthorized' }));
        }
      })
    );

    // Make the API call
    const response = await api.get(endpoints.posts.feed);

    // Check the response
    expect(response.data).toEqual([
      {
        id: 'post-1',
        body: 'Test post 1',
        userId: 'user-1',
      },
    ]);
  });

  it('should handle API errors gracefully', async () => {
    // Set up mock error response
    server.use(
      rest.get('*/api/posts/feed', (req, res, ctx) => {
        return res(
          ctx.status(500),
          ctx.json({ message: 'Internal server error' })
        );
      })
    );

    // Make the API call and expect it to fail
    await expect(api.get(endpoints.posts.feed)).rejects.toThrow();
  });

  it('should handle authentication errors and redirect to login', async () => {
    // Mock window.location.href
    const originalLocation = window.location;
    delete window.location;
    window.location = { href: '' } as Location;

    // Set up mock unauthorized response
    server.use(
      rest.get('*/api/posts/feed', (req, res, ctx) => {
        return res(
          ctx.status(401),
          ctx.json({ message: 'Unauthorized' })
        );
      }),
      rest.post('*/api/auth/refresh', (req, res, ctx) => {
        return res(
          ctx.status(401),
          ctx.json({ message: 'Invalid refresh token' })
        );
      })
    );

    // Set up expired token and refresh token
    localStorage.setItem('access_token', 'expired-token');
    localStorage.setItem('refresh_token', 'expired-refresh-token');

    // Make the API call and expect it to fail
    try {
      await api.get(endpoints.posts.feed);
    } catch (error) {
      // This should fail
    }

    // Check that localStorage items were removed
    expect(localStorage.getItem('access_token')).toBeNull();
    expect(localStorage.getItem('refresh_token')).toBeNull();
    expect(localStorage.getItem('user')).toBeNull();

    // Check that we were redirected to login
    expect(window.location.href).toBe('/login');

    // Restore original location
    window.location = originalLocation;
  });

  it('should refresh the token when it expires', async () => {
    // Mock window.location.href
    const originalLocation = window.location;
    delete window.location;
    window.location = { href: '' } as Location;

    // Set up mock responses
    server.use(
      // First request fails with 401
      rest.get('*/api/posts/feed', (req, res, ctx) => {
        const authHeader = req.headers.get('Authorization');
        
        if (authHeader === 'Bearer expired-token') {
          return res(
            ctx.status(401),
            ctx.json({ message: 'Token expired' })
          );
        } else if (authHeader === 'Bearer new-token') {
          return res(
            ctx.json([
              {
                id: 'post-1',
                body: 'Test post with refreshed token',
                userId: 'user-1',
              },
            ])
          );
        }
        
        return res(ctx.status(401));
      }),
      
      // Refresh token request succeeds
      rest.post('*/api/auth/refresh', (req, res, ctx) => {
        return res(
          ctx.json({
            access_token: 'new-token',
            refresh_token: 'new-refresh-token',
            user: {
              id: 'user-1',
              email: '<EMAIL>',
            },
          })
        );
      })
    );

    // Set up expired token and valid refresh token
    localStorage.setItem('access_token', 'expired-token');
    localStorage.setItem('refresh_token', 'valid-refresh-token');

    // Make the API call
    const response = await api.get(endpoints.posts.feed);

    // Check that the tokens were updated
    expect(localStorage.getItem('access_token')).toBe('new-token');
    expect(localStorage.getItem('refresh_token')).toBe('new-refresh-token');

    // Check the response with the new token
    expect(response.data).toEqual([
      {
        id: 'post-1',
        body: 'Test post with refreshed token',
        userId: 'user-1',
      },
    ]);

    // Restore original location
    window.location = originalLocation;
  });
});
