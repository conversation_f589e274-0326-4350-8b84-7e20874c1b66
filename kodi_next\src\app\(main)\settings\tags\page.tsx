'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import Link from 'next/link';

interface Tag {
  _id: string;
  name: string;
  count: number;
  companyId: string;
}

export default function TagsSettingsPage() {
  const { user } = useAuth();
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [newTagName, setNewTagName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    if (user?.profile?.companyId) {
      fetchTags(user.profile.companyId);
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchTags = async (companyId: string) => {
    try {
      setLoading(true);
      const { data } = await api.get(`${endpoints.tags.base}?companyId=${companyId}`);
      setTags(data);
    } catch (err: any) {
      console.error('Failed to fetch tags:', err);
      setError('Failed to load tags. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTag = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newTagName.trim() || !user?.profile?.companyId) return;
    
    try {
      setIsCreating(true);
      setError('');
      
      const { data } = await api.post(endpoints.tags.base, {
        name: newTagName,
        companyId: user.profile.companyId,
        count: 0
      });
      
      setTags([...tags, data]);
      setNewTagName('');
      setSuccessMessage('Tag created successfully');
      
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (err: any) {
      console.error('Failed to create tag:', err);
      setError(err.response?.data?.message || 'Failed to create tag. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteTag = async (tagId: string) => {
    if (!confirm('Are you sure you want to delete this tag?')) return;
    
    try {
      await api.delete(endpoints.tags.byId(tagId));
      setTags(tags.filter(tag => tag._id !== tagId));
      setSuccessMessage('Tag deleted successfully');
      
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (err: any) {
      console.error('Failed to delete tag:', err);
      setError(err.response?.data?.message || 'Failed to delete tag. Please try again.');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-5xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Tags</h1>
          <Link 
            href="/settings"
            className="text-sm text-indigo-600 hover:text-indigo-800"
          >
            Back to Settings
          </Link>
        </div>

        {error && (
          <div className="bg-red-50 p-4 rounded-md mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {successMessage && (
          <div className="bg-green-50 p-4 rounded-md mb-6">
            <p className="text-green-700">{successMessage}</p>
          </div>
        )}

        {user?.roles?.includes('admin') && (
          <div className="bg-white shadow overflow-hidden sm:rounded-md mb-6 p-4">
            <h2 className="text-lg font-medium mb-4">Create New Tag</h2>
            <form onSubmit={handleCreateTag} className="flex items-center">
              <input
                type="text"
                value={newTagName}
                onChange={(e) => setNewTagName(e.target.value)}
                placeholder="Enter tag name"
                className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              <button
                type="submit"
                disabled={isCreating || !newTagName.trim()}
                className="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-300"
              >
                {isCreating ? 'Creating...' : 'Create Tag'}
              </button>
            </form>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {tags.length > 0 ? (
                tags.map((tag) => (
                  <li key={tag._id}>
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-900">#{tag.name}</span>
                          <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            {tag.count} posts
                          </span>
                        </div>
                        {user?.roles?.includes('admin') && (
                          <button
                            onClick={() => handleDeleteTag(tag._id)}
                            className="text-sm text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        )}
                      </div>
                    </div>
                  </li>
                ))
              ) : (
                <li className="px-4 py-5 sm:px-6">
                  <div className="text-center text-gray-500">
                    No tags found
                  </div>
                </li>
              )}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
