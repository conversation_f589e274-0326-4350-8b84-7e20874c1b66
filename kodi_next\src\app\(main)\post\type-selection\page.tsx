'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import Link from 'next/link';

interface PostType {
  _id: string;
  name: string;
  description?: string;
  recordTips?: string;
  companyId: string;
}

interface Team {
  _id: string;
  name: string;
  companyId: string;
}

export default function PostTypeSelectionPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [postTypes, setPostTypes] = useState<PostType[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [selectedTeam, setSelectedTeam] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (user?.profile?.companyId) {
      fetchPostTypes(user.profile.companyId);
      fetchTeams(user.profile.companyId);
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchPostTypes = async (companyId: string) => {
    try {
      // The server will now enforce the company ID restriction based on the user's JWT token
      const { data } = await api.get(endpoints.postTypes.base);
      setPostTypes(data);
    } catch (err: any) {
      console.error('Failed to fetch post types:', err);
      setError('Failed to load post types. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchTeams = async (companyId: string) => {
    try {
      // The server will now enforce the company ID restriction based on the user's JWT token
      const { data } = await api.get(endpoints.teams.base);
      setTeams(data);
    } catch (err: any) {
      console.error('Failed to fetch teams:', err);
      // Don't set error here to avoid blocking the UI if only teams fail to load
    }
  };

  const handlePostTypeSelect = (postTypeId: string) => {
    // Navigate to pre-record page with the selected post type and team
    router.push(`/post/pre-record?postTypeId=${postTypeId}${selectedTeam ? `&teamId=${selectedTeam}` : ''}`);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Create a Post</h1>
          <Link
            href="/feed"
            className="text-sm text-indigo-600 hover:text-indigo-800"
          >
            Cancel
          </Link>
        </div>

        <div className="mb-8">
          <h2 className="text-lg font-medium mb-4">Select a Team (Optional)</h2>
          <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
            {teams.map((team) => (
              <button
                key={team._id}
                onClick={() => setSelectedTeam(selectedTeam === team._id ? '' : team._id)}
                className={`p-4 rounded-lg border text-left transition-colors ${
                  selectedTeam === team._id
                    ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                    : 'border-gray-200 hover:border-indigo-200 hover:bg-indigo-50'
                }`}
              >
                <span className="block font-medium">{team.name}</span>
              </button>
            ))}
          </div>
        </div>

        <h2 className="text-lg font-medium mb-4">Select a Post Type</h2>

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 p-4 rounded-md">
            <p className="text-red-700">{error}</p>
            <button
              onClick={() => user?.profile?.companyId && fetchPostTypes(user.profile.companyId)}
              className="mt-2 text-sm text-red-700 underline"
            >
              Try again
            </button>
          </div>
        ) : (
          <div className="grid gap-4">
            {postTypes.length > 0 ? (
              postTypes.map((postType) => (
                <button
                  key={postType._id}
                  onClick={() => handlePostTypeSelect(postType._id)}
                  className="p-4 rounded-lg border border-gray-200 text-left hover:border-indigo-200 hover:bg-indigo-50 transition-colors"
                >
                  <span className="block font-medium text-lg mb-1">{postType.name}</span>
                  {postType.description && (
                    <div className="text-sm text-gray-500" dangerouslySetInnerHTML={{ __html: postType.description }} />
                  )}
                </button>
              ))
            ) : (
              <div className="text-center p-8 bg-gray-50 rounded-lg">
                <p className="text-gray-500">No post types found. Please contact your administrator.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
