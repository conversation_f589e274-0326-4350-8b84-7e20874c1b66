import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import Card from '@/src/components/ui/Card';
import { Post } from '@/src/types';

interface PostItemProps {
  post: Post;
  onTagPress?: (tag: string) => void;
  isDraft?: boolean;
  onPublish?: (postId: string) => void;
  onDelete?: (postId: string) => void;
}

export default function PostItem({ post, onTagPress, isDraft, onPublish, onDelete }: PostItemProps) {
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Function to handle audio playback
  const handlePlayAudio = async () => {
    try {
      if (sound !== null) {
        // If we already have a sound object
        if (isPlaying) {
          await sound.pauseAsync();
          setIsPlaying(false);
        } else {
          await sound.playAsync();
          setIsPlaying(true);
        }
      } else {
        // Load the audio if we haven't already
        setIsLoading(true);
        const { sound: newSound } = await Audio.Sound.createAsync(
          { uri: post.audioUrl as string },
          { shouldPlay: true },
          onPlaybackStatusUpdate
        );
        setSound(newSound);
        setIsPlaying(true);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error playing audio:', error);
      setIsLoading(false);
    }
  };

  // Handle audio playback status updates
  const onPlaybackStatusUpdate = (status: any) => {
    if (status.didJustFinish) {
      setIsPlaying(false);
    }
  };

  // Clean up sound when component unmounts
  React.useEffect(() => {
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [sound]);

  // Get user initials for avatar
  const getUserInitials = () => {
    const firstName = post.user?.firstName || '';
    const lastName = post.user?.lastName || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || 'U';
  };

  return (
    <Card style={styles.postCard}>
      <View style={styles.postHeader}>
        <View style={styles.postUser}>
          <View style={[styles.avatar, { backgroundColor: colors.muted }]}>
            {post.user?.avatar ? (
              <Image
                source={{ uri: post.user.avatar }}
                style={styles.avatarImage}
                accessibilityLabel={`${post.user.firstName} ${post.user.lastName}'s avatar`}
              />
            ) : (
              <Text style={[styles.avatarText, { color: colors.mutedForeground }]}>
                {getUserInitials()}
              </Text>
            )}
          </View>
          <View>
            <Text style={[styles.userName, { color: colors.foreground }]}>
              {post.user?.firstName} {post.user?.lastName}
            </Text>
            <Text style={[styles.postMeta, { color: colors.mutedForeground }]}>
              {new Date(post.createdAt).toLocaleDateString()}
              {post.team && ` • ${post.team.name}`}
              {post.postType && ` • ${post.postType.name}`}
            </Text>
          </View>
        </View>
      </View>

      <Text style={[styles.postBody, { color: colors.foreground }]}>
        {post.enhancedBody || post.body}
      </Text>

      {post.hashtags && post.hashtags.length > 0 && (
        <View style={styles.tagsContainer}>
          {post.hashtags.map((tag) => (
            <TouchableOpacity
              key={tag}
              style={[styles.tag, { backgroundColor: colors.secondary }]}
              onPress={() => onTagPress?.(tag)}
              accessibilityRole="button"
              accessibilityLabel={`Hashtag ${tag}`}
              accessibilityHint="Filter posts by this tag"
            >
              <Text style={[styles.tagText, { color: colors.secondaryForeground }]}>
                #{tag}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {post.audioUrl && (
        <TouchableOpacity
          style={[styles.audioContainer, { backgroundColor: colors.secondary }]}
          onPress={handlePlayAudio}
          disabled={isLoading}
          accessibilityRole="button"
          accessibilityLabel="Play audio recording"
          accessibilityHint="Plays or pauses the audio recording for this post"
        >
          {isLoading ? (
            <>
              <Ionicons name="hourglass" size={20} color={colors.secondaryForeground} />
              <Text style={[styles.audioText, { color: colors.secondaryForeground }]}>
                Loading audio...
              </Text>
            </>
          ) : (
            <>
              <Ionicons
                name={isPlaying ? "pause" : "play"}
                size={20}
                color={colors.secondaryForeground}
              />
              <Text style={[styles.audioText, { color: colors.secondaryForeground }]}>
                {isPlaying ? "Pause audio" : "Play audio recording"}
              </Text>
            </>
          )}
        </TouchableOpacity>
      )}

      {isDraft && onPublish && onDelete && (
        <View style={styles.draftActions}>
          <TouchableOpacity
            style={[styles.draftButton, { backgroundColor: colors.primary }]}
            onPress={() => onPublish(post._id)}
            accessibilityRole="button"
            accessibilityLabel="Publish draft"
            accessibilityHint="Publishes this draft post"
          >
            <Ionicons name="send" size={16} color={colors.primaryForeground} style={styles.buttonIcon} />
            <Text style={[styles.buttonText, { color: colors.primaryForeground }]}>
              Publish
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.draftButton, { backgroundColor: colors.destructive }]}
            onPress={() => onDelete(post._id)}
            accessibilityRole="button"
            accessibilityLabel="Delete draft"
            accessibilityHint="Deletes this draft post"
          >
            <Ionicons name="trash" size={16} color="white" style={styles.buttonIcon} />
            <Text style={[styles.buttonText, { color: 'white' }]}>
              Delete
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  postCard: {
    marginBottom: 16,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  postUser: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    overflow: 'hidden',
  },
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  avatarText: {
    fontSize: 16,
    fontWeight: '600',
  },
  userName: {
    fontWeight: '600',
    fontSize: 16,
  },
  postMeta: {
    fontSize: 12,
  },
  postBody: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 12,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  audioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  audioText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  draftActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  draftButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 8,
  },
  buttonIcon: {
    marginRight: 4,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
