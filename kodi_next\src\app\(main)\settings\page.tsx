'use client';

import React from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

const settingsSections = [
  { id: 'account', name: 'Account Settings', href: '/settings/account', description: 'Manage your personal account settings' },
  { id: 'people', name: 'People', href: '/settings/people', description: 'Manage users and permissions' },
  { id: 'teams', name: 'Teams', href: '/settings/teams', description: 'Create and manage teams' },
  { id: 'tags', name: 'Tags', href: '/settings/tags', description: 'Manage post tags and categories' },
  { id: 'post-types', name: 'Post Types', href: '/settings/post-types', description: 'Configure post types and templates' },
  { id: 'prompts', name: 'Prompts', href: '/settings/prompts', description: 'Manage AI prompts and templates' },
  { id: 'company', name: 'Company', href: '/settings/company', description: 'Manage company information and settings' },
  { id: 'appearance', name: 'Appearance', href: '/settings/appearance', description: 'Customize the look and feel of the application' },
];

export default function SettingsPage() {
  const { user } = useAuth();

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="max-w-5xl mx-auto">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Settings</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {settingsSections.map((section) => (
            <Link
              key={section.id}
              href={section.href}
              className="block bg-white rounded-md border border-border p-4 hover:bg-gray-50 transition-colors"
            >
              <h2 className="text-xl font-semibold text-gray-900">{section.name}</h2>
              <p className="mt-2 text-gray-600">{section.description}</p>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
