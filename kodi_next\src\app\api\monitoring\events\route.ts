import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const eventData = await request.json();
    
    // In a real implementation, you would:
    // 1. Validate the event data
    // 2. Store the event in a database or send to an analytics service
    // 3. Potentially trigger actions based on specific events
    
    // For now, we'll just log the event in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Custom event received:', eventData);
    }
    
    // In production, you might send this to a service like:
    // - Google Analytics
    // - Mixpanel
    // - Amplitude
    // - Custom analytics backend
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing event:', error);
    return NextResponse.json({ error: 'Failed to process event' }, { status: 500 });
  }
}

// Disable body size limit for this route
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '500kb',
    },
  },
};
