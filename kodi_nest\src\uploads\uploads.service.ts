import { Injectable } from '@nestjs/common';
import { S3 } from 'aws-sdk';
import { v4 as uuid } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class UploadsService {
  private s3: S3;

  constructor() {
    this.s3 = new S3({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || process.env.AWS_DEFAULT_REGION,
    });

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir);
    }

    // Create audio uploads directory if it doesn't exist
    const audioUploadsDir = path.join(uploadsDir, 'audio');
    if (!fs.existsSync(audioUploadsDir)) {
      fs.mkdirSync(audioUploadsDir);
    }
  }

  async generatePresignedUrl(fileType: string, isPrivate: boolean = false) {
    const bucket = isPrivate
      ? process.env.AWS_PRIVATE_BUCKET
      : process.env.AWS_BUCKET;

    const key = `${uuid()}-${Date.now()}`;

    const params = {
      Bucket: bucket,
      Key: key,
      ContentType: fileType,
      Expires: 300, // URL expires in 5 minutes
    };

    const uploadUrl = await this.s3.getSignedUrlPromise('putObject', params);

    return {
      uploadUrl,
      key,
      url: `https://${bucket}.s3.amazonaws.com/${key}`,
    };
  }

  handleFileUpload(file: Express.Multer.File, type: string) {
    const baseUrl = process.env.API_URL || 'http://localhost:3011';
    return {
      url: `${baseUrl}/uploads/${type}/${file.filename}`,
      filename: file.filename,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
    };
  }
}