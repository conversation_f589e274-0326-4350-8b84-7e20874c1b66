'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';

interface Draft {
  _id: string;
  body: string;
  createdAt: string;
  updatedAt: string;
  teamId?: string;
  team?: {
    name: string;
  };
  postTypeId?: string;
  postType?: {
    name: string;
  };
}

export default function DraftsPage() {
  const { user } = useAuth();
  const [drafts, setDrafts] = useState<Draft[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (user) {
      fetchDrafts();
    }
  }, [user]);

  const fetchDrafts = async () => {
    try {
      setLoading(true);
      const { data } = await api.get(`${endpoints.posts.drafts}?userId=${user?.id}`);
      setDrafts(data);
    } catch (err: any) {
      console.error('Failed to fetch drafts:', err);
      setError('Failed to load drafts. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const publishDraft = async (draftId: string) => {
    try {
      await api.patch(`${endpoints.posts.byId(draftId)}`, {
        isDraft: false,
      });
      fetchDrafts();
    } catch (err: any) {
      console.error('Failed to publish draft:', err);
      setError('Failed to publish draft. Please try again later.');
    }
  };

  const deleteDraft = async (draftId: string) => {
    try {
      await api.delete(endpoints.posts.byId(draftId));
      fetchDrafts();
    } catch (err: any) {
      console.error('Failed to delete draft:', err);
      setError('Failed to delete draft. Please try again later.');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Your Drafts</h1>
      
      {error && (
        <div className="bg-red-50 p-4 rounded-md mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}
      
      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      ) : drafts.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <p className="text-gray-500">You don't have any drafts yet.</p>
        </div>
      ) : (
        <div className="space-y-6">
          {drafts.map((draft) => (
            <div key={draft._id} className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <p className="text-sm text-gray-500">
                    Last updated: {new Date(draft.updatedAt).toLocaleDateString()}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    {draft.team && (
                      <span className="text-sm text-gray-500">
                        Team: {draft.team.name}
                      </span>
                    )}
                    {draft.postType && (
                      <span className="text-sm text-gray-500">
                        Type: {draft.postType.name}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => publishDraft(draft._id)}
                    className="px-3 py-1 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  >
                    Publish
                  </button>
                  <button
                    onClick={() => deleteDraft(draft._id)}
                    className="px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                  >
                    Delete
                  </button>
                </div>
              </div>
              
              <div className="prose max-w-none">
                <p>{draft.body}</p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
